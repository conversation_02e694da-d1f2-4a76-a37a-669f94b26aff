@extends('layouts.admin',['noGreyBg' => 'no-grey-bg'])
{{-- @section('breadcrumbs', Breadcrumbs::render('edit-profile')) --}}
@section('content')
    @include('partials.studentbannertabs')
    @push('styles')
        <style>
            .pb-8 {
                padding-bottom: 2.5rem;
            }

        </style>
    @endpush
    @if (session()->has('message'))
        <div class="alert alert-success text-center">
            <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a> {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card card-transparent">
                <div class="card-header text-center">
                    <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                    {{-- <div class="card-title custom-card-title">Update Profile</div> --}}
                    <p class="uppercase bold oswald">Your Account</p>
                </div>
                <div class="card-block">
                    <form id="form-personal" method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                        {{ csrf_field() }}
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>First Name</label>
                                    <input type="text" class="form-control" name="firstname" required="" value="{{ $user->profile->firstname }}" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Last Name</label>
                                    <input type="text" class="form-control" name="lastname" required="" value="{{ $user->profile->lastname }}" aria-required="true">
                                </div>
                            </div>
                        </div>
                        <div class="row clearfix">
                            <div class="col-md-6">
                                <div class="form-group form-group-default required" aria-required="true">
                                    <label>Email</label>
                                    <input type="email" class="form-control" name="email" value="{{ $user->email }}" required="" aria-required="true">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group form-group-default" aria-required="true">
                                    <label>Password</label>
                                    <input type="password" class="form-control" name="password" placeholder="Enter only if you want to change" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        @if (Auth::user()->school_id)
                            <div class="row clearfix">
                                <div class="@if (Auth::user()->school->campuses->isNotEmpty()) col-md-6 @else col-md-12 @endif">
                                    <div class="form-group form-group-default required disabled">
                                        <label>School</label>
                                        <input type="text" class="form-control" value="{{ $user->school->name }}" disabled>
                                    </div>
                                </div>
                                @if (Auth::user()->school->campuses->isNotEmpty())
                                    <div class="col-md-6">
                                        {{-- <div class="form-group form-group-default form-group-default-select2 required">
                                <label>Year</label>
                                <select class="form-control" name="year" data-init-plugin="select2" data-placeholder="Select..">
                                    <option value="">Select..</option>
                                    @foreach ($years as $key => $year)
                                    @if ($year->title != 'I’ve finished high school')
                                    <option value="{{$year->id}}" @if ($year->id == $user->profile->standard_id) selected @endif>{{$year->title}}</option>
                                    @endif
                                    @endforeach
                                </select>
                            </div> --}}
                                        <div class="form-group form-group-default form-group-default-select2 required @if (Auth::user()->campuses->isNotEmpty())disabled @endif" aria-required="true">
                                            <label>School Campus</label>
                                            <select name="campuses" class="form-control" data-init-plugin="select2" data-placement="Select.." @if (Auth::user()->campuses->isNotEmpty())disabled @endif>
                                                <option selected disabled></option>
                                                @foreach (Auth::user()->school->campuses as $campus)
                                                    <option @if ($user->campuses->isNotEmpty() && $user->campuses->first()->id == $campus->id) selected="selected" @endif value="{{ $campus->id }}">{{ $campus->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            {{-- @if (Auth::user()->school->campuses->isNotEmpty() && Auth::user()->profile->standard_id != 7)
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                                <label>School Campus</label>
                                <select name="campuses" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                    <option selected disabled></option>
                                    @foreach (Auth::user()->school->campuses as $campus)
                                    <option @if ($user->campuses->isNotEmpty() && $user->campuses->first()->id == $campus->id) selected="selected" @endif value="{{$campus->id}}">{{$campus->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    @endif --}}
                        @endif
                        @if (Auth::user()->organisation_id)
                            <div class="row clearfix">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default required disabled">
                                        <label>Organisation</label>
                                        <input type="text" class="form-control" value="{{ $user->organisation->name }}" disabled>
                                    </div>
                                </div>
                                @if (Auth::user()->organisation->campuses->isNotEmpty())
                                    <div class="col-md-6">
                                        <div class="form-group form-group-default form-group-default-select2 required disabled" aria-required="true">
                                            <label>Organisation Campus</label>
                                            <select name="orgcampuses" class="form-control" data-init-plugin="select2" data-placement="Select.." disabled>
                                                <option selected disabled></option>
                                                @foreach (Auth::user()->organisation->campuses as $campus)
                                                    <option @if ($user->orgCampuses->isNotEmpty() && $user->orgCampuses->first()->id == $campus->id) selected="selected" @endif value="{{ $campus->id }}">{{ $campus->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endif
                        @if (Auth::user()->role->name == 'Individual Student' || Auth::user()->role->name == 'Student')
                            <div class="row">
                                <div class="col-md-6" id="stage-col">
                                    <div class="form-group form-group-default form-group-default-select2 required">
                                        <label>Stage of life</label>
                                        <select class="form-control full-width" id="stage" name="stage" data-init-plugin="select2" data-placeholder="Select..">
                                            <option value="">Select...</option>
                                            <option value="school" @if ($user->profile->standard_id != 7) selected @endif>I'm still in high school</option>
                                            <option value="done" @if ($user->profile->standard_id == 7) selected @endif>I've finished high school</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 d-none" id="what-stage">
                                    {{-- <div class="form-group form-group-default form-group-default-select2 required">
                                <label>What stage of life are you at?</label>
                                <select class="form-control full-width" name="stage_id" data-init-plugin="select2" data-placeholder="Select..">
                                    <option value="">Select..</option>
                                    @foreach ($stages as $stage)
                                    <option value="{{$stage->id}}" @if ($stage->id == $user->profile->stage_id) selected
                                        @endif>{{$stage->title}}
                                    </option>
                                    @endforeach
                                </select>
                            </div> --}}
                                    <div class="form-group form-group-default form-group-default-select2 required d-none">
                                        <label>Graduated Year</label>
                                        <select class="full-width" name="graduate_year" data-init-plugin="select2" data-placeholder="Select..">
                                            <option value="" selected></option>
                                            @for ($i = 2018; $i <= date('Y'); $i++)
                                                <option @if ($user->profile->graduate_year == $i) selected @endif>{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row d-none" id="school-detail">
                                <div class="col-md-6">
                                    <div class="form-group form-group-default form-group-default-select2 required">
                                        <label>Year</label>
                                        <select class="form-control full-width" name="school_year" data-init-plugin="select2" data-placeholder="Select..">
                                            <option value="">Select..</option>
                                            @foreach ($years as $year)
                                                <option value="{{ $year->id }}" @if ($year->id == $user->profile->standard_id) selected
                                            @endif>{{ $year->title }}</option>
                        @endforeach
                        </select>
                </div>
            </div>
            @if (!Auth::user()->school_id)
                <div class="col-md-6">
                    <div class="form-group form-group-default required">
                        <label>What School do you go to?</label>
                        <input id="school-name" type="text" placeholder="Enter school name" class="form-control" value="{{ $user->profile->school }}" name="school_name" required>
                    </div>
                </div>
            @endif
        </div>
        @endif
        <div class="row">
            <div class="col-md-12">
                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                    <label>Country</label>
                    <select name="country" class="form-control" data-init-plugin="select2" data-placement="Select..">
                        <option selected disabled></option>
                        @foreach ($countries as $country)
                            <option @if ($user->state && $country->id == $user->state->country->id) selected="selected" @endif value="{{ $country->id }}">{{ $country->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <div class="row clearfix">
            <div class="col-md-6">
                <div class="form-group form-group-default form-group-default-select2 required" aria-required="true">
                    <label>State</label>
                    <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                        <option selected disabled></option>
                        @if ($user->state)
                            @foreach ($user->state->country->states as $state)
                                <option @if ($state->id == $user->state_id) selected="selected" @endif value="{{ $state->id }}">{{ $state->name }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group form-group-default required" aria-required="true">
                    <label>Postcode</label>
                    <input type="text" class="form-control" name="postcode" required="" value="{{ $user->postcode }}" aria-required="true">
                </div>
            </div>
        </div>
        <div class="row clearfix">
            <div class="col-md-6">
                <div class="form-group form-group-default form-group-default-select2">
                    <label>Gender</label>
                    <select class="full-width" id="gender" name="gender" data-init-plugin="select2" data-placeholder="Select..">
                        <option value=""></option>
                        <option value="M" @if($user->profile->gender == 'M') selected @endif>Male</option>
                        <option value="F" @if($user->profile->gender == 'F') selected @endif>Female</option>
                        <option value="O" @if($user->profile->gender && $user->profile->gender != 'M' && $user->profile->gender != 'F') selected @endif>Other / Prefer not to say</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
        <button class="btn btn-black" type="submit">Update</button>
        </form>
    </div>
    </div>
    <hr>
    </div>
    </div>
    {{-- @if (config('app.env') == 'local') --}}
    <div class="row">
        <div class="col-md-8 mx-auto ">
            <div class="card card-transparent">
                <div class="card-header">
                    <p class="uppercase bold pb-8 text-center">Invite your parents / Guardians</p>
                    <p class="sub-title">Invite your parents to join The Careers Department. Once your parent has created their account and are connected to you, they'll be able to see your career plans and the activities you complete along the way.</p>
                </div>
                <div class="card-block">
                    @forelse ($user->parents as $parent)
                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">Parent {{-- {{$loop->iteration}} --}}</div>
                            </div>
                            <div class="card-block">
                                <div class="row clearfix">
                                    <div class="col-md-6">
                                        <div class="form-group form-group-default disabled">
                                            <label>First Name</label>
                                            <input type="text" class="form-control" value="{{ $parent->profile->firstname }}" disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group form-group-default disabled">
                                            <label>Last Name</label>
                                            <input type="text" class="form-control" value="{{ $parent->profile->lastname }}" disabled>
                                        </div>
                                    </div>
                                </div>
                                <div class="row clearfix">
                                    <div class="col-md-8">
                                        <div class="form-group form-group-default disabled">
                                            <label>Email</label>
                                            <input type="email" class="form-control" value="{{ $parent->email }}" disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h4 class="text-blue uppercase oswald no-margin lh-normal text-right">Added <i class="fa fa-check"></i></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                    @endforelse
                    <form id="formParentInvitation" action="{{ route('parentinvitees.store') }}" method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                        @csrf
                        @forelse ($invitees as $invitee)
                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">Invitation </div>
                                    <div class="card-controls">
                                        <a href="{{ route('parentinvitees.destroy', $invitee->id) }}" class="uppercase fs-10" data-toggle="tooltip" title="Cancel invitation" data-method="delete" data-token="{{ csrf_token() }}" data-confirm="Are you sure?">Remove <i class="fa fa-trash-o text-danger fs-15"></i></a>
                                    </div>
                                </div>
                                <div class="card-block">
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default">
                                                <label>Email</label>
                                                <div class="form-control">{{ $invitee->email }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <h4 class="text-blue uppercase oswald no-margin lh-normal text-right">Waiting <i class="fa fa-circle-o"></i></h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">Invitation </div>
                                </div>
                                <div class="card-block">
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default required" aria-required="true">
                                                <label>Email</label>
                                                <input type="email" name="email[1]" class="form-control">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row clearfix">
                                        <div class="col-md-12">
                                            <div class="form-group form-group-default required" aria-required="true">
                                                <label>Relationship to you</label>
                                                <input type="text" class="form-control" name="relation[1]">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforelse
                        <div class="m-b-10 clearfix text-right">
                            <button class="btn btn-primary btn-custom-sm" type="button" id="btnAddparent">
                                <i class="fa fa-plus" aria-hidden="true"></i> Invite Another Parent
                            </button>
                        </div>
                        <button id="btnSendAway" class="btn btn-primary {{ $invitees->isNotEmpty() ? 'd-none' : '' }}" type="submit">Send away</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {{-- @endif --}}
    @push('scripts')
        <script>
            $(function() {
                if ($('#stage').val() == 'school') {
                    $('#school-detail').removeClass('d-none');
                    $('#what-stage').addClass('d-none');
                    $('#stage-col').addClass('col-md-12');
                    $('#stage-col').removeClass('col-md-6');
                    $('#what-stage select').val('').trigger("change.select2");
                } else {
                    $('#stage-col').addClass('col-md-6');
                    $('#stage-col').removeClass('col-md-12');
                    $('#what-stage').removeClass('d-none');
                    $('#school-detail').addClass('d-none');
                    $('#school-detail select, #school-detail input').val('').trigger("change.select2");
                }
                $('#stage').change(function() {
                    if ($(this).val() == 'school') {
                        $('#stage-col').addClass('col-md-12');
                        $('#stage-col').removeClass('col-md-6');
                        $('#school-detail').removeClass('d-none');
                        $('#what-stage').addClass('d-none');
                        $('#what-stage select').val('').trigger("change.select2");
                    } else {
                        $('#stage-col').addClass('col-md-6');
                        $('#stage-col').removeClass('col-md-12');
                        $('#what-stage').removeClass('d-none');
                        $('#school-detail').addClass('d-none');
                        $('#school-detail select, #school-detail input').val('').trigger("change.select2");
                    }
                });
            });
            jQuery(document).ready(function() {
                jQuery("select[name=country]").change(function() {
                    jQuery.ajax({
                        type: "get",
                        url: "{{ route('getStates') }}",
                        data: {
                            id: jQuery(this).val(),
                        },
                        success: function(response) {
                            jQuery("select[name=state]").html('<option selected disabled></option>');
                            jQuery.each(response, function(i, v) {
                                jQuery("select[name=state]").append('<option value="' + v.id + '">' + v.name + '</option>')
                            });
                        },
                        fail: function() {
                            jQuery("select[name=state]").html('<option selected disabled></option>');
                        }
                    });
                });

                jQuery(document).on('click', '#removeImg', function() {
                    var answer = confirm('Are you sure you want to remove this?');
                    if (answer) {
                        jQuery('#imgCol').remove();
                        jQuery('#inputCol').removeClass('col-md-8').addClass('col-md-12')
                        jQuery('#current-avatar').val('');
                    } else {}
                });
                jQuery(document).on('click', '.remove', function() {
                    jQuery(this).closest('.card').remove();
                    num--;
                    if (num == 1 && {{ $invitees->count() }}) {
                        jQuery('#btnSendAway').addClass('d-none');
                    }

                });

                var num = 1;
                @if ($invitees->isNotEmpty())
                    num = {{ $invitees->count() }};
                @endif

                jQuery(document).on('click', '#btnAddparent', function() {
                    num++;
                    jQuery(this).parent().before('<div class="card"> <div class="card-header"> <div class="card-title">Invitation </div> <div class="card-controls"> <i role="button" class="fa fa-times text-danger remove"></i></div></div> <div class="card-block"> <div class="row clearfix"> <div class="col-md-12"> <div class="form-group form-group-default required" aria-required="true"> <label>Email</label> <input type="email" name="email[' + num + ']" class="form-control"> </div> </div> </div> <div class="row clearfix"> <div class="col-md-12"> <div class="form-group form-group-default required" aria-required="true"> <label>Relationship to you</label> <input type="text" class="form-control" name="relation[' + num + ']"> </div> </div> </div> </div> </div>');

                    jQuery('#btnSendAway').removeClass('d-none');

                    $('input[name="relation[' + num + ']"]').rules("add", {
                        required: true,
                    });

                    $('input[name="email[' + num + ']"]').rules("add", {
                        required: true,
                        email: true,
                        notEqualTo: ['input[type=email]'],
                        messages: {
                            notEqualTo: "Email already entered!",
                        }
                    });
                });

                jQuery.validator.addMethod("notEqualTo", function(value, element, options) {
                    // get all the elements passed here with the same class
                    var elems = $(element).parents('form').find(options[0]);
                    // the value of the current element
                    var valueToCompare = value;
                    // count
                    var matchesFound = 0;
                    // loop each element and compare its value with the current value
                    // and increase the count every time we find one
                    jQuery.each(elems, function() {
                        thisVal = $(this).val();
                        if (thisVal == valueToCompare) {
                            matchesFound++;
                        }
                    });
                    // count should be either 0 or 1 max
                    if (this.optional(element) || matchesFound <= 1) {
                        //elems.removeClass('error');
                        return true;
                    } else {
                        //elems.addClass('error');
                    }
                }, "")
                jQuery('#formParentInvitation').validate({
                    errorElement: 'span',
                    errorClass: 'help-block error-help-block',

                    errorPlacement: function(error, element) {
                        if (element.parent('.input-group').length ||
                            element.prop('type') === 'checkbox' || element.prop('type') === 'radio') {
                            error.insertAfter(element.parent());
                            // else just place the validation message immediatly after the input
                        } else {
                            error.insertAfter(element);
                        }
                    },
                    highlight: function(element) {
                        $(element).closest('.form-group').removeClass('has-success').addClass('has-error'); // add the Bootstrap error class to the control group
                    },
                    rules: {
                        'email[1]': {
                            required: true,
                            email: true,
                            notEqualTo: ['input[type=email]'],
                        },
                        'relation[1]': 'required',
                    },
                    messages: {
                        'email[1]': {
                            notEqualTo: "Email already entered!",
                        },

                    }
                })

                // jQuery(document).on('change','#gender', function() {
                // jQuery('#gender').on('change', function() {
                //     if (this.value == 'Other') {
                //         jQuery("#edit_other_gender").removeClass('d-none');
                //     } else {
                //         jQuery("#edit_other_gender").addClass('d-none');
                //         jQuery('#form-personal .other_gender').val('');
                //     }
                // });
                jQuery('#form-personal').validate({
                    rules: {
                        firstname: 'required',
                        lastname: 'required',
                        fullname: 'required',
                        email: {
                            required: true,
                            email: true,
                            remote: {
                                url: "/checkEmailOnUpdate",
                                type: "get",
                                data: {
                                    email: function() {
                                        return $("input[name='email']").val();
                                    },
                                    id: {{ Auth::id() }},
                                },
                            }
                        },
                        school: 'required',
                        year: 'required',
                        country: 'required',
                        campuses: 'required',
                        orgcampuses: 'required',
                        state: 'required',
                        postcode: 'required',
                        stage: 'required',
                        school_year: {
                            required: function() {
                                return (jQuery('#stage').val() == 'school');
                            }
                        },
                        school_name: {
                            required: function() {
                                return (jQuery('#stage').val() == 'school');
                            }
                        },
                        // graduate_year: {
                        //     required: function() {
                        //         return (jQuery('#stage').val() == 'done');
                        //     }
                        // },
                        stage_id: {
                            required: function() {
                                return (jQuery('#stage').val() == 'done');
                            }
                        },
                        // other_gender: {
                        //     required: function() {
                        //         return (jQuery('#gender').val() == 'Other');
                        //     }
                        // },
                    },
                    messages: {
                        email: {
                            remote: "Email already in use!"
                        },
                    }
                })

            });
        </script>
        {{-- {!! JsValidator::formRequest('App\Http\Requests\UpdateProfile')->selector('#form-personal') !!} --}}
    @endpush
@endsection
