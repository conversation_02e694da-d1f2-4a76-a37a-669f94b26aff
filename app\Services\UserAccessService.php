<?php

namespace App\Services;

use App\User;
use App\Standard;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class UserAccessService
{
    /**
     * Check if the authenticated user has access the specified user
     * Accepts both User ID (int) and User instance
     */
    public static function currentUserCanAccess(User|int $user): bool
    {
        $authenticatedUser = Auth::user();

        if (!$authenticatedUser) {
            return false;
        }

        // Handle both User instance and ID
        if ($user instanceof User) {
            $targetUser = $user;
            $userId = $user->id;
        } else {
            $userId = $user;
            $targetUser = User::find($userId);

            if (!$targetUser) {
                return false;
            }
        }

        // Users can always access their own data
        if ($authenticatedUser->id === $userId) {
            return true;
        }

        return match (true) {
            $authenticatedUser->isAdmin() => true,
            $authenticatedUser->isTeacher() => self::teacherCanAccessStudent($authenticatedUser, $userId),
            $authenticatedUser->isStaff() => self::staffCanAccessStudent($authenticatedUser, $userId),
            $authenticatedUser->isParent() => self::parentCanAccessChild($authenticatedUser, $userId),
            $authenticatedUser->isEmployer() => self::employerCanAccessStudent($targetUser),
            default => false,
        };
    }

    /**
     * CONVENIENCE METHOD: For when you already have User instances
     */
    public static function oneUserCanAccessAnother(User $authenticatedUser, User $targetUser): bool
    {
        if ($authenticatedUser->id === $targetUser->id) {
            return true;
        }

        return match (true) {
            $authenticatedUser->isAdmin() => true,
            $authenticatedUser->isTeacher() => self::teacherCanAccessStudent($authenticatedUser, $targetUser->id),
            $authenticatedUser->isStaff() => self::staffCanAccessStudent($authenticatedUser, $targetUser->id),
            $authenticatedUser->isParent() => self::parentCanAccessChild($authenticatedUser, $targetUser->id),
            $authenticatedUser->isEmployer() => self::employerCanAccessStudent($targetUser),
            default => false,
        };
    }

    /**
     * Check if teacher can access student based on school association and permissions
     */
    private static function teacherCanAccessStudent(User $teacher, int $studentId): bool
    {
        if (!$teacher->hasManagerAccess() && !$teacher->hasFullAccess()) {
            return false;
        }


        return $teacher->school()
            ->whereHas('students', function (Builder $studentQuery) use ($studentId) {
                $studentQuery->where('id', $studentId)
                    ->whereHas('profile', function (Builder $profileQuery) {
                        $profileQuery->where(function (Builder $q) {
                            $q->whereIn('standard_id', self::getValidStandardIds())->orWhere(function (Builder $subQuery) {
                                $subQuery->whereNull('standard_id')->where('accountcreated', false);
                            });
                        });
                    });
            })->exists();
    }

    /**
     * Check if staff can access student in their organization
     */
    private static function staffCanAccessStudent(User $staff, int $studentId): bool
    {
        return $staff->organisation()
            ->whereHas('students', function (Builder $studentQuery) use ($studentId) {
                $studentQuery->where('id', $studentId)
                    ->whereHas('profile', function (Builder $profileQuery) {
                        $profileQuery->where(function (Builder $q) {
                            $q->whereIn('standard_id', self::getValidStandardIds())->orWhere(function (Builder $subQuery) {
                                $subQuery->whereNull('standard_id')->where('accountcreated', false);
                            });
                        });
                    });
            })->exists();
    }

    /**
     * Check if parent can access their child
     */
    private static function parentCanAccessChild(User $parent, int $childId): bool
    {
        return $parent->children()
            ->where('users.id', $childId)
            ->exists();
    }

    /**
     * Check if employer can access student with public profile
     */
    private static function employerCanAccessStudent(User $student): bool
    {
        return $student->isStudent() &&
            $student->profile?->is_public === true;
    }

    /**
     * Get cached list of valid standard IDs
     */
    private static function getValidStandardIds(): Collection
    {
        return Cache::remember('valid_standard_ids', 3600, function () {
            return Standard::pluck('id');
        });
    }
}
