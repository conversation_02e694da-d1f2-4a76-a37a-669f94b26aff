<?php

namespace App\Http\Controllers;

use App;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Mail;
use App\Role;
use App\User;
use App\Student;
use App\Teacher;
use App\ChildParent;
use App\IndividualStudent;
use App\IndustryCategory;
use App\Industryunit;
use App\Marker;
use App\Staff;
use Illuminate\Support\Facades\Cache;
use Newsletter;
use Symfony\Component\VarDumper\Cloner\Data;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth', ['except' => ['store', 'getInTouch', 'filesync', 'schoolRegister', 'complaintMail', 'landing']]);
    }
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function filesync()
    {
        $public = 'public';
        $files = Storage::allFiles($public);

        foreach ($files as $key => $file) {
            $contents = Storage::get($file);

            Storage::cloud()->put($file, $contents, 'public');
        }
        dd($files);
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        if (Auth::user()->isAdmin()) {
            return view('home');
        }
        return redirect('/home');
    }

    public function store(Request $request)
    {
        Mail::send('emails.contactrequest', [
            'type' => $request->get('type'),
            'name' => $request->get('name'),
            'email' => $request->get('email'),
            'phone' => $request->get('phone'),
            'state' => $request->get('state'),
            'school' => $request->get('school'),
        ], function ($message) use ($request) {
            // $message->from($request->get('email'));
            $message->to(config('mail.contact.to'))->subject('Contact form');
        });

        $response = [
            'status' => 'success',
            'msg' => 'Mail sent successfully',
        ];

        //        return response()->json([$response], 200);
        return Redirect('/#enquiry')->with('message', 'Thank you! We’ll be in touch soon.');
        //          $redirectUrl = url()->previous();
        // if($redirectUrl == url('updateUserSession') || strpos($redirectUrl,'api') !== false) {
        //     $redirectUrl = session()->get('previousUrl');
        // }
        // return redirect($redirectUrl)->with('message', 'Thank you! We’ll be in touch soon.');
    }

    public function getInTouch(Request $request)
    {

        Mail::send('emails.contactrequest', [
            'type' => $request->type,
            'name' => $request->name,
            'email' => $request->email,
            'number' => $request->number,
            'school' => (in_array($request->type, ['Teacher', 'Student'])) ? $request->school : '',
            'text' => $request->message,
        ], function ($message) {
            $mailto = config('mail.contact.to');

            if (config('app.env') == 'local') {
                $mailto = '<EMAIL>';
            }
            $message->to($mailto)->subject(request('subject') ?? 'New Contact Form Request');
        });

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', "Thank you! We will contact you soon.");
    }



    public function complaintMail(Request $request)
    {
        Mail::send('emails.complaint', [
            'url' => $request->url,
            'response' => $request->response,
            'location' => $request->location,
            'firstname' => $request->firstname,
            'lastname' => $request->lastname,
            'email' => $request->email,
            'copy' => $request->copy,
            'subject' => $request->subject,
            'comments' => $request->comments,
        ], function ($message) {
            $mailto = config('mail.contact.to');

            if (config('app.env') == 'local') {
                $mailto = '<EMAIL>';
            }
            $message->to($mailto)->subject(request('subject') ?? 'New Complaint Lodged');
        });

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', "Thank you! We will contact you soon.");
    }

    public function schoolRegister(Request $request)
    {
        Mail::send('emails.schoolregister', [
            'school' => $request->school,
            'contact_name' => $request->contact_name,
            'email' => $request->email,
            'position' => $request->position,
            'phone' => $request->phone,
            'comment' => $request->message,
        ], function ($message) {
            $mailto = config('mail.contact.to');

            if (config('app.env') == 'local') {
                $mailto = '<EMAIL>';
            }
            $message->to($mailto)->subject('New School Registration Request');
        });

        return "success";
    }

    public function users(Request $request)
    {
        $role = Role::whereIn('name', ['School', 'Organisation', 'Admin'])->pluck('id');
        $users = User::select('id', 'name', 'email', 'role_id', 'created_at', 'school_id', 'organisation_id')->with('role:id,name', 'school:id,name', 'organisation:id,name')->whereNotIn('role_id', $role)
            ->when(request('email'), function ($query) {
                $query->where('email', request('email'));
            })
            ->orderBy('email')->paginate(50);
        $request->flash();
        return view('users.index', compact('users'));
    }
    public function userDestroy($id)
    {
        $user = User::find($id);
        if ($user->role->name == 'Teacher') {
            $teacher = Teacher::find($id)->delete();
        } elseif ($user->role->name == 'Staff') {
            $teacher = Staff::find($id)->delete();
        } elseif ($user->role->name == 'Student') {
            $teacher = Student::find($id)->delete();
        } elseif ($user->role->name == 'Parent') {
            $teacher = ChildParent::find($id)->delete();
        } elseif ($user->role->name == 'Individual Student') {
            $teacher = IndividualStudent::find($id)->delete();
        } elseif ($user->role->name == 'Marker') {
            $teacher = Marker::find($id)->delete();
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'User has been deleted successfully');
    }
    public function parentLaunch(Request $request)
    {
        Newsletter::subscribeOrUpdate($request->email, ['STATE' => $request->state], 'parent_subscribers',);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'You have been successfully subscribed to newsletter!');
    }
    public function landing()
    {
        if (!Auth::check() || Auth::user()->isStudent() || Auth::user()->isParent()  || Auth::user()->isTeacher()) {
            return view('app');
        } else {
            return view('home');
        }

        // return redirect()->away('https://www.thecareersdepartment.com/');
    }

    public function getGeoPopupData(Request $request)
    {

        // no use
        // $location = collect([
        //     'address' => $request->address,
        //     'lat' => $request->latitude,
        //     'lng' => $request->longitude,
        // ]);

        $user = Auth::user();
        if ($request->industry || ($request->tags && is_numeric($request->tags))) {


            if ($request->tags) {
                Cache::forget('location' . Auth::id());
                session(['userLocation' => $request->location]);
            }
            $id = $request->industry ?? $request->tags;
            $industry = IndustryCategory::find($id);

            //uncomment this for google map integration

            // $latitude = $user->location()->lat;
            // $longitude = $user->location()->lng;
            // $content = Industryunit::selectRaw("industryunits.id, industryunits.title, industryunits.banner, industryunit_locations.latitude, industryunit_locations.longitude, (6371000 * acos( cos( radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance", [$latitude, $longitude, $latitude])
            //     ->join('industryunit_locations', 'industryunits.id', '=', 'industryunit_locations.industryunit_id')
            //     ->whereNotNull('address')
            //     // ->having("distance", "<", $radius)
            //     ->with('industries')
            //     ->groupBy('id')
            //     ->whereHas('industries', function ($q) use ($industry) {
            //         $q->where('industry_id', $industry->id);
            //     })->orderBy('distance')->take(8)->get();

            $data = collect();
            $data->keywords = $industry->name;
            $data->id = $request->id;

            // $map = $user->mapData([$industry->search_keyword]); //uncomment this for google map integration
            $jobs = $user->jobs($industry->search_keyword);
            $returnHTML = view('partials.foryouBody',  compact('user', 'jobs', 'data'))->render(); // pass map & content when using google maps
        } else {
            Cache::forget('location' . Auth::id());
            session(['userLocation' => $request->location]);



            // $map = $user->mapData($request->tags);

            if ($request->tags) {

                $data = collect();
                $data->keywords = $request->tags;
                $data->id = $request->id;

                // $data->put('keywords', $request->tags); //uncomment this for google map integration
                // $data->put('id', $request->id);  //uncomment this for google map integration
                $returnHTML = view('partials.foryouBody', compact('user', 'data'))->render();  // pass map when using google maps
            } else {
                $returnHTML = view('partials.foryouBody', compact('user'))->render(); // pass map when using google maps
            }
        }


        return response()->json(array('success' => true, 'html' => $returnHTML)); // pass map when using google maps
    }

    public function loginUsingId(Request $request, $id)
    {
        if (Auth::check() && Auth::user()->isAdmin()) {
            if (Auth::loginUsingId($id)) {
                return redirect('/home');
            }
        }
        abort('419');
    }
}
