import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

interface GetParams {
    url: string;
    config?: AxiosRequestConfig;
}

interface PostParams {
    url: string;
    data?: any;
    config?: AxiosRequestConfig;
}

interface PostParamsAuth {
    url: string;
    data?: any;
    config?: AxiosRequestConfig;
}

interface PatchParams {
    url: string;
    data?: any;
    config?: AxiosRequestConfig;
}

interface PutParams {
    url: string;
    data?: unknown;
    config?: AxiosRequestConfig;
}

interface DeleteParams {
    url: string;
    config?: AxiosRequestConfig;
}

export default class ApiService {
    static serviceInstance: ApiService;
    instance: AxiosInstance;
    instanceAuth: AxiosInstance;

    constructor() {
        axios.defaults.withCredentials = false;

        this.instance = axios.create({
            baseURL: getBaseURL(window.location.origin),
        });

        this.instanceAuth = axios.create({
            /* baseURL: environments.oAuthHost, */
        });

        this.setRequestInterceptors();
        this.setResponseInterceptors();
    }

    public static getInstance(): ApiService {
        if (!ApiService.serviceInstance) {
            ApiService.serviceInstance = new ApiService();
        }
        return ApiService.serviceInstance;
    }

    setRequestInterceptors(): void {
        this.instance.interceptors.request.use((config) => {
            if (config && config?.headers) {
                const meta = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
                config.headers['X-CSRF-TOKEN'] = meta.content;
            }

            return config;
        });
    }

    setResponseInterceptors(): void {
        this.instance.interceptors.response.use(
            (response: AxiosResponse) => {
                return response;
            },
            (error: AxiosError) => {
                if (error.response?.status === 401) {
                    window.location.href = '/#/sign-in';
                }

                return Promise.reject(error);
            },
        );
    }

    get<T>({ url, config }: GetParams): Promise<AxiosResponse<T>> {
        return this.instance.get(url, config);
    }

    post<T>({ url, data, config }: PostParams): Promise<AxiosResponse<T>> {
        return this.instance.post(url, data, config);
    }

    postAuth<T>({ url, data, config }: PostParamsAuth): Promise<AxiosResponse<T>> {
        return this.instanceAuth.post(url, data, config);
    }

    patch<T>({ url, data, config }: PatchParams): Promise<AxiosResponse<T>> {
        return this.instance.patch(url, data, config);
    }

    put<T>({ url, data, config }: PutParams): Promise<AxiosResponse<T>> {
        return this.instance.put(url, data, config);
    }

    delete<T>({ url, config }: DeleteParams): Promise<AxiosResponse<T>> {
        return this.instance.delete(url, config);
    }
}

function getBaseURL(windowLocation: string) {
    if (process.env.NODE_ENV === 'development') {
        return process.env.REACT_APP_API_HOST_DEVELOP;
    }

    const urlWithoutProtocols = windowLocation.replace(/(^\w+:|^)\/\//, '');
    if (urlWithoutProtocols.startsWith(process.env.REACT_APP_PRODUCTION_URL || ''))
        return process.env.REACT_APP_API_HOST_PRODUCTION;
    return process.env.REACT_APP_API_HOST_STAGING;
}

ApiService.getInstance();
