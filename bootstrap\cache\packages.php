<?php return array (
  'artesaos/seotools' => 
  array (
    'aliases' => 
    array (
      'SEO' => 'Artesaos\\SEOTools\\Facades\\SEOTools',
      'JsonLd' => 'Artesaos\\SEOTools\\Facades\\JsonLd',
      'SEOMeta' => 'Artesaos\\SEOTools\\Facades\\SEOMeta',
      'Twitter' => 'Artesaos\\SEOTools\\Facades\\TwitterCard',
      'OpenGraph' => 'Artesaos\\SEOTools\\Facades\\OpenGraph',
    ),
    'providers' => 
    array (
      0 => 'Artesaos\\SEOTools\\Providers\\SEOToolsServiceProvider',
    ),
  ),
  'aws/aws-sdk-php-laravel' => 
  array (
    'aliases' => 
    array (
      'AWS' => 'Aws\\Laravel\\AwsFacade',
    ),
    'providers' => 
    array (
      0 => 'Aws\\Laravel\\AwsServiceProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'bepsvpt/secure-headers' => 
  array (
    'providers' => 
    array (
      0 => 'Bepsvpt\\SecureHeaders\\SecureHeadersServiceProvider',
    ),
  ),
  'cviebrock/eloquent-sluggable' => 
  array (
    'providers' => 
    array (
      0 => 'Cviebrock\\EloquentSluggable\\ServiceProvider',
    ),
  ),
  'diglactic/laravel-breadcrumbs' => 
  array (
    'aliases' => 
    array (
      'Breadcrumbs' => 'Diglactic\\Breadcrumbs\\Breadcrumbs',
    ),
    'providers' => 
    array (
      0 => 'Diglactic\\Breadcrumbs\\ServiceProvider',
    ),
  ),
  'digu087/tcd-tagging' => 
  array (
    'providers' => 
    array (
      0 => 'Conner\\Tagging\\Providers\\TaggingServiceProvider',
    ),
  ),
  'genealabs/laravel-model-caching' => 
  array (
    'providers' => 
    array (
      0 => 'GeneaLabs\\LaravelModelCaching\\Providers\\Service',
    ),
  ),
  'gmopx/laravel-owm' => 
  array (
    'providers' => 
    array (
      0 => 'Gmopx\\LaravelOWM\\LaravelOWMServiceProvider',
    ),
  ),
  'jenssegers/agent' => 
  array (
    'providers' => 
    array (
      0 => 'Jenssegers\\Agent\\AgentServiceProvider',
    ),
    'aliases' => 
    array (
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
    ),
  ),
  'laravel/cashier' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Cashier\\CashierServiceProvider',
    ),
  ),
  'laravel/forge-sdk' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Forge\\ForgeServiceProvider',
    ),
  ),
  'laravel/horizon' => 
  array (
    'aliases' => 
    array (
      'Horizon' => 'Laravel\\Horizon\\Horizon',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Horizon\\HorizonServiceProvider',
    ),
  ),
  'laravel/octane' => 
  array (
    'aliases' => 
    array (
      'Octane' => 'Laravel\\Octane\\Facades\\Octane',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Octane\\OctaneServiceProvider',
    ),
  ),
  'laravel/scout' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Scout\\ScoutServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/telescope' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Telescope\\TelescopeServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'laravelcollective/html' => 
  array (
    'providers' => 
    array (
      0 => 'Collective\\Html\\HtmlServiceProvider',
    ),
    'aliases' => 
    array (
      'Form' => 'Collective\\Html\\FormFacade',
      'Html' => 'Collective\\Html\\HtmlFacade',
    ),
  ),
  'lavary/laravel-menu' => 
  array (
    'providers' => 
    array (
      0 => 'Lavary\\Menu\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Menu' => 'Lavary\\Menu\\Facade',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'masterro/laravel-mail-viewer' => 
  array (
    'providers' => 
    array (
      0 => 'MasterRO\\MailViewer\\Providers\\MailViewerServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'overtrue/laravel-favorite' => 
  array (
    'providers' => 
    array (
      0 => 'Overtrue\\LaravelFavorite\\FavoriteServiceProvider',
    ),
  ),
  'proengsoft/laravel-jsvalidation' => 
  array (
    'aliases' => 
    array (
      'JsValidator' => 'Proengsoft\\JsValidation\\Facades\\JsValidatorFacade',
    ),
    'providers' => 
    array (
      0 => 'Proengsoft\\JsValidation\\JsValidationServiceProvider',
    ),
  ),
  'rennokki/laravel-sns-events' => 
  array (
    'providers' => 
    array (
      0 => 'Rennokki\\LaravelSnsEvents\\LaravelSnsEventsServiceProvider',
    ),
  ),
  'renoki-co/laravel-aws-webhooks' => 
  array (
    'providers' => 
    array (
      0 => 'RenokiCo\\AwsWebhooks\\AwsWebhooksServiceProvider',
    ),
  ),
  'spatie/laravel-activitylog' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Activitylog\\ActivitylogServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'spatie/laravel-newsletter' => 
  array (
    'aliases' => 
    array (
      'Newsletter' => 'Spatie\\Newsletter\\NewsletterFacade',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\Newsletter\\NewsletterServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-oracle' => 
  array (
    'aliases' => 
    array (
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
    ),
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    ),
  ),
);