import useSelectionNavigation from '../../utils/useSelectionNavigation';
import { SelectionSteps } from '../../models/SubjectSelection.model';
import StepLayout from '../../components/StepLayout/StepLayout';
import copy from '../../utils/content.json';
import LabelRadio from '@/components/base/LabelRadio/LabelRadio';
import { useQuiz } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { setQuizSelection } from '../../context/SubjectSelection.actions';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';

export default function SubjectSelectionMySkillsView() {
    useSelectionNavigation({ currentStep: SelectionSteps.SKILLS });
    const { data } = useQuiz();
    const { quizSelection } = useSubjectSelectionContextStore();
    const dispatch = useSubjectSelectionDispatch();

    const content = copy.subjectSelection[SelectionSteps.SKILLS];

    const handleCheckboxClick = (id: number) => {
        dispatch(setQuizSelection({ key: SelectionSteps.SKILLS, value: id }));
    };

    return (
        <div className='skills-view'>
            <StepLayout title={content.title} description={content.description} progress={content.progress}>
                <div className='skills-view__list-container'>
                    {data?.skills.map((skill) => {
                        const isSelected = quizSelection.skills.includes(skill.id);

                        return (
                            <LabelRadio
                                key={skill.id}
                                id={skill.id}
                                text={skill.name}
                                onClick={handleCheckboxClick}
                                selected={isSelected}
                            />
                        );
                    })}
                </div>
            </StepLayout>
        </div>
    );
}
