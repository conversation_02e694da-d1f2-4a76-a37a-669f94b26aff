<?php

namespace App;

use Auth;
use App\ChildInvitee;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class DashboardChecklist extends Model
{
    protected $guarded = [];
    // protected $appends = ['compeletedskillsexperience', 'compeletedeportfolio'];

    public static function boot()
    {
        parent::boot();

        static::saved(function ($checklist) {
            if ($checklist->status != 'Closed' && $checklist->status != 'Completed' && $checklist->skills_experience && $checklist->eportfolio && $checklist->pick_industries && $checklist->explore_industries && $checklist->misconceptions_checklist && $checklist->courses && $checklist->resume && $checklist->child) {
                $checklist->update(['status' => 'Completed']);
            }
        });
        static::retrieved(function ($checklist) {
            if ($checklist->status != 'Closed' && $checklist->status != 'Completed' && $checklist->skills_experience && $checklist->eportfolio && $checklist->pick_industries && $checklist->explore_industries && $checklist->misconceptions_checklist && $checklist->courses && $checklist->resume && $checklist->child) {
                $checklist->update(['status' => 'Completed']);
            }
        });
    }

    protected function getCompeletedskillsexperienceAttribute()
    {
        $childinvited = ChildInvitee::where('parent_id', Auth::id())->whereHas('child')->whereProcessed('1')->exists();

        return $this->skills_experience && $childinvited;
    }

    protected function getCompeletedeportfolioAttribute()
    {
        $childinvited = ChildInvitee::where('parent_id', Auth::id())->whereHas('child')->whereProcessed('1')->exists();

        return $this->eportfolio && $childinvited;
    }
}
