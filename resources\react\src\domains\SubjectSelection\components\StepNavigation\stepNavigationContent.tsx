import {
    SUBJECTS_SELECTION_ELECTIVES,
    SUBJECTS_SELECTION_INTERESTS,
    SUBJECTS_SELECTION_JOBS,
    SUBJECTS_SELECTION_LANGUAGES,
    SUBJECTS_SELECTION_LEARNING_STYLE,
    SUBJECTS_SELECTION_SKILLS,
} from '@/router/Router.constants';
import { SelectionSteps } from '../../models/SubjectSelection.model';

import MyJobs from '@/assets/icons/subjectSelection/MyJobs';
import MyLanguages from '@/assets/icons/subjectSelection/MyLanguages';
import MyElectives from '@/assets/icons/subjectSelection/MyElectives';
import MyLearningStyle from '@/assets/icons/subjectSelection/MyLearningStyle';
import MyInterests from '@/assets/icons/subjectSelection/MyInterests';
import MySkills from '@/assets/icons/subjectSelection/MySkills';

export default [
    {
        id: SelectionSteps.INTERESTS,
        title: 'My Interests',
        icon: <MyInterests />,
        description: "I know what I'm interested in",
        route: SUBJECTS_SELECTION_INTERESTS,
    },
    {
        id: SelectionSteps.SKILLS,
        title: 'My Skills',
        icon: <MySkills />,
        description: 'I know what skills I want to learn',
        route: SUBJECTS_SELECTION_SKILLS,
    },
    {
        id: SelectionSteps.STUDY_HABITS,
        title: 'My Learning Style',
        icon: <MyLearningStyle />,
        description: 'I know how I like to learn',
        route: SUBJECTS_SELECTION_LEARNING_STYLE,
    },
    {
        id: SelectionSteps.JOBS,
        title: 'My Jobs',
        icon: <MyJobs />,
        description: "I know what jobs I'm interested in",
        route: SUBJECTS_SELECTION_JOBS,
    },
    {
        id: SelectionSteps.LANGUAGES,
        title: 'My Languages',
        icon: <MyLanguages />,
        description: 'I know what languages I speak',
        route: SUBJECTS_SELECTION_LANGUAGES,
    },
    {
        id: SelectionSteps.ELECTIVES,
        title: 'My Electives',
        icon: <MyElectives />,
        description: 'I know my current electives',
        route: SUBJECTS_SELECTION_ELECTIVES,
    },
];
