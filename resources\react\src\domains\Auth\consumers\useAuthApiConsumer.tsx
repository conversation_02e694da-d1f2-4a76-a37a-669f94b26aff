import { useQuery } from '@tanstack/react-query';
import { useAppDeps } from '@/App';
import { UserResponse } from '../models/Auth.model';
import { log } from '@/services/Logger.service';

export enum AuthApiConsumer {
    userData = 'userData',
    logOut = 'logOut',
}

export default function useAuthApiConsumer() {
    const { data: userData, refetch: getUser } = useUserData();

    return {
        store: {
            user: userData,
        },
        dispatch: {
            getUser,
        },
    };
}

export const useUserData = () => {
    const { authService } = useAppDeps();

    return useQuery<UserResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [AuthApiConsumer.userData],
        queryFn: async () => {
            const user = await authService.verifyToken();
            return user.data.data;
        },
        onError: (error) => {
            const someError = new Error().stack;
            log().debug({ message: `test-${someError}` });
        },
    });
};

export const useLogOut = () => {
    const { authService } = useAppDeps();

    return useQuery({
        enabled: false,
        queryKey: [AuthApiConsumer.logOut],
        queryFn: async () => {
            await authService.logOut();
        },
        onError: () => {
            log().error({ message: `error when logging out` });
        },
    });
};
