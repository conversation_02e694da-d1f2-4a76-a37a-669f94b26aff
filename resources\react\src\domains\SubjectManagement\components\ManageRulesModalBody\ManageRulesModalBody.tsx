import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import {
    useCreateSchoolRule,
    useDeleteSchoolRule,
    useSchoolRules,
    useUpdateSchoolRule,
} from '../../consumers/useSchoolsApiConsumer';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { SchoolRulesDetailsResponse } from '../../models/Schools.service.api.model';
import { useRef, useState } from 'react';
import Trash from '@/assets/icons/subjectSelection/Trash';
import Edit from '@/assets/icons/subjectSelection/Edit';
import Checkmark from '@/assets/icons/subjectSelection/Checkmark';

interface Props {
    onClose?: () => void;
}

enum InputAction {
    DEFAULT = 'DEFAULT',
    ADD = 'ADD',
    EDIT = 'EDIT',
}

enum MutationStatus {
    IDLE = 'idle',
    LOADING = 'loading',
    SUCCESS = 'success',
}

export default function ManageRulesModalBody({ onClose }: Props) {
    const [rule, setRule] = useState('');
    const [edit, setEdit] = useState('');
    const [ruleIdForEdit, setRuleIdForEdit] = useState<number>();
    const { data: user } = useUserData();

    const selectedRule = useRef<any>();

    const { isLoading: isLoadingRules, data: schoolRules } = useSchoolRules({ schoolId: user?.schoolId });

    const { isLoading: isCreating, mutate: createRule } = useCreateSchoolRule();
    const { status: updateStatus, mutateAsync: updateRule } = useUpdateSchoolRule();
    const { status: deleteStatus, mutate: deleteRule } = useDeleteSchoolRule();

    const handleSubmit = async (e: any) => {
        e.preventDefault();

        if (rule === '') return;

        await createRule({ schoolId: user?.schoolId, name: rule });
        setRule('');
        onClose && onClose();
    };

    const handleRuleChange = (action: InputAction, e: any) => {
        switch (action) {
            case InputAction.ADD:
                setRule(e.target.value);
                break;
            case InputAction.EDIT:
                setEdit(e.target.value);
                break;
        }
    };

    const handleStartEditRule = (rule: SchoolRulesDetailsResponse) => {
        setRuleIdForEdit(rule.id);
        setEdit(rule.name);
    };

    const handleEditOk = async (e: any, rule: SchoolRulesDetailsResponse) => {
        e.preventDefault();

        if (rule.name === edit) {
            setRuleIdForEdit(undefined);
            return;
        }

        await updateRule({ schoolId: user?.schoolId, ruleId: ruleIdForEdit as number, name: edit });
        setEdit('');
        setRuleIdForEdit(undefined);
    };

    const handleRemoveRule = (ruleId: number) => {
        deleteRule({ schoolId: user?.schoolId, ruleId });
        setRuleIdForEdit(undefined);
    };

    return (
        <div className='manage-rules'>
            <p>
                Help your students during their subject selections process by keeping their guidelines front and centre.{' '}
            </p>
            <form className='manage-rules__form' onSubmit={handleSubmit}>
                <div className='manage-rules__form-group'>
                    <input
                        type='text'
                        className='input'
                        value={rule}
                        onChange={(e) => handleRuleChange(InputAction.ADD, e)}
                    />
                    <Button
                        type={ButtonType.OUTLINED_BLACK}
                        title={isCreating ? 'Adding...' : 'Add'}
                        spanClassName='fs-6'
                        spinner={isCreating}
                    />
                </div>
            </form>
            <div>
                <p className='fw-bold fw-4'>Your rules</p>
                {isLoadingRules && (
                    <div className='d-flex align-items-center'>
                        <span>Fetching rules...</span>
                        <div className='spinner-border text-primary ms-2' role='status' />
                    </div>
                )}
                <div style={{ maxHeight: 400, overflow: 'auto' }}>
                    {schoolRules?.map((rule) => {
                        const isDeleting = deleteStatus === MutationStatus.LOADING && selectedRule.current === rule.id;
                        const isUpdating = updateStatus === MutationStatus.LOADING && selectedRule.current === rule.id;

                        return (
                            <div key={rule.id} className='d-flex justify-content-between pe-2'>
                                {(ruleIdForEdit === rule.id && (
                                    <>
                                        <form
                                            style={{ width: '100%', zIndex: 99 }}
                                            onSubmit={(e) => handleEditOk(e, rule)}
                                        >
                                            <textarea
                                                style={{ height: 'inherit' }}
                                                className='input'
                                                value={edit}
                                                onChange={(e) => handleRuleChange(InputAction.EDIT, e)}
                                            />
                                        </form>
                                        <ActionIcons
                                            onEditOk={(e) => handleEditOk(e, rule)}
                                            isUpdating={isUpdating}
                                            isDeleting={isDeleting}
                                            onRemove={() => {
                                                handleRemoveRule(rule.id);
                                                selectedRule.current = rule.id;
                                            }}
                                        />
                                    </>
                                )) || (
                                    <>
                                        <p style={{ display: 'flex', alignItems: 'center', padding: 10 }}>
                                            {rule.name}
                                        </p>
                                        <ActionIcons
                                            isDeleting={isDeleting}
                                            onEdit={() => {
                                                handleStartEditRule(rule);
                                                selectedRule.current = rule.id;
                                            }}
                                            onRemove={() => {
                                                handleRemoveRule(rule.id);
                                                selectedRule.current = rule.id;
                                            }}
                                        />
                                    </>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}

interface ActionIconsProps {
    onEdit?: () => void;
    onEditOk?: (e: any) => void;
    onRemove: () => void;
    isUpdating?: boolean;
    isDeleting: boolean;
}

function ActionIcons({ onEditOk, onRemove, isDeleting, onEdit, isUpdating }: ActionIconsProps) {
    return (
        <div className='d-flex align-items-center ms-2'>
            {isUpdating ? (
                <div className='spinner-border text-primary me-2' role='status' />
            ) : (
                <>
                    {onEdit && (
                        <span className='cursor-pointer me-2' onClick={onEdit}>
                            <Edit />
                        </span>
                    )}
                    {onEditOk && (
                        <span className='cursor-pointer me-2' onClick={onEditOk}>
                            <Checkmark />
                        </span>
                    )}
                </>
            )}
            {isDeleting ? (
                <div className='spinner-border text-primary ms-2' role='status' />
            ) : (
                <Trash className='cursor-pointer' onClick={onRemove} />
            )}
        </div>
    );
}
