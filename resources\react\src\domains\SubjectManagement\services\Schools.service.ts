import { Filter<PERSON>pi, PaginationApi } from '@/common/models/Api.model';
import ApiService from '@/services/Api.decode.service';
import { AxiosResponse } from 'axios';
import {
    SchoolReport,
    SchoolRulesDetailsResponse,
    SchoolRulesResponse,
    SchoolSubjectManagementFilterResponse,
    SchoolSubjectsFilterQueryParams,
    SchoolSubjectsResponse,
} from '../models/Schools.service.api.model';

export class SchoolsService {
    private apiService: ApiService;

    private static instance: SchoolsService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): SchoolsService {
        if (!this.instance) {
            this.instance = new SchoolsService();
        }
        return this.instance;
    }

    public getSchoolSubjects({
        schoolId,
        pagination,
        filter,
    }: {
        schoolId: number | null;
        pagination?: PaginationApi;
        filter?: FilterApi & SchoolSubjectsFilterQueryParams;
    }): Promise<AxiosResponse<SchoolSubjectsResponse>> {
        const params = {
            ...pagination,
            ...filter,
        };

        return this.apiService.get({
            url: `/schools/${schoolId}/subjects`,
            config: {
                params,
                paramsSerializer: {
                    serialize: (params: Record<string, any>): string => {
                        const searchParams = new URLSearchParams();

                        for (const key in params) {
                            if (params.hasOwnProperty(key)) {
                                const value = params[key];

                                if (Array.isArray(value)) {
                                    value.forEach((item) => {
                                        searchParams.append(`${key}[]`, item.value);
                                    });
                                } else {
                                    searchParams.set(key, value);
                                }
                            }
                        }

                        return searchParams.toString();
                    },
                },
            },
        });
    }

    public updateSchoolSubject(schoolId: number | null, subjects: number[]) {
        return this.apiService.post({
            url: `/schools/${schoolId}/subjects`,
            data: {
                subjects,
            },
        });
    }

    public requestSchoolSubjectsCsvExport(schoolId: number): Promise<AxiosResponse<{ message: string }>> {
        return this.apiService.get({ url: `/schools/${schoolId}/subjects/export` });
    }

    public requestSchoolStudentsCsvExport(schoolId: number): Promise<AxiosResponse<{ message: string }>> {
        return this.apiService.get({ url: `/schools/${schoolId}/reports/students` });
    }

    public getSchoolRules({ schoolId }: { schoolId: number | null }): Promise<AxiosResponse<SchoolRulesResponse>> {
        return this.apiService.get({ url: `/schools/${schoolId}/rules` });
    }

    public createSchoolRule(
        schoolId: number | null,
        name: string,
    ): Promise<AxiosResponse<{ data: SchoolRulesDetailsResponse }>> {
        return this.apiService.post({
            url: `/schools/${schoolId}/rules`,
            data: {
                name,
            },
        });
    }

    public updateSchoolRule(
        schoolId: number | null,
        ruleId: number,
        name: string,
    ): Promise<AxiosResponse<{ data: SchoolRulesDetailsResponse }>> {
        return this.apiService.patch({
            url: `/schools/${schoolId}/rules/${ruleId}`,
            data: {
                name,
            },
        });
    }

    public deleteSchoolRule(
        schoolId: number | null,
        ruleId: number,
    ): Promise<AxiosResponse<{ data: SchoolRulesDetailsResponse }>> {
        return this.apiService.delete({
            url: `/schools/${schoolId}/rules/${ruleId}`,
        });
    }

    public getSchoolSubjectManagementFilters(): Promise<
        AxiosResponse<{ data: SchoolSubjectManagementFilterResponse }>
    > {
        return this.apiService.get({ url: `/schools-subjects-filters` });
    }

    public getSchoolReport(schoolId: number | null, type: SchoolReport): Promise<AxiosResponse<{ message: string }>> {
        return this.apiService.get({ url: `/schools/${schoolId}/reports/${type}` });
    }
}
