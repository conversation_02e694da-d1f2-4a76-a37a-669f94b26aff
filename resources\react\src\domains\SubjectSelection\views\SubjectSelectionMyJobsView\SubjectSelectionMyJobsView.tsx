import useSelectionNavigation from '../../utils/useSelectionNavigation';
import { SelectionSteps } from '../../models/SubjectSelection.model';
import StepLayout from '../../components/StepLayout/StepLayout';
import copy from '../../utils/content.json';
import { useQuiz } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';
import { setQuizSelection } from '../../context/SubjectSelection.actions';
import LabelRadio from '@/components/base/LabelRadio/LabelRadio';

export default function SubjectSelectionMyJobsView() {
    useSelectionNavigation({ currentStep: SelectionSteps.JOBS });
    const { data } = useQuiz();
    const { quizSelection } = useSubjectSelectionContextStore();
    const dispatch = useSubjectSelectionDispatch();

    const content = copy.subjectSelection[SelectionSteps.JOBS];

    const handleCheckboxClick = (id: number) => {
        dispatch(setQuizSelection({ key: SelectionSteps.JOBS, value: id }));
    };

    return (
        <div className='jobs-view'>
            <StepLayout title={content.title} description={content.description} progress={content.progress}>
                <div className='jobs-view__list-container'>
                    {data?.jobs.map((job) => {
                        const isSelected = quizSelection.jobs.includes(job.id);

                        return (
                            <LabelRadio
                                key={job.id}
                                id={job.id}
                                text={job.name}
                                onClick={handleCheckboxClick}
                                selected={isSelected}
                            />
                        );
                    })}
                </div>
            </StepLayout>
        </div>
    );
}
