import SearchIcon from '@/assets/icons/subjectSelection/SearchIcon';

interface Props {
    className?: string;
    value?: string;
    style?: React.CSSProperties;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export default function SearchMenu({ onChange, value, className, style }: Props) {
    return (
        <div
            style={style}
            className={`menu menu-sub menu-sub-dropdown menu-rounded menu-column p-7 w-325px w-md-375px show search-menu ${className}`}
        >
            <div>
                <form className='w-100 position-relative mb-3'>
                    <SearchIcon style={{ position: 'absolute', left: 0, top: 5 }} />
                    <input
                        type='text'
                        value={value}
                        className='fs-6 form-control form-control-flush ps-16'
                        name='search'
                        placeholder='Search...'
                        onChange={onChange}
                    ></input>
                </form>
                <div className='separator border-gray-200 mb-6'></div>
            </div>
        </div>
    );
}
