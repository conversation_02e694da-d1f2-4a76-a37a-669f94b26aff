import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import { SubjectsResponse } from '../../models/SubjectSelection.api.model';
import React from 'react';
import Gallery from '@/components/Gallery/Gallery';
import { GalleryType } from '@/domains/SubjectManagement/models/Subjects.service.api.model';
import { truncateText } from '@/utils/helpers';

interface Props {
    isSelected?: boolean;
    subject: SubjectsResponse;
    onAddSubjectPreferenceClick: () => void;
    isTeacher?: boolean;
    isLoading?: boolean;
}

const CourseDetails = ({
    onAddSubjectPreferenceClick,
    subject,
    isSelected,
    isTeacher = false,
    isLoading = false,
}: Props) => {
    const majorWork: any = {
        '0': 'No',
        '1': 'Yes',
    };

    const handleDescriptionClick = () => {
        subject.description_url && window.open(subject.description_url, '_blank');
    };

    const isCourseNumber =
        String(subject?.number) !== 'undefined' &&
        String(subject.number) !== 'null' &&
        String(subject.number) !== '0' &&
        String(subject.number) !== '';

    const handleGalleryClick = () => {
        document.getElementById('gallery-item-0')?.click();
    };


    return (
        <div className='course-details shadow mb-5 bg-white'>
            {subject?.featured_image_url && subject.featured_image_url !== '' && (
                <div
                    style={{ backgroundImage: `url(${subject.featured_image_url})` }}
                    className='course-details__image-container'
                ></div>
            )}
            <div className='course-details__button-container'>
                {!!subject.gallery.length && (
                    <Button
                        title='Subject Showcase'
                        type={ButtonType.GRAY_DARKER}
                        onClick={handleGalleryClick}
                        disabled={isLoading}
                        spinner={isLoading}
                    />
                )}
                <Button
                    title='Full Course Description'
                    type={ButtonType.OUTLINED_BLACK}
                    onClick={handleDescriptionClick}
                />
                {!isTeacher && (
                    <Button
                        title={isSelected ? 'Remove from Saved Subjects' : 'Add to Saved Subjects'}
                        type={ButtonType.SECONDARY}
                        onClick={onAddSubjectPreferenceClick}
                    />
                )}
            </div>
            <div className='course-details__info-container'>
                {isCourseNumber && (
                    <div className='course-details__info'>
                        <span>Course Number</span>
                        <span>{String(subject.number)}</span>
                    </div>
                )}
                <div className='course-details__info'>
                    <span>Type</span>
                    <span>{subject.subject_type?.name || ''}</span>
                </div>
                {subject.course_type && (<div className='course-details__info'>
                    <span>Course Type</span>
                    <span>{subject.course_type?.name || ''}</span>
                </div>)}
                <div className='course-details__info'>
                    <span>Major Work</span>
                    <span>{majorWork[subject.major_work]}</span>
                </div>
                {subject?.units && subject.units !== '' && subject.units !== '0' && (
                    <div className='course-details__info'>
                        <span>Units</span>
                        <span>{subject.units}</span>
                    </div>
                )}
            </div>
            {!!subject.gallery.length && (
                <Gallery>
                    {subject.gallery?.map((item, index) => {
                        switch (item.type) {
                            case GalleryType.Youtube:
                            case GalleryType.Vimeo:
                                return (
                                    <a
                                        id={`gallery-item-${index}`}
                                        key={item.id}
                                        data-lg-size='1280-720'
                                        data-src={item.src}
                                        data-poster={item.thumbnail}
                                        data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                    >
                                        <img alt='thumbnail' src={item.thumbnail} className='img-responsive' />
                                    </a>
                                );
                            case GalleryType.Wistia:
                                return (
                                    <a
                                        id={`gallery-item-${index}`}
                                        key={item.id}
                                        data-lg-size='1280-720'
                                        data-src={item.src}
                                        data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                    >
                                        <div
                                            style={{
                                                height: 262,
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                backgroundColor: '#000000',
                                            }}
                                        >
                                            <span className='fw-bold fs-4 iframe-title'>
                                                {truncateText(item.caption, 50)}
                                            </span>
                                        </div>
                                    </a>
                                );
                            case GalleryType.Image:
                                return (
                                    <a
                                        id={`gallery-item-${index}`}
                                        key={item.id}
                                        data-src={item.src}
                                        data-sub-html={`
                                        <h4>${item.caption}</h4><p>${item.description}</p>
                                        `}
                                    >
                                        <img alt='thumbnail' src={item.src} className='img-responsive' />
                                    </a>
                                );
                            case GalleryType.Iframe:
                                return (
                                    <a
                                        id={`gallery-item-${index}`}
                                        key={item.id}
                                        data-iframe='true'
                                        data-src={item.src}
                                        data-iframe-title={item.caption}
                                        data-sub-html={`
                                        <h4>${item.caption}</h4><p>${item.description}</p>
                                        `}
                                    >
                                        <div
                                            style={{
                                                height: 262,
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                backgroundColor: '#000000',
                                            }}
                                        >
                                            <span className='fw-bold fs-4 iframe-title'>
                                                {truncateText(item.caption, 50)}
                                            </span>
                                        </div>
                                    </a>
                                );
                            default:
                                return <a>nothing</a>;
                        }
                    })}
                </Gallery>
            )}
            {/* TODO: Uncomment when the feature is available to show multiple years */}
            <div className='course-details__info-container'>
                <div className='course-details__info'>
                    <span>Available In</span>
                    <div
                        style={{ width: '200px', margin: 'auto 0 auto auto', textAlign: 'right', gap: 5 }}
                        className='row'
                    >
                        {subject.standards?.map((standard, index) => {
                            return (
                                <React.Fragment key={standard.id}>
                                    <span style={{ padding: 0 }} className='col'>
                                        Year {standard.title}
                                    </span>
                                    {(index + 1) % 2 === 0 && <div className='w-100'></div>}
                                </React.Fragment>
                            );
                        })}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CourseDetails;