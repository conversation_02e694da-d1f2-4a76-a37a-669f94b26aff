import { useState } from 'react';
import { useSubjectSelectionContextStore, useSubjectSelectionDeps } from '../SubjectSelection';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
    SubjectPreferenceStatus,
    SubjectSelectionQuizResponse,
    SubjectsResponse,
} from '../models/SubjectSelection.api.model';
import { log } from '@/services/Logger.service';
import { useAppContextStore, useAppDispatch } from '@/context/App.context';
import {
    setMappedSubjectsAction,
    setPreferencesDataState,
    setQuizSubmissionState,
    setSubjectPreferencesSubmissionState,
    setSubmittedQuizSelectionState,
} from '@/context/App.actions';
import { mapSubjectsByAreaId, mapSubjectsToSubjectPreferences } from '../utils/mappers';
import { SubjectPreference, SubjectSelectionQuizState } from '../models/SubjectSelection.model';
import useToast from '@/hooks/useToast';

export enum SubjectSelectionApiConsumer {
    quiz = 'quiz',
    quizSubmission = 'quizSubmission',
    quizAnswers = 'quizAnswers',
    quizResults = 'quizResults',
    subjects = 'subjects',
    subjectPreferences = 'subjectPreferences',
    subjectPreferencesSelection = 'subjectPreferencesSelection',
    subjectById = 'subjectById',
}

export default function useSubjectSelectionApiConsumer() {
    const { refetch: getQuiz, isLoading } = useQuiz();

    const queryClient = useQueryClient();

    const getQuizData = () => {
        return queryClient.getQueryData([SubjectSelectionApiConsumer.quiz]);
    };

    return {
        isLoading,
        store: {
            getQuizData,
        },
        dispatch: {
            getQuiz,
        },
    };
}

export const useQuiz = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();

    return useQuery<SubjectSelectionQuizResponse>({
        queryKey: [SubjectSelectionApiConsumer.quiz],
        queryFn: async () => {
            const quiz = await subjectSelectionService.getQuiz();
            return quiz.data;
        },
        onError: (error) => {
            log().error({ message: 'useQuiz - fetching quiz failed' });
            console.trace(error);
        },
    });
};

export const useQuizSubmission = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();
    const dispatch = useAppDispatch();
    const queryClient = useQueryClient();
    const store = useSubjectSelectionContextStore();

    return useQuery<SubjectSelectionQuizState>({
        enabled: false,
        queryKey: [SubjectSelectionApiConsumer.quizSubmission],
        queryFn: async () => {
            const response = await subjectSelectionService.submitQuiz(store.quizSelection);
            const quizSelection = response.data.data;

            dispatch(setQuizSubmissionState(true));
            return quizSelection;
        },
        onError: (error) => {
            log().error({ message: 'useQuizSubmission - quiz submission failed' });
            console.trace(error);
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries([SubjectSelectionApiConsumer.quizResults]);
            queryClient.invalidateQueries([SubjectSelectionApiConsumer.quizAnswers]);
            dispatch(setSubmittedQuizSelectionState(data));
        },
    });
};

export const useQuizAnswers = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();
    const dispatch = useAppDispatch();

    return useQuery<SubjectSelectionQuizState>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [SubjectSelectionApiConsumer.quizAnswers],
        queryFn: async () => {
            const response = await subjectSelectionService.getQuizAnswers();
            const quizSelection = response.data.data;
            const hasAnswers = Object.values(quizSelection).some((array) => array.length > 0);

            quizSelection &&
                !!Object.entries(quizSelection).length &&
                dispatch(setSubmittedQuizSelectionState(quizSelection));
            dispatch(setQuizSubmissionState(hasAnswers));

            return quizSelection;
        },
        onError: (error) => {
            log().error({ message: 'useQuizAnswers - fetching quiz answers failed ' });
            console.trace(error);
        },
    });
};

export const useSubjectPreferencesSelection = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();
    const dispatch = useAppDispatch();

    return useQuery<SubjectPreference[]>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [SubjectSelectionApiConsumer.subjectPreferencesSelection],
        queryFn: async () => {
            const response = await subjectSelectionService.getSubjectPreferences();
            const { data } = response.data;
            const subjectPreferences = mapSubjectsToSubjectPreferences(data.subjects);

            dispatch(setPreferencesDataState(subjectPreferences, data.status));

            return subjectPreferences;
        },
        onError: (error) => {
            log().error({ message: 'useSubjectPreferencesSelection - fetching subject preferences failed ' });
        },
    });
};

export const useSubmitPreferences = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();
    const store = useAppContextStore();
    const dispatch = useAppDispatch();

    return useMutation({
        mutationFn: async (status?: SubjectPreferenceStatus) => {
            const selectedPreferencesSubjectIds = store.selectedPreferences.map((preference) => preference.id);
            const response = await subjectSelectionService.submitSubjectPreferences({
                subjects: selectedPreferencesSubjectIds,
                status,
            });

            return response.data;
        },
        onSuccess: ({ data }) => {
            const subjectPreferences = mapSubjectsToSubjectPreferences(data.subjects);

            dispatch(setPreferencesDataState(subjectPreferences, data.status));
            dispatch(setSubjectPreferencesSubmissionState(true));

            return subjectPreferences;
        },
    });
};

export const useSubjects = () => {
    const { subjectSelectionService } = useSubjectSelectionDeps();
    const { data: quizResults } = useQuizResults();
    const dispatch = useAppDispatch();

    return useQuery<SubjectsResponse[]>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [SubjectSelectionApiConsumer.subjects],
        queryFn: async () => {
            const { data } = await subjectSelectionService.getSubjects();
            const subjects = data.data;

            return subjects;
        },
        onSuccess: (subjects) => {
            if (quizResults?.completed) return;

            const mappedSubjects = mapSubjectsByAreaId(subjects || []);
            dispatch(setMappedSubjectsAction(mappedSubjects));
        },
        onError: (error) => {
            log().error({ message: 'useSubjects - fetching subjects failed' });
            console.trace(error);
        },
    });
};

export const useSubjectById = (subjectId: string) => {
    const { subjectSelectionService } = useSubjectSelectionDeps();

    return useQuery<SubjectsResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [SubjectSelectionApiConsumer.subjectById],
        queryFn: async () => {
            const { data } = await subjectSelectionService.getSubjectById(subjectId);
            const subject = data.data;

            return subject;
        },
        onError: (error) => {
            log().error({ message: 'useSubjectById - fetching single subject failed', error: error as Error });
        },
    });
};

export const useQuizResults = () => {
    const toast = useToast();
    const dispatch = useAppDispatch();
    const [customOptions, setCustomOptions] = useState<any>({
        enabled: false,
        refetchInterval: 10000,
        staleTime: 0,
        cacheTime: 5 * 60 * 1000,
    });
    const { subjectSelectionService } = useSubjectSelectionDeps();

    return useQuery<{ attachment_url: string; attachment: string; completed: boolean; subjects: SubjectsResponse[] }>({
        ...customOptions,
        queryKey: [SubjectSelectionApiConsumer.quizResults],
        queryFn: async () => {
            const { data } = await subjectSelectionService.getQuizStatus();

            return {
                attachment: data.attachment,
                attachment_url: data.attachment_url,
                completed: data.completed,
                subjects: data.data,
            };
        },
        onError: (error) => {
            log().error({ message: 'useSubjects - fetching subjects failed' });
            console.trace(error);
        },
        onSuccess: (data) => {
            if (!data.completed) {
                setCustomOptions({
                    enabled: true,
                    refetchInterval: 10000,
                    staleTime: 0,
                    cacheTime: 5 * 60 * 1000,
                });
                return;
            }

            setCustomOptions({
                enabled: false,
                refetchInterval: 0,
                staleTime: Infinity,
                cacheTime: Infinity,
            });

            const mappedSubjects = mapSubjectsByAreaId(data.subjects || []);

            dispatch(setMappedSubjectsAction(mappedSubjects));

            toast.show({ message: 'Your results have been loaded!' });
        },
    });
};
