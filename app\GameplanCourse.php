<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class GameplanCourse extends Model
{

    public static function boot()
    {
        parent::boot();

        static::saved(function ($course) {
            if (auth()->user()->isParent()) {
                if (self::whereUserId(auth()->id())->count() >= 5) {
                    $course->parent->saveParentDashboardChecklist(['courses' => true]);
                }
            }
        });
    }
    protected $guarded = [];

    public function course()
    {
        return $this->belongsTo('App\Course');
    }
    public function parent()
    {
        return $this->belongsTo('App\ChildParent', 'user_id');
    }
    public function student()
    {
        return $this->belongsTo('App\Student', 'user_id');
    }
    public function individual()
    {
        return $this->belongsTo('App\IndividualStudent', 'user_id');
    }
}
