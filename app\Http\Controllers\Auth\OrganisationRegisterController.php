<?php

namespace App\Http\Controllers\Auth;

use App\ChildInvitee;
use App\Http\Controllers\Controller;
// use Mail;
use App\Mail\OrgAccountLimitNotification;
use App\IndividualStudent;
use App\Mail\PleaseConfirmYourEmail;
use App\Profile;
use App\School;
use App\Standard;
use App\State;
use App\Student;
use App\User;
use Auth;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Country;
use App\SchoolDetail;
use App\Events\StudentRegistered;
use App\Organisation;
use App\Stage;

class OrganisationRegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
     */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|min:6|confirmed',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array $data
     * @return User
     */

    public function selectOrg()
    {
        return view('auth.organisation.select');
    }


    public function searchresult()
    {
        $term = request('q');
        $schools = Organisation::confirmed()->select('id', 'name')->where('name', 'like', "%" . $term . "%")->with('detail:school_id,slug')->paginate();
        return $schools;
    }

    public function organisation(Request $request, $slug)
    {
        $request->session()->forget('data');
        $school = SchoolDetail::whereHas('organisation')->whereSlug($slug)->first();
        if ($school) {
            $request->session()->put('data.organisation_id', $school->school_id);
            return view('auth.organisation.organisation', compact('school'));
        }
        abort(404);
    }

    public function validateOrg(Request $request, $slug)
    {
        // $request->session()->forget('data');
        $school = Organisation::find($request->school);
        if ($school) {
            if ($school->password === $request->password) {
                if ($request->has('campus')) {
                    $request->session()->put('data.campus_id', $request->campus);
                }
                return view('auth.organisation.searchemail', compact('school'));
            }
            return redirect('organisation/' . $slug)->with('error', "Password didn't match!");
        }
        return redirect('organisation/' . $slug)->with('error', "Whoops! Something went wrong. Please try again.");
    }

    public function searchEmail()
    {
        $term = request('q');
        $organisation_id = session('data.organisation_id');
        if ($term) {
            if (filter_var($term, FILTER_VALIDATE_EMAIL)) {
                $children = IndividualStudent::select('id', 'name', 'email')->where('email', $term)->whereHas('profile', function ($query) {
                    $query->where('accountcreated', '1');
                })->first();

                if ($children) {
                    return $children;
                } else {
                    $student = Student::select('id', 'email')->where('email', $term)->where(function ($query) use ($organisation_id) {
                        $query->where('organisation_id',  $organisation_id)->orWhereNull('organisation_id');
                    })->orderBy('name')->first();
                }
                if (!$student) {
                    $student = collect(['id' => $term, 'email' => 'Create new account']);
                }
                return $student;
            }
        }
        return 'Invalid email address';
    }

    public function createaccount(Request $request)
    {
        if ($request->has('school')) {
            $countries = Country::all();
            $states = State::all();
            $stages = Stage::all();
            $years = Standard::all();
            $orgId = $request->session()->get('data.organisation_id');
            $request->session()->forget('data.user_id');
            $student = null;

            $school = Organisation::find($orgId);

            if (is_numeric(request('student'))) {
                $student = User::with('profile')->find(request('student')); //User model is used to get both Student and Individual students
            }
            if (!$student) {
                $student = new Student();
                $student->email = request('student');
                $student->organisation_id = request('school');
                return view('auth.organisation.registernew', compact('student', 'years', 'countries', 'states', 'school', 'stages'));
            }

            $request->session()->put('data.user_id', $student->id);
            if (($student->role_id == 4) || (!$student->organisation_id && $student->profile->accountcreated)) {
                // if (!Organisation::hasSpace($school, $student->profile->standard_id)) {
                //     return redirect('/school/select-school')->with('message', "Sorry! You can't connect your account to your organisation's account because maximum number of students has already been registered for year " . $student->profile->standard_id . ".");
                // }

                $request->session()->put('data.connect', true);

                return redirect('/connectorganisation');

                // return view('auth.connectschool', compact('student', 'school'));
            }
            if (!filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
                $student->email = '';
            }
            if ($student->profile->accountcreated) {
                return redirect('/login')->with('message', 'Looks like you already have gone through this step. Please login with your email and password');
            }
            return view('auth.organisation.register', compact('student', 'years', 'countries', 'states', 'school', 'stages'));
        } else {
            $organisation_id = $request->session()->get('data.organisation_id');
            if (!Organisation::find($organisation_id)->student_limit_reached) {
                // if ($request->session()->get('data.organisation_id') && $request->has('year')) {
                //     if (!Organisation::hasSpace($organisation_id, $request->year)) {
                //         return redirect('/school/select-school')->with('message', 'Oops! Student registration has failed because maximum number of students has already been registered for year ' . $request->year . '!');
                //     }
                // }

                $profile = new Profile();

                if ($request->has(['firstname', 'lastname'])) {
                    $profile->firstname = request('firstname');
                    $profile->lastname = request('lastname');
                }

                if (request('other_gender')) {
                    $gender = request('other_gender');
                } else {
                    $gender = request('gender');
                }

                if ($gender) {
                    $profile->gender = request('gender');
                }
                if (request('stage') == 'school') {
                    $standard_id = request('year');
                    $school = request('school_name');
                    $graduate_year = null;
                } else {
                    // $stage_id = request('stage_id');
                    $graduate_year = request('graduate_year');
                    $school = null;
                    $standard_id = Standard::nonStudentId();
                }
                $profile->standard_id = $standard_id;
                // $profile->stage_id = $stage_id;
                $profile->graduate_year = $graduate_year;
                $profile->school = $school;
                $profile->accountcreated = true;

                if (!$request->session()->get('data.user_id')) {
                    $user = User::where('email', request('email'))->first();
                    if ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists()) {
                        $student = $user;
                    } else {
                        $student = new Student;
                        $student->email = request('email');
                    }
                    $student->role_id = 3;
                    $student->name = request('firstname') . " " . request('lastname');
                    $student->organisation_id = $organisation_id;
                    $student->password = bcrypt(request('password'));
                    $student->state_id = request('state');
                    $student->postcode = request('postcode');
                    $student->save();
                    if ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists()) {
                        $student->profile()->update($profile->toArray());
                        ChildInvitee::where('child_id', $student->id)->whereIn(
                            'parent_id',
                            $student->parents()->pluck('parent_id')
                        )->update(['processed' => '1']);
                        if ($organisation_id) {
                            event(new StudentRegistered($student, true));  // Second argument true in case of organisation
                        }
                    } else {
                        $student->profile()->save($profile);
                        event(new StudentRegistered($student, true));  // Second argument true in case of organisation
                    }
                    if ($request->session()->has('data.campus_id')) {
                        $student->orgCampuses()->sync($request->session()->get('data.campus_id'));
                    }

                    $organisation = $student->organisation;
                    if ($organisation->detail->total_students == $organisation->account_limit - 20) {
                        Mail::send(new OrgAccountLimitNotification("alert", $organisation));
                    } else if ($organisation->detail->total_students == $organisation->account_limit) {
                        Mail::send(new OrgAccountLimitNotification("complete", $organisation));
                    }
                } else {
                    $student = Student::findOrFail($request->session()->get('data.user_id'));
                    if ($student->profile->accountcreated) {
                        return redirect('/login')->with('message', 'Looks like you already have gone through this step. Please login with your email and password');
                    }
                    if ($request->has(['firstname', 'lastname'])) {
                        $student->name = request('firstname') . " " . request('lastname');
                    }
                    if ($request->has('email')) {
                        $student->email = request('email');
                    }
                    $student->password = bcrypt(request('password'));
                    if ($request->has('state')) {
                        $student->state_id = request('state');
                    }
                    if ($request->has('postcode')) {
                        $student->postcode = request('postcode');
                    }
                    $student->save();
                    $student->profile()->update($profile->toArray());
                    if ($student->school) {
                        event(new StudentRegistered($student, true));  // Second argument true in case of organisation
                    }
                    if ($request->session()->has('data.campus_id')) {
                        $student->orgCampuses()->sync($request->session()->get('data.campus_id'));
                    }

                    $organisation = $student->organisation;
                    if ($organisation->detail->total_students == $organisation->account_limit - 20) {
                        Mail::send(new OrgAccountLimitNotification("alert", $organisation));
                    } else if ($organisation->detail->total_students == $organisation->account_limit) {
                        Mail::send(new OrgAccountLimitNotification("complete", $organisation));
                    }
                }

                if (Auth::attempt(['email' => $student->email, 'password' => request('password')])) {
                    return redirect('/#/gameplan')->with('message', "Your account has been successfully created.");
                }
            } else {
                $redirectUrl = url()->previous();
                if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                    $redirectUrl = session()->get('previousUrl');
                }
                return redirect($redirectUrl)->with('message', "Sorry! Your organisation has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.");
            }
        }
    }


    public function showCreateAccount()
    {
        if (!(session()->has('data.connect'))) {
            session()->forget('data');
            return redirect('/school/select-school');
        }
        session()->forget('data.connect');
        $student = User::find(session('data.user_id'));
        $school = Organisation::find(session('data.organisation_id'));

        return view('auth.organisation.connect', compact('student', 'school'));
    }



    public function login($id)
    {
        $name = User::where("id", $id)->first();
        return view('auth/login')->with('name', $name);
    }


    public function showConnectOrg()
    {
        if (!(session()->has('data.connect'))) {
            session()->forget('data');
            return redirect('/school/select-school');
        }
        session()->forget('data.connect');
        $student = User::find(session('data.user_id'));
        $school = Organisation::find(session('data.organisation_id'));

        return view('auth.organisation.connect', compact('student', 'school'));
    }


    public function connectOrg(Request $request)
    {
        $student = User::find($request->session()->get('data.user_id'));
        $organisation_id = $request->session()->get('data.organisation_id');
        if (!Organisation::find($organisation_id)->student_limit_reached) {
            if (Auth::validate(['id' => $student->id, 'password' => $request->password])) {
                $student->role_id = 3;
                $student->organisation_id = $organisation_id;

                $profile = $student->profile;

                $student->save();
                $student->profile()->save($profile);
                $user = Student::find($student->id);
                if ($organisation_id) {
                    event(new StudentRegistered($user, true));  // Second argument true in case of organisation
                }
                if ($request->session()->has('data.campus_id')) {
                    $student->orgCampuses()->sync($request->session()->get('data.campus_id'));
                }

                $organisation = $student->organisation;
                if ($organisation->detail->total_students == $organisation->account_limit - 20) {
                    Mail::send(new OrgAccountLimitNotification("alert", $organisation));
                } else if ($organisation->detail->total_students == $organisation->account_limit) {
                    Mail::send(new OrgAccountLimitNotification("complete", $organisation));
                }

                if (Auth::attempt(['email' => $student->email, 'password' => $request->password])) {
                    return redirect('/home')->with('confirmation', "Your account has been successfully connected to your organisation.");
                }
            }

            // $school = Organisation::find($request->session()->get('data.organisation_id'));
            // $request->session()->flash('message', 'You have entered a wrong password');
            // return view('auth.connectschool', compact('student', 'school'));
            session()->put('data.connect', true);
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'You have entered a wrong password');
        } else {
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', "Sorry! Your organisation has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.");
        }
    }
}
