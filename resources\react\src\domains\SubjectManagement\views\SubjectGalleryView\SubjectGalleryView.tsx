import {
    useAddGalleryImage,
    useAddGalleryItem,
    useGetSubjectGallery,
    useOrderSubjectGallery,
    useRemoveGalleryItems,
    useUpdateGalleryImage,
    useUpdateGalleryItem,
} from '@/domains/SubjectSelection/consumers/useSubjectsApiConsumer';
import { useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import DebouncedInput from '../../components/DebouncedInput/DebouncedInput';
import useModal from '@/hooks/useModal/useModal';
import GalleryItemModalBody from '../../components/GalleryItemModalBody/GalleryItemModalBody';
import {
    GalleryType,
    SubjectGalleryItemRequest,
    SubjectGalleryItemResponse,
} from '../../models/Subjects.service.api.model';
import Gallery from '@/components/Gallery/Gallery';
import GalleryDragAndDrop from '../../components/GalleryDragAndDrop/GalleryDragAndDrop';
import { useSubjects } from '@/domains/SubjectSelection/consumers/useSubjectSelectionQuizApiConsumer';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import { truncateText } from '@/utils/helpers';

export default function SubjectGalleryView() {
    const [search, setSearch] = useState('');
    const [isEdit, setIsEdit] = useState(false);
    const [selectedItemIds, setSelectedItemIds] = useState([] as number[]);
    const [itemsForOrderChange, setItemsForOrderChange] = useState([] as SubjectGalleryItemResponse[]);
    const [itemForEdit, setItemForEdit] = useState<SubjectGalleryItemResponse>();
    const { data: subjects } = useSubjects();

    const itemForDeletion = useRef<any>();

    const { subjectId } = useParams<{ subjectId: string }>();

    const {
        isLoading: isGalleryLoading,
        isRefetching: isGalleryRefetching,
        data: galleryData,
    } = useGetSubjectGallery({ subjectId: Number(subjectId), filter: { search } });

    const addGalleryItem = useAddGalleryItem();
    const addGalleryImage = useAddGalleryImage();

    const updateGalleryItem = useUpdateGalleryItem();
    const updateGalleryImage = useUpdateGalleryImage();

    const orderSubjectGallery = useOrderSubjectGallery();
    const deleteGalleryItem = useRemoveGalleryItems();

    const {
        isOpen: isAddItem,
        Modal: AddItemModal,
        toggle: toggleAddItem,
    } = useModal({
        title: 'Add Gallery Item',
    });

    const {
        isOpen: isDelete,
        Modal: DeleteModal,
        toggle: toggleDelete,
    } = useModal({
        title: 'Delete Gallery Item',
    });

    const {
        isOpen: isDeleteMultiple,
        Modal: DeleteMultipleModal,
        toggle: toggleDeleteMultiple,
    } = useModal({
        title: 'Delete Multiple Gallery Items',
    });

    const handleSubmitItem = (data: SubjectGalleryItemRequest) => {
        const selectedItemForEdit = JSON.stringify(itemForEdit);
        const incomingDataFromModal = JSON.stringify(data);

        if (selectedItemForEdit === incomingDataFromModal) {
            toggleAddItem();
            return;
        }

        if (itemForEdit) {
            processEditingGalleryItem(data, itemForEdit);
            return;
        }

        processAddingGalleryItem(data);
    };

    function processAddingGalleryItem(data: SubjectGalleryItemRequest) {
        if (data.type === GalleryType.Image) {
            addGalleryImage.mutate({ subjectId: Number(subjectId), data });
            toggleAddItem();
            return;
        }

        addGalleryItem.mutate({ subjectId: Number(subjectId), data });
        toggleAddItem();
    }

    function processEditingGalleryItem(data: SubjectGalleryItemRequest, itemForEdit: SubjectGalleryItemResponse) {
        if (data.type === GalleryType.Image) {
            updateGalleryImage.mutate({ subjectId: Number(subjectId), data, galleryItemId: itemForEdit.id });
            toggleAddItem();
            return;
        }

        updateGalleryItem.mutate({ subjectId: Number(subjectId), data, galleryItemId: itemForEdit.id });
        setItemForEdit(undefined);
        toggleAddItem();
    }

    const handleCancelModal = () => {
        toggleAddItem();
        setItemForEdit(undefined);
    };

    const handleEdit = () => {
        galleryData && changeItemOrder(galleryData, itemsForOrderChange);
        setItemForEdit(undefined);
        setIsEdit(!isEdit);
    };

    const handleCheckboxClick = (item: SubjectGalleryItemResponse) => {
        let selectedItemIdsCopy = [...selectedItemIds];

        if (selectedItemIds.includes(item.id)) {
            selectedItemIdsCopy = selectedItemIdsCopy.filter((id) => id !== item.id);
        } else {
            selectedItemIdsCopy = [...selectedItemIdsCopy, item.id];
        }

        setSelectedItemIds(selectedItemIdsCopy);
    };

    function changeItemOrder(
        galleryData: SubjectGalleryItemResponse[],
        itemsForOrderChange: SubjectGalleryItemResponse[],
    ) {
        if (!!!itemsForOrderChange.length) return;

        const galleryItemIds = galleryData.map((item) => item.id);
        const itemsForOrderChangeIds = itemsForOrderChange.map((item) => item.id);

        const galleryItemIdsOrder = JSON.stringify(galleryItemIds);
        const newGalleryItemIdsOrder = JSON.stringify(itemsForOrderChangeIds);

        if (galleryItemIdsOrder === newGalleryItemIdsOrder) return;

        orderSubjectGallery.mutate({
            subjectId: Number(subjectId),
            subjectIds: itemsForOrderChangeIds.map((item) => ({ id: item })),
        });
    }

    const handleEditItem = (item: SubjectGalleryItemResponse) => {
        setItemForEdit(item);
        toggleAddItem();
    };

    const handleItemOrderChange = (items: SubjectGalleryItemResponse[]) => {
        setItemsForOrderChange(items);
    };

    const handleDeleteModal = (item: SubjectGalleryItemResponse) => {
        itemForDeletion.current = item;
        toggleDelete();
    };

    const handleDeleteItem = () => {
        deleteGalleryItem.mutate({
            subjectId: Number(subjectId),
            galleryItemIds: [itemForDeletion.current.id],
        });

        itemForDeletion.current = undefined;
        toggleDelete();
    };

    const handleDeleteMultipleItems = () => {
        deleteGalleryItem.mutate({
            subjectId: Number(subjectId),
            galleryItemIds: selectedItemIds,
        });

        setSelectedItemIds([]);
        toggleDeleteMultiple();
    };

    const subject = subjects?.find((subject) => subject.id === Number(subjectId));

    return (
        <div className='h-100 subject-gallery-view'>
            <div
                style={{
                    maxWidth: '1700px',
                    margin: 'auto',
                    overflow: 'auto',
                }}
            >
                <h1>
                    Manage Gallery{' '}
                    {subject && (
                        <>
                            -{' '}
                            <span>
                                <span>{subject.subject_type.name}</span> <span>{subject.title}</span>
                            </span>
                        </>
                    )}
                </h1>
                <div className='d-flex w-100 justify-content-between' style={{ padding: '20px 0' }}>
                    <div className='d-flex'>
                        <DebouncedInput
                            value={search ?? ''}
                            onChange={(value) => {
                                setSearch(String(value));
                            }}
                            className='p-2 font-lg shadow border border-block'
                            placeholder='Search gallery items'
                        />
                        {isGalleryLoading && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Loading gallery items...</div>
                                </div>
                            </div>
                        )}
                        {isGalleryRefetching && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Refreshing gallery items...</div>
                                </div>
                            </div>
                        )}
                        {(addGalleryImage.isLoading || addGalleryItem.isLoading) && !isGalleryRefetching && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Processing new added item...</div>
                                </div>
                            </div>
                        )}
                        {(updateGalleryImage.isLoading || updateGalleryItem.isLoading) && !isGalleryRefetching && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Processing your update...</div>
                                </div>
                            </div>
                        )}
                        {orderSubjectGallery.isLoading && !isGalleryRefetching && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Processing reorder...</div>
                                </div>
                            </div>
                        )}
                        {deleteGalleryItem.isLoading && !isGalleryRefetching && (
                            <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                                <div className='d-flex'>
                                    <div className='spinner-border me-3 text-primary' role='status' />
                                    <div className='fs-5 d-flex align-items-center'>Processing deletion...</div>
                                </div>
                            </div>
                        )}
                    </div>
                    <div className='action-buttons' style={{ gap: 10, display: 'flex', alignItems: 'center' }}>
                        {!!selectedItemIds.length && (
                            <button
                                className='delete fs-6'
                                style={{
                                    width: '140px',
                                    height: '45px',
                                    border: 'none',
                                    textTransform: 'uppercase',
                                    color: '#7f8297',
                                }}
                                onClick={toggleDeleteMultiple}
                            >
                                Delete
                            </button>
                        )}
                        {!!galleryData?.length && (
                            <button
                                className='edit fs-6'
                                onClick={handleEdit}
                                style={{
                                    width: '140px',
                                    height: '45px',
                                    border: 'none',
                                    textTransform: 'uppercase',
                                    color: '#7f8297',
                                    ...(isEdit ? { color: '#FFFFFF', backgroundColor: '#0062ff' } : {}),
                                }}
                            >
                                {isEdit ? 'Finish' : 'Edit'}
                            </button>
                        )}
                        <button
                            className='add fs-6'
                            onClick={toggleAddItem}
                            style={{
                                width: '140px',
                                height: '45px',
                                border: 'none',
                                textTransform: 'uppercase',
                                color: '#7f8297',
                            }}
                        >
                            Add
                        </button>
                    </div>
                </div>
                <div>
                    {!!!galleryData?.length && (
                        <div className='d-flex align-items-center justify-content-center'>
                            <div className='fs-5 d-flex align-items-center'>No gallery items found.</div>
                        </div>
                    )}
                    {!!galleryData?.length && isEdit && (
                        <GalleryDragAndDrop
                            data={galleryData}
                            selectedItemIds={selectedItemIds}
                            onItemOrderChange={handleItemOrderChange}
                            onEdit={handleEditItem}
                            onSelect={handleCheckboxClick}
                            onDelete={handleDeleteModal}
                        />
                    )}
                    {!!galleryData?.length && !isEdit && (
                        <Gallery>
                            {galleryData?.map((item) => {
                                switch (item.type) {
                                    case GalleryType.Youtube:
                                    case GalleryType.Vimeo:
                                        return (
                                            <a
                                                key={item.id}
                                                data-lg-size='1280-720'
                                                data-src={item.src}
                                                data-poster={item.thumbnail}
                                                data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                            >
                                                <img alt='thumbnail' src={item.thumbnail} className='img-responsive' />
                                            </a>
                                        );
                                    case GalleryType.Wistia:
                                        return (
                                            <a
                                                key={item.id}
                                                data-lg-size='1280-720'
                                                data-src={item.src}
                                                data-poster=''
                                                data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                            >
                                                <div
                                                    style={{
                                                        height: 262,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        backgroundColor: '#000000',
                                                    }}
                                                >
                                                    <span className='fw-bold fs-4 iframe-title'>
                                                        {truncateText(item.caption, 50)}
                                                    </span>
                                                </div>
                                            </a>
                                        );
                                    case GalleryType.Image:
                                        return (
                                            <a
                                                key={item.id}
                                                data-src={item.src}
                                                data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                            >
                                                <img alt='thumbnail' src={item.src} className='img-responsive' />
                                            </a>
                                        );
                                    case GalleryType.Iframe:
                                        return (
                                            <a
                                                key={item.id}
                                                data-iframe='true'
                                                data-src={item.src}
                                                data-iframe-title={item.caption}
                                                data-sub-html={`
                                            <h4>${item.caption}</h4><p>${item.description}</p>
                                            `}
                                            >
                                                <div
                                                    style={{
                                                        height: 262,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        backgroundColor: '#000000',
                                                    }}
                                                >
                                                    <span className='fw-bold fs-4 iframe-title'>
                                                        {truncateText(item.caption, 50)}
                                                    </span>
                                                </div>
                                            </a>
                                        );
                                    default:
                                        return <a>nothing</a>;
                                }
                            })}
                        </Gallery>
                    )}
                </div>
                {isAddItem && (
                    <AddItemModal>
                        <GalleryItemModalBody
                            onCancel={handleCancelModal}
                            onSubmit={handleSubmitItem}
                            itemForEdit={itemForEdit}
                        />
                    </AddItemModal>
                )}
                {isDelete && (
                    <DeleteModal>
                        <p>Are you sure you want to delete the item?</p>
                        <div className='d-flex' style={{ width: 200, marginLeft: 'auto' }}>
                            <Button
                                className='me-3'
                                type={ButtonType.OUTLINED_BLACK}
                                title={'Yes'}
                                spanClassName='fs-6'
                                element='button'
                                onClick={handleDeleteItem}
                            />
                            <Button
                                type={ButtonType.OUTLINED_BLACK}
                                title={'Cancel'}
                                spanClassName='fs-6'
                                element='button'
                                onClick={() => {
                                    itemForDeletion.current = undefined;
                                    toggleDelete();
                                }}
                            />
                        </div>
                    </DeleteModal>
                )}
                {isDeleteMultiple && (
                    <DeleteMultipleModal>
                        <p>Are you sure you want to delete all selected items?</p>
                        <div className='d-flex' style={{ width: 200, marginLeft: 'auto' }}>
                            <Button
                                className='me-3'
                                type={ButtonType.OUTLINED_BLACK}
                                title={'Yes'}
                                spanClassName='fs-6'
                                element='button'
                                onClick={handleDeleteMultipleItems}
                            />
                            <Button
                                type={ButtonType.OUTLINED_BLACK}
                                title={'Cancel'}
                                spanClassName='fs-6'
                                element='button'
                                onClick={() => {
                                    toggleDeleteMultiple();
                                    setSelectedItemIds([]);
                                }}
                            />
                        </div>
                    </DeleteMultipleModal>
                )}
            </div>
        </div>
    );
}
