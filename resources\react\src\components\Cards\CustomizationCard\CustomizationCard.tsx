import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';

interface CustomizationCardProps {
    text: string;
    onClick: () => void;
    classNames?: string;
    style?: {};
}

const CustomizationCard = ({ text, classNames, style, onClick }: CustomizationCardProps) => {
    return (
        <div className={`customization-card ${classNames}`} style={style}>
            <div className='customization-card__container'>
                <h1 className='mb-6'>{text}</h1>
                <Button title='Manage' type={ButtonType.GRAY} onClick={onClick} className='fw-normal fs-6' />
            </div>
        </div>
    );
};

export default CustomizationCard;
