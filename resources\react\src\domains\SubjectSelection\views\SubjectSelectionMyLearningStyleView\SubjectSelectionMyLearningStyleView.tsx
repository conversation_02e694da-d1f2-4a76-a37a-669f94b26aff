import useSelectionNavigation from '../../utils/useSelectionNavigation';
import { SelectionSteps } from '../../models/SubjectSelection.model';
import StepLayout from '../../components/StepLayout/StepLayout';
import copy from '../../utils/content.json';
import { setQuizSelection } from '../../context/SubjectSelection.actions';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';
import { useQuiz } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import LabelRadio from '@/components/base/LabelRadio/LabelRadio';

export default function SubjectSelectionMyLearningStyleView() {
    useSelectionNavigation({ currentStep: SelectionSteps.STUDY_HABITS });
    const { data } = useQuiz();
    const { quizSelection } = useSubjectSelectionContextStore();
    const dispatch = useSubjectSelectionDispatch();

    const content = copy.subjectSelection[SelectionSteps.STUDY_HABITS];

    const handleCheckboxClick = (id: number) => {
        dispatch(setQuizSelection({ key: SelectionSteps.STUDY_HABITS, value: id }));
    };

    return (
        <div className='learning-styles-view'>
            <StepLayout title={content.title} description={content.description} progress={content.progress}>
                <div className='learning-styles-view__list-container'>
                    {data?.study_habits.map((learningStyle) => {
                        const isSelected = quizSelection?.study_habits.includes(learningStyle.id);
                        return (
                            <LabelRadio
                                key={learningStyle.id}
                                id={learningStyle.id}
                                text={learningStyle.name}
                                onClick={() => handleCheckboxClick(learningStyle.id)}
                                selected={isSelected}
                            />
                        );
                    })}
                </div>
            </StepLayout>
        </div>
    );
}
