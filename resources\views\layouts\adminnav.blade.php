@if (!Auth::user()->isAdmin())
    <style>
        /* body:not(.no-grey-bg) .page-content-wrapper,
        body:not(.no-grey-bg) .header {
            background: #fff;
            background: linear-gradient(left, #f4f4f4, #f4f4f4 35%, #fff 35%, #fff 100%);
            background: -webkit-linear-gradient(left, #f4f4f4, #f4f4f4 35%, #fff 35%, #fff 100%);
            background: -moz-linear-gradient(left, #f4f4f4, #f4f4f4 35%, #fff 20%, #fff 100%)
        } */

        body.mobile .page-sidebar .sidebar-menu .menu-items li:hover a {
            color: #fff;
        }

        .page-sidebar {
            background-color: #000;
        }

        .scroll-wrapper>.scroll-content {
            -ms-overflow-style: none;
        }

        .page-sidebar .sidebar-menu {
            height: calc(100% - 100px);
        }

        .page-sidebar .sidebar-header {
            background-color: transparent;
            border-bottom: none;
        }

        .page-sidebar .sidebar-menu .menu-items {
            padding-left: 40px !important;
            padding-right: 10px !important;
        }


        .page-sidebar .sidebar-menu .menu-items * {
            padding: 0 !important;
        }

        .page-sidebar a,
        .page-sidebar button,
        .page-sidebar a:focus,
        .page-sidebar a:visited,
        .page-sidebar button:focus,
        .page-sidebar button:visited,
        .child-name {
            color: #fff;
        }

        .page-sidebar .sidebar-menu .menu-items li.disabled>a {
            opacity: .5;
            pointer-events: none;
            cursor: not-allowed;
        }

        .page-sidebar .sidebar-menu .menu-items>li.active>a>.title,
        .page-sidebar .sidebar-menu .menu-items li:not(.disabled)>a>.title:hover,
        /* .page-sidebar .sidebar-menu .menu-items>li.active>a, */
        .page-sidebar .sidebar-menu .menu-items li:not(.disabled)>a:hover,
        .page-sidebar .sidebar-menu .menu-items>li.active>a>.level-one-menu.have-child,
        .page-sidebar .sidebar-menu .menu-items li:not(.disabled)>a>.level-one-menu:hover {
            font-weight: bold;
        }

        ul.menu-items li:first-child {
            margin-top: 70px;
        }

        .page-sidebar .sidebar-menu .menu-items ul>li {
            line-height: normal;
            padding: 3px 0 !important;
        }


        .page-sidebar .sidebar-menu .menu-items>li ul.sub-menu {
            background-color: transparent;
            margin-bottom: 0;
            margin-top: 5px;
        }

        .page-sidebar .sidebar-menu .menu-items>li>a {
            min-height: unset;
            line-height: normal;
            width: 100%;
        }

        .page-sidebar .sidebar-menu .menu-items>li>a>.title {
            width: 100%;
            letter-spacing: 2px;
            font-family: 'Oswald', sans-serif;
            text-transform: uppercase;
            font-size: 18px;
            margin-top: 10px;
        }

        .page-sidebar .sidebar-menu .menu-items>li ul.item-sub-menu .level-two-menu {
            margin-left: 10px;
        }

        .page-sidebar .sidebar-menu .menu-items>li.active>a>.sub-title::before,
        .page-sidebar .sidebar-menu .menu-items>li.active>a>.sub-title::before,
        .page-sidebar .sidebar-menu .menu-items>li ul.sub-menu:not(.item-sub-menu)>li.active>a::before,
        .page-sidebar .sidebar-menu .menu-items>li ul.item-sub-menu>li.active>a>div::before,
        .page-sidebar .sidebar-menu .menu-items>li.active>a>div:not(.title):not(.have-child)::before,
        .page-sidebar .sidebar-menu .menu-items>li.active>a>div.arrow-on-active::before {
            content: "\f061";
            font-family: 'FontAwesome';
            font-size: 9px;
            margin-right: 5px;
            vertical-align: 1px;
        }

        .page-sidebar .sidebar-menu .menu-items>li.active>a>div.arrow-on-active::before {
            vertical-align: 4px;
        }

        .page-sidebar .sidebar-menu .menu-items>li.active>a>.sub-title {
            font-weight: normal;
        }

        .page-sidebar .sidebar-menu .menu-items>li ul.sub-menu>li {
            padding: 3px 0 !important;
        }

        .page-sidebar .sidebar-menu .menu-items>li ul.sub-menu>li>a,
        .page-sidebar .sidebar-menu .menu-items>li .level-one-menu {
            font-family: 'Roboto', sans-serif;
            font-size: 13px;
            width: 100%;
        }


        .menu-play-icon {
            width: 17px;
            margin-right: 10px;
            vertical-align: sub;
        }

        .menu-social-icons>li {
            padding: 0;
        }

        .menu-social-icons>li+li {
            margin-left: 5px;
        }

        .menu-social-icons>li>a {
            width: 19px;
            height: 19px;
            display: inline-block;
        }

        .content>.container,
        .content>.container-fluid {
            padding-top: 0px;
        }

        .sidebar-collapse {
            transform: rotate(90deg);
            transform-origin: right bottom;
            cursor: pointer;
            float: right;
            margin-right: 30px !important;
        }

        .sidebar-expand {
            position: absolute;
            top: 50%;
            transform: rotate(90deg);
            cursor: pointer;
        }

        .expand {
            width: 50px !important;
        }

        .p-l-50 {
            padding-left: 50px;
        }

        .teacher-section-btn {
            border: none !important;
            color: #000;
        }

        .btn-complete.active {
            color: #000 !important;
            background-color: #fff !important;
            border-radius: 0% !important;
        }

        .btn-group>a {
            border: none !important;
        }

        .m-t-20-minus {
            margin-top: -20px;
        }

        .page-container .page-content-wrapper .content,
        .page-sidebar {
            -webkit-transition: all 0s linear;
            transition: all 0s linear;
        }

        @media (min-width: 992px) {
            body.menu-pin .page-container .page-content-wrapper .content {
                padding-top: 0;
            }
        }

        @media (max-width: 991px) {
            body.sidebar-open .page-container {
                -webkit-transform: translate3d(140px, 0, 0);
                transform: translate3d(140px, 0, 0);
                -ms-transform: translate(140px, 0);
                overflow: hidden;
                position: fixed;
            }

            .page-sidebar {
                z-index: 9;
                width: 140px
            }

            body.menu-behind .page-sidebar {
                z-index: 0;
            }

            body.menu-pin .page-sidebar {
                transform: none !important;
                -webkit-transform: none !important;
                -ms-transform: none !important;
            }

            body.menu-pin .page-container .page-content-wrapper .content {
                padding-left: 0;
            }

            .page-sidebar .sidebar-menu {
                height: calc(100% - 80px);
            }
        }
    </style>
@else
    <style>
        .page-sidebar .sidebar-menu {
            padding-bottom: 50px;
        }
    </style>
@endif
<nav class="page-sidebar expand">
    <div class="mr-2 text-white sidebar-expand">
        <p>EXPAND</p>
    </div>
</nav>
<nav class="page-sidebar myNav" data-pages="sidebar">
    <!-- BEGIN SIDEBAR MENU TOP TRAY CONTENT-->
    <div class="sidebar-overlay-slide from-top" id="appMenu"></div>
    <!-- END SIDEBAR MENU TOP TRAY CONTENT-->
    <!-- BEGIN SIDEBAR MENU HEADER-->
    @if (Auth::user()->isAdmin())
        <div class="sidebar-header p-t-5">
            <img src="{{ asset('images/favicon.png') }}" alt="logo" class="brand hidden-md-up" data-src="{{ asset('images/favicon.png') }}" data-src-retina="{{ asset('images/favicon.png') }}" width="50" height="50">
        </div>
    @elseif(Auth::user()->isParent() || Auth::user()->isStudent() || Auth::user()->isTeacher())
    @else
        <div class="padding-25 hidden-md-down">
            <img src="{{ asset('images/TCD-stacked-logo-white.png') }}" alt="logo" class="img-fluid" data-src="{{ asset('images/TCD-stacked-logo-white.png') }}" data-src-retina="{{ asset('images/TCD-stacked-logo-white.png') }}">
        </div>
        @if (Auth::user()->isTeacher() && Auth::user()->hasSecondarySchoolAccess() && Auth::user()->hasPrimarySchoolAccess())
            {{-- <div class="px-4 row">
                <div class="btn-group" data-toggle="buttons">
                    <a href="{{ url('schoolSection/primary') }}" class="btn custom-btn btn-white btn-complete @if (session('schoolSection') == 'primary') checked @endif" aria-pressed="true">
                        <input type="radio" name="options" id="primary" @if (session('schoolSection') == 'primary') checked @endif> Primary
                    </a>
                    <a href="{{ url('schoolSection/secondary') }}" class="btn custom-btn btn-white btn-complete @if (session('schoolSection') == 'secondary') checked @endif" aria-pressed="true">
                        <input type="radio" name="options" id="secondary" @if (session('schoolSection') == 'secondary') checked @endif> Secondary
                    </a>
                </div>
            </div> --}}
        @endif
    @endif
    <!-- END SIDEBAR MENU HEADER-->
    <!-- START SIDEBAR MENU -->
    <div class="sidebar-menu">
        {{ Auth::user()->isAdmin() }}
        @if (Auth::user()->isAdmin())
            {!! $sideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu']) !!}
            {{-- @elseif ((Auth::user()->isTeacher() && (session('schoolSection') == 'primary')) || Auth::user()->isCompassTeacher()) --}}
        @elseif ((Auth::user()->isTeacher() && Auth::user()->hasPrimarySchoolAccess() && !Auth::user()->hasSecondarySchoolAccess()) || Auth::user()->isCompassTeacher())
            {{-- {!! $primaryTeacherSideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu item-sub-menu']) !!} --}}
            <x-primaryteachernav />
        @elseif((Auth::user()->isTeacher() || Auth::user()->isStaff()) && !session('studentView') && Auth::user()->hasSecondarySchoolAccess())
            <x-teachernav />
            {{-- {!! $teacherSideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu item-sub-menu']) !!} --}}
        @elseif(Auth::user()->isStudent() || ((Auth::user()->isTeacher() || Auth::user()->isStaff()) && session('studentView')))
            {{-- @if (Auth::user()->isStudent()) --}}
            <x-studentnav />
            {{-- @else

            {!! $studentSideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu']) !!}
            @endif --}}
        @elseif(Auth::user()->isParent())
            @if (Auth::user()->profile->premium_access == 1)
                <x-premiumparentnav />
            @else
                <x-parentnav />
            @endif
            {{-- {!! $parentSideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu']) !!} --}}
        @elseif(Auth::user()->isMarker())
            {!! $markerSideNavbar->asUl(['class' => 'menu-items'], ['class' => 'sub-menu']) !!}
        @endif
        <!-- BEGIN SIDEBAR MENU ITEMS-->
        <div class="clearfix"></div>
    </div>
    <!-- END SIDEBAR MENU -->
    <div class="aside-footer flex-column-auto p-t-50" id="kt_app_sidebar_footer">
        {{-- <a href="https://help.thecareersdepartment.com/en/" class="px-0 pb-20 overflow-hidden btn btn-flex flex-center btn-custom btn-primary text-nowrap h-40px w-100" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click" title="200+ in-house components and 3rd-party plugins">
            <span class="btn-label"></span>
            <span class="m-0 svg-icon btn-icon svg-icon-2 footer-icon">
                <svg viewBox="0 0 24 24" width="19.5" height="19.5" xmlns="http://www.w3.org/2000/svg">
                    <path xmlns="http://www.w3.org/2000/svg" d="M16,4A12,12,0,1,0,28,16,12.013,12.013,0,0,0,16,4Zm0,2A10,10,0,1,1,6,16,9.984,9.984,0,0,1,16,6Zm0,4a4.013,4.013,0,0,0-4,4h2a2,2,0,0,1,4,0,1.781,1.781,0,0,1-1.219,1.688l-.406.125A2.021,2.021,0,0,0,15,17.719V19h2V17.719l.406-.125A3.807,3.807,0,0,0,20,14,4.013,4.013,0,0,0,16,10ZM15,20v2h2V20Z" transform="translate(-4 -4)" fill="#707070"></path>
                </svg>
            </span>
        </a> --}}
        @if (!Auth::user()->isAdmin() && !Auth::user()->isParent() && !Auth::user()->isStudent() && !Auth::user()->isTeacher())
            <div class="sidebar-footer p-l-40">
                {{-- <div class="text-white"><a href="#" data-target="#modalHowItWorks" data-toggle="modal"><img src="{{asset('images/icon-play-circle.svg')}}" alt="" class="menu-play-icon"><span class="uppercase oswald lh-normal fs-15">How it works</span></a></div> --}}
                <ul class="list-inline mb-0  @if (Auth::user()->isTeacher() && Auth::user()->hasSecondarySchoolAccess() && Auth::user()->hasPrimarySchoolAccess()) m-t-20-minus @else m-t-20 @endif menu-social-icons">
                    <li><a href="{{ config('sociallinks.facebook') }}" target="_blank"><img src="{{ asset('images/icon-facebook-circle.svg') }}" alt="facebook" class="img-fluid">{{-- <i class="fa fa-facebook"></i> --}}</a></li>
                    <li><a href="{{ config('sociallinks.twitter') }}" target="_blank"><img src="{{ asset('images/icon-twitter-circle.svg') }}" alt="twitter" class="img-fluid">{{-- <i class="fa fa-twitter"></i> --}}</a></li>
                    <li><a href="{{ config('sociallinks.instagram') }}" target="_blank"><img src="{{ asset('images/icon-instagram-circle.svg') }}" alt="instagram" class="img-fluid">{{-- <i class="fa fa-instagram"></i> --}}</a></li>
                </ul>
            </div>
            @if (!Auth::user()->isParent() && !Auth::user()->isStudent() && !Auth::user()->isTeacher())
                <div class="mr-2 text-white sidebar-collapse">
                    <p>COLLAPSE</p>
                </div>
            @endif
        @endif
</nav>
