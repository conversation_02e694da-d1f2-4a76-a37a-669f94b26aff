import CustomizationCard from '@/components/Cards/CustomizationCard/CustomizationCard';
import ProgressCard from '@/components/Cards/ProgressCard/ProgressCard';
import FileUpload from '@/components/base/FileUpload/FileUpload';
import useModal from '@/hooks/useModal/useModal';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { STUDENT_MANAGEMENT, SUBJECT_MANAGEMENT } from '@/router/Router.constants';
import User from '@/assets/icons/subjectSelection/User';
// import Users from '@/assets/icons/subjectSelection/Users';
import Book from '@/assets/icons/subjectSelection/Book';
import {
    useDeleteSchoolGuides,
    useSchoolGuides,
    useStatistics,
    useUploadSchoolGuides,
} from '../../consumers/useSubjectManagementApiConsumer';
import Chart from './Chart';
import classNames from 'classnames';
import { log } from '@/services/Logger.service';
import Trash from '@/assets/icons/subjectSelection/Trash';
import ManageRulesModalBody from '../../components/ManageRulesModalBody/ManageRulesModalBody';
import { useSchoolReport } from '../../consumers/useSchoolsApiConsumer';
import { SchoolReport } from '../../models/Schools.service.api.model';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import FileExport from '@/assets/icons/subjectSelection/FileExport';

enum ChartType {
    SubjectArea = 'Subject Area',
    Subjects = 'Subjects',
    SubjectType = 'Subject Type',
}

enum PdfTypes {
    SubjectSelectionGuide = 'subject_selection_guide',
    SubjectSelectionRules = 'subject_selection_rules',
}

const SubjectManagementStatisticsView = () => {
    const navigate = useNavigate();
    const [title, setTitle] = useState('Upload PDF');
    const [pdfModalId, setPdfModalId] = useState<PdfTypes>();
    const [bodyTitle, setBodyTitle] = useState('Upload PDF');
    const [selectedFileName, setSelectedFileName] = useState<string | undefined>();
    const selectedFiles = useRef<FileList | null>();
    const formData = useRef(new FormData());
    const deletionId = useRef<number | null>(null);
    const [selectedChartData, setSelectedChartData] = useState<{
        data: { name: string; count: number }[];
        title: string;
        subtitle: string;
    }>({
        data: [],
        title: '',
        subtitle: '',
    });
    const { data: user } = useUserData();
    const { isLoading: isLoadingGuides, data: schoolGuides, isRefetching } = useSchoolGuides();
    const { isLoading, data: statistics } = useStatistics();

    const schoolGuideMutation = useUploadSchoolGuides();
    const schoolGuideDeleteMutation = useDeleteSchoolGuides();
    const schoolReportMutation = useSchoolReport();

    useEffect(() => {
        if (!statistics) return;
        setSelectedChartData({
            data: statistics.subject_area,
            title: ChartType.SubjectArea,
            subtitle: 'Number of students shortlisting subjects by Subject Area',
        });
    }, [isLoading, statistics]);

    const { isOpen, Modal, toggle } = useModal({
        title,
        onClose: () => {
            formData.current = new FormData();
            selectedFiles.current = null;
            setSelectedFileName(undefined);
        },
    });

    const {
        isOpen: isManageRules,
        Modal: ManageRulesModal,
        toggle: toggleManageRules,
    } = useModal({
        title: 'Manage Rules',
    });

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const fileNames: string[] = [];
        event?.target?.files &&
            Array.from(event?.target?.files).forEach((file) => {
                fileNames.push(file.name);
            });

        setSelectedFileName(fileNames.join(', '));

        log().debug({ message: `adding selected files to selectedFiles ref` });

        selectedFiles.current = event.target.files;
    };

    const handleManageSubjectSelectionGuide = () => {
        setPdfModalId(PdfTypes.SubjectSelectionGuide);
        setTitle('Manage Subject Selection Guide');
        setBodyTitle('');
        toggle();
    };

    const handleSubjectSelectionRules = () => {
        setPdfModalId(PdfTypes.SubjectSelectionRules);
        setTitle('Manage Rules');
        setBodyTitle(
            'Help your students during their subject selections process by keeping their guidelines front and centre. ',
        );
        toggle();
    };

    const handleChartSelection = (selected: string) => {
        switch (selected) {
            case ChartType.SubjectArea:
                setSelectedChartData({
                    data: statistics?.subject_area || [],
                    title: ChartType.SubjectArea,
                    subtitle: 'Number of students shortlisting subjects by Subject Area',
                });
                break;
            case ChartType.Subjects:
                setSelectedChartData({
                    data: statistics?.subjects || [],
                    title: ChartType.Subjects,
                    subtitle: 'Number of students shortlisting subjects by Subject Name',
                });
                break;
            case ChartType.SubjectType:
                setSelectedChartData({
                    data: statistics?.subject_type || [],
                    title: ChartType.SubjectType,
                    subtitle: 'Number of students shortlisting subjects by Subject Type',
                });
                break;

            default:
                break;
        }
    };

    const handleFileUpload = async () => {
        log().debug({ message: `Selected file: in state and ref - respectively` });
        console.log(selectedFiles.current);

        log().debug({ message: `adding file to formData` });
        selectedFiles.current &&
            Array.from(selectedFiles.current).forEach((file) => {
                console.log(file);
                formData.current.append('attachments[]', file);
            });

        schoolGuideMutation.mutate(formData.current);
        toggle();
    };

    function handleDeleteGuide(guideId: number) {
        deletionId.current = guideId;
        schoolGuideDeleteMutation.mutate(guideId);
    }

    function handleCsvExport() {
        const type: any = {
            [ChartType.SubjectArea]: SchoolReport.SubjectAreas,
            [ChartType.Subjects]: SchoolReport.Subjects,
            [ChartType.SubjectType]: SchoolReport.SubjectTypes,
        };

        user && schoolReportMutation.mutate({ schoolId: user.schoolId, type: type[selectedChartData.title] });
    }

    return (
        <div className='statistics'>
            <div className='statistics-container'>
                <div className='statistics-container__page-information'>
                    <p className='fw-bold'>Subject Selections</p>
                    <div className='statistics-container__breadcrumbs-container'>
                        <p
                            style={{
                                padding: '10px',
                                background: 'white',
                                borderRadius: '6px',
                            }}
                        >
                            Year 10 {new Date().getFullYear()}
                        </p>
                    </div>
                </div>
                <div className='statistics-container__progress-container'>
                    <p className='fw-bold fs-4'>Progress</p>
                    <div className='row gy-5 g-xl-10 mb-4'>
                        <div className='col-sm-6 col-xl-2 mb-xl-10'>
                            <ProgressCard
                                svgIcon={<User />}
                                statisticsNumber={statistics?.completed || 0}
                                text='Students have completed subject selection quiz'
                                classNames='card h-lg-100 rounded-0'
                                onClick={() => navigate(STUDENT_MANAGEMENT)}
                            />
                        </div>
                        <div className='col-sm-6 col-xl-2 mb-xl-10'>
                            <ProgressCard
                                svgIcon={<Book />}
                                statisticsNumber={statistics?.approved || 0}
                                text='Students have submitted their subject preferences'
                                classNames='h-lg-100 rounded-0'
                                onClick={() => navigate(STUDENT_MANAGEMENT)}
                            />
                        </div>
                    </div>
                </div>
                <div className='statistics-container__customization-container'>
                    <div className='d-flex'>
                        <p className='fw-bold fs-4 me-5'>Customisation</p>
                        {schoolGuideMutation.isLoading && <div className='spinner-border text-primary' role='status' />}
                    </div>
                    <div className='row gy-5 g-xl-10 mb-4'>
                        <div className='col-sm-6 col-xl-2 mb-xl-10'>
                            <CustomizationCard
                                text='Manage Subject Options'
                                onClick={() => navigate(SUBJECT_MANAGEMENT)}
                                classNames='h-lg-100 rounded-0'
                            />
                        </div>
                        <div className='col-sm-6 col-xl-2 mb-xl-10'>
                            <CustomizationCard
                                text={
                                    schoolGuideMutation.isLoading && pdfModalId === PdfTypes.SubjectSelectionGuide
                                        ? 'Uploading...'
                                        : 'Manage Subject Selection Guide'
                                }
                                onClick={handleManageSubjectSelectionGuide}
                                classNames='h-lg-100 rounded-0'
                            />
                        </div>{' '}
                        <div className='col-sm-6 col-xl-2 mb-xl-10'>
                            <CustomizationCard
                                text='Manage Subject Selection Rules'
                                onClick={toggleManageRules}
                                classNames='h-lg-100 rounded-0'
                            />
                        </div>
                    </div>
                </div>
                <div className='statistics-container__chart-container'>
                    <p className='fw-bold fs-4'>Reports</p>
                    <div className='col-xl-8'>
                        <div className='d-flex mb-5 space-between'>
                            <div className='d-flex w-100'>
                                {[ChartType.SubjectArea, ChartType.SubjectType].map(
                                    (item, index) => {
                                        const isFirst = index === 0;
                                        const isSelected = selectedChartData.title === item;

                                        const style = classNames({
                                            'mx-3 text-grey cursor-pointer fs-6': !isFirst,
                                            'me-3 text-grey cursor-pointer fs-6': isFirst,
                                            'fw-bold text-black': isSelected,
                                        });

                                        return (
                                            <p
                                                key={item}
                                                className={style}
                                                onClick={() => handleChartSelection(item)}
                                                style={{
                                                    padding: '10px',
                                                    background: 'white',
                                                    borderRadius: '6px',
                                                }}
                                            >
                                                {item}
                                            </p>
                                        );
                                    },
                                )}
                            </div>
                            <div>
                                <button
                                    style={{ borderRadius: 6, padding: 10 }}
                                    className='btn export'
                                    onClick={handleCsvExport}
                                >
                                    <FileExport />
                                    <span className='fs-6 ms-2'>Export</span>
                                </button>
                            </div>
                        </div>
                        {!isLoading && (
                            <Chart
                                data={selectedChartData.data}
                                title={selectedChartData.title}
                                subtitle={selectedChartData.subtitle}
                            />
                        )}
                    </div>
                </div>

                <div>
                    <div className='d-flex'>
                        <p className='fw-bold fs-4 me-5'>School guides</p>
                        {isRefetching && <div className='spinner-border text-primary' role='status' />}
                    </div>

                    <div className='col-xl-8'>
                        <div className='card card-body pt-5'>
                            {isLoadingGuides ? (
                                <div>Loading...</div>
                            ) : schoolGuides && !!!schoolGuides.data.length ? (
                                <div>No guides uploaded</div>
                            ) : (
                                <>
                                    {schoolGuides &&
                                        schoolGuides.data.map((guide) => {
                                            return (
                                                <div key={guide.id}>
                                                    <div className='d-flex flex-stack'>
                                                        <a href={guide.attachment_url} target='_blank'>
                                                            {guide.name}
                                                        </a>
                                                        {schoolGuideDeleteMutation.isLoading &&
                                                        deletionId.current === guide.id ? (
                                                            <div
                                                                className='spinner-border text-primary'
                                                                role='status'
                                                            />
                                                        ) : (
                                                            <div
                                                                className='cursor-pointer'
                                                                onClick={() => handleDeleteGuide(guide.id)}
                                                            >
                                                                <Trash />
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className='separator separator-dashed my-4'></div>
                                                </div>
                                            );
                                        })}
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {isOpen && (
                <Modal>
                    <FileUpload
                        fileName={selectedFileName}
                        name='upload'
                        onChange={handleFileInputChange}
                        title={bodyTitle}
                        isUploading={schoolGuideMutation.isLoading}
                        onOkClick={handleFileUpload}
                    />
                </Modal>
            )}
            {isManageRules && (
                <ManageRulesModal>
                    <ManageRulesModalBody />
                </ManageRulesModal>
            )}
        </div>
    );
};

export default SubjectManagementStatisticsView;
