<template>
    <div class="w-100">
    <div class="pb-10 pb-lg-15 text-center">
      <p class="fw-bold text-dark fs-2x" >Add a Parent/Guardian</p>
      <div class="text-gray-400 fw-semobold fs-6">
        Invite a guardian to join your account
      </div>
    </div>
    <div class="w-100 text-gray-400">
        <div class="fv-row">
            <div class="fv-row mb-3">
                <div class="form-floating ">
                    <Field
                        id="parentemail"
                        class="form-control form-control-lg rounded-0"
                        type="email"
                        placeholder="Guardian/Parent Email"
                        name="parentemail"
                        autocomplete="off"
                        v-model="formData.parentemail"
                    />
                    <label for="parentemail">Guardian/Parent Email</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="parentemail" />
                    </div>
                </div>
            </div>
            <div class="row fv-row pt-3">
                <div class="col-xl-6">
                    <div class="form-floating">
                        <Field
                        id="parentfname"
                        class="form-control form-control-lg rounded-0"
                        type="text"
                        placeholder="First Name"
                        name="parentfname"
                        autocomplete="off"
                        v-model="formData.parentfname"
                        />
                        <label for="parentfname">First Name</label>
                    </div>
                    <div class="fv-plugins-message-container">
                        <div class="fv-help-block">
                            <ErrorMessage name="parentfname" />
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="form-floating">
                        <Field
                        id="parentlname"
                        class="form-control form-control-lg rounded-0"
                        type="text"
                        placeholder="Last Name"
                        name="parentlname"
                        autocomplete="off"
                        v-model="formData.parentlname"
                        />
                        <label for="parentlname">Last Name</label>
                    </div>
                    <div class="fv-plugins-message-container">
                        <div class="fv-help-block">
                            <ErrorMessage name="parentlname" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from "vue";
import { ErrorMessage, Field, Form } from "vee-validate";
import { Actions } from "@/store/enums/StoreEnums";
import { useRegisterStore } from "@/stores/Auth/RegisterStore";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import * as Yup from "yup";

export default defineComponent({
  name: "step-6",
  components: {
    Field,
    Form,
    ErrorMessage,
  },
  props:['formData'],
  setup() {
    const store = useStore();
    const router = useRouter();
    const authStore=useRegisterStore();
    authStore.$reset();
    // const submitButton = ref<HTMLButtonElement | null>(null);
    const passwordField = ref(0);
    const loginProcess = ref(0);
    //Create form validation object
    const addParent = Yup.object().shape({
      email: Yup.string().email().required().label("Email"),
      // password: Yup.string().min(4).required().label("Password"),
    });
    const skip=function(){
      router.push({ name: "dashboard" });
    }
    //Form submit function
    // const onSubmitAddParent = async (values) => {
    //   console.log('checkinvite');
    //   if (submitButton.value) {
    //     // eslint-disable-next-line
    //     submitButton.value!.disabled = true;
    //     // Activate indicator
    //     submitButton.value.setAttribute("data-kt-indicator", "on");
    //   }

    //   await store.dispatch(Actions.PARENT_REGISTER, values);

    //   const [errorName] = Object.keys(store.getters.getErrors);
    //   const error = store.getters.getErrors[errorName];

    //   if (!error) {
    //     router.push({ name: "dashboard" });
    //   } else {
    //     Swal.fire({
    //           text: error[0],
    //           icon: "error",
    //           buttonsStyling: false,
    //           confirmButtonText: "Try again!",
    //           customClass: {
    //             confirmButton: "btn fw-semobold btn-light-danger",
    //           },
    //         });
    //   }

    //   //Deactivate indicator
    //   submitButton.value?.removeAttribute("data-kt-indicator");
    //   // eslint-disable-next-line
    //     submitButton.value!.disabled = false;
    // };

    return {
    //   onSubmitAddParent,
      addParent,
    //   submitButton,
      passwordField,
      loginProcess,
      skip
    };
  },
});
</script>
