import Select from 'react-select';
import {
    // useExportSchoolSubjects,
    useSchoolSubjects,
    useSubjectManagementFilters,
    useToggleSubjectVisibility,
} from '../../consumers/useSchoolsApiConsumer';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    useReactTable,
} from '@tanstack/react-table';
import { SchoolSubjectDetailsResponse } from '../../models/Schools.service.api.model';
import IndeterminateCheckbox from '../IndeterminateCheckbox/IndeterminateCheckbox';
import DebouncedInput from '../DebouncedInput/DebouncedInput';
import Table from '@/components/base/Table/Table';
import Filter from '@/components/Filter/Filter';
import classNames from 'classnames';
import { SUBJECT_DETAILS, SUBJECT_MANAGEMENT_GALLERY } from '@/router/Router.constants';
import { generatePath, useNavigate } from 'react-router-dom';
import FileExport from '@/assets/icons/subjectSelection/FileExport';
import FilterIcon from '@/assets/icons/subjectSelection/FilterIcon';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';

enum FilterKeys {
    SUBJECT_TYPE = 'subject_type',
    SUBJECT_AREA = 'subject_area',
    STATE = 'state',
    STANDARD = 'standard',
    VISIBILITY = 'visibility',
}

export default function SubjectManagementTable() {
    const yearsRef = useRef<any>();
    const subjectTypeRef = useRef<any>();
    const subjectAreaRef = useRef<any>();

    const navigate = useNavigate();
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [search, setSearch] = useState('');

    const { data: user } = useUserData();

    const [rowSelection, setRowSelection] = useState({});
    const [globalFilter, setGlobalFilter] = useState('');

    const [isBulkAction, setIsBulkAction] = useState(false);
    const [isFilter, setIsFilter] = useState(false);
    const [actionsRow, setActionsRow] = useState<number | undefined>();

    const mutation = useToggleSubjectVisibility();

    const [selectedFilters, setSelectedFilters] = useState(new Map());
    const [queryParams, setQueryParams] = useState({});

    // const { isLoading: isRequestingCsv, mutate: requestCsvExport } = useExportSchoolSubjects();
    const { data: filters } = useSubjectManagementFilters();

    const {
        isLoading,
        data: subjects,
        isRefetching,
    } = useSchoolSubjects({
        schoolId: user ? user.schoolId : null,
        pagination: {
            page,
            per_page: perPage,
        },
        filter: {
            search,
            ...queryParams,
        },
    });

    const [data, setData] = useState(() => [...(subjects?.data || [])]);

    useEffect(() => {
        if (!isLoading) {
            subjects && setData([...subjects?.data]);
        }
    }, [isLoading, subjects]);

    const columns = useMemo<ColumnDef<SchoolSubjectDetailsResponse>[]>(() => {
        return [
            {
                id: 'select',
                header: ({ table }) => (
                    <IndeterminateCheckbox
                        {...{
                            checked: table.getIsAllRowsSelected(),
                            indeterminate: table.getIsSomeRowsSelected(),
                            onChange: table.getToggleAllRowsSelectedHandler(),
                        }}
                    />
                ),
                cell: ({ row }) => (
                    <div className='px-1'>
                        <IndeterminateCheckbox
                            {...{
                                checked: row.getIsSelected(),
                                disabled: !row.getCanSelect(),
                                indeterminate: row.getIsSomeSelected(),
                                onChange: row.getToggleSelectedHandler(),
                            }}
                        />
                    </div>
                ),
            },
            {
                header: 'Title',
                accessorKey: 'title',
                cell: (info) => info.renderValue(),
            },
            {
                header: 'Subject type',
                accessorKey: 'subject_type',
                cell: (info) => {
                    const subjectTypeName = info.row.original.subject_type.name;
                    return <div>{subjectTypeName}</div>;
                },
            },
            {
                header: 'Subject Area',
                accessorKey: 'subject_area',
                cell: (info) => {
                    const subjectArea = info.row.original.area?.name;
                    return <div>{subjectArea}</div>;
                },
            },
            {
                header: 'Years',
                accessorKey: 'standards',
                cell: (info) => {
                    const standards = info.row.original.standards.map((item) => item.title).join(', ');
                    return <div>{standards}</div>;
                },
            },
            {
                header: 'Gallery',
                accessorKey: 'gallery',
                cell: (info) => {
                    return (
                        <div
                            onClick={() =>
                                window.open(`/subjects-selection/management/${info.row.original.id}/gallery`, '_blank')
                            }
                            className='gallery cursor-pointer'
                        >
                            Manage
                        </div>
                    );
                },
            },
            {
                header: 'Visibility',
                accessorKey: 'visible',
                cell: (info) => {
                    const visible = info.row.original.visible;
                    const style = classNames({
                        visibility: true,
                        'visibility--hidden': !visible,
                    });

                    return (
                        <div className={style} onClick={() => handleToggleSingleVisibility(info.row.original.id)}>
                            {visible ? 'Visible' : 'Hidden'}
                        </div>
                    );
                },
            },
            {
                header: 'Actions',
                cell: (info) => {
                    const subjectId = info.row.original.id;

                    const caretStyle = classNames({
                        caret: true,
                        'caret--active': actionsRow === subjectId,
                    });

                    return (
                        <div className='row-action'>
                            <button className='btn' onClick={() => handleToggleActions(subjectId)}>
                                <span className='fs-6 fw-bold'>Actions</span>
                                <span className={caretStyle}></span>
                            </button>
                            <Filter
                                title='Choose action'
                                isHidden={actionsRow !== subjectId}
                                className='row-action__dropdown'
                            >
                                <span
                                    onClick={() =>
                                        window.open(
                                            `/subjects-selection/student/subject-details/${subjectId}`,
                                            '_blank',
                                        )
                                    }
                                    className='filter-component__action fs-6'
                                >
                                    View details
                                </span>
                            </Filter>
                        </div>
                    );
                },
            },
        ];
    }, [subjects, actionsRow]);

    const table = useReactTable({
        data,
        columns,
        state: {
            rowSelection,
            globalFilter,
            pagination: {
                pageIndex: page - 1,
                pageSize: 10,
            },
        },
        pageCount: subjects?.meta.last_page || 0,
        manualPagination: true,
        enableRowSelection: true,
        onRowSelectionChange: setRowSelection,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    });

    const handleToggleActions = (subjectId: number) => {
        if (actionsRow === subjectId) {
            setActionsRow(undefined);
            return;
        }

        setActionsRow(subjectId);
    };

    const handleBulkAction = () => {
        setIsBulkAction(!isBulkAction);
    };

    const handleFilterClick = () => {
        setIsFilter(!isFilter);
    };

    const handleToggleSingleVisibility = (subjectId: number) => {
        if (!user) return;
        if (user.schoolId === null) return;

        mutation.mutate({ schoolId: user.schoolId, subjects: [subjectId] });
    };

    const handleToggleVisibility = async () => {
        if (!user) return;
        if (user.schoolId === null) return;

        const subjectIds = table.getSelectedRowModel().rows.map((row) => row.original.id);

        await mutation.mutate({ schoolId: user.schoolId, subjects: subjectIds });
        table.resetRowSelection();
        setIsBulkAction(false);
    };

    // const handleExportCsv = () => {
    //     user?.schoolId && requestCsvExport(user.schoolId);
    // };

    const handleFilterChange = ({ filter, value }: { filter: FilterKeys; value?: any }) => {
        setSelectedFilters((previousState) => {
            const newSelectedFilters = new Map(previousState);
            newSelectedFilters.set(filter, value);

            Array.from(newSelectedFilters.entries()).forEach(([key]) => {
                if (key.length === 0) {
                    newSelectedFilters.delete(key);
                }
            });

            return newSelectedFilters;
        });
    };

    const handleApplyFilters = () => {
        const queryParams: any = {};

        Array.from(selectedFilters.entries()).forEach(([key, value]) => {
            if (Array.isArray(value) && !!value.length) {
                queryParams[key] = value;
            } else if (!Array.isArray(value)) {
                queryParams[key] = value;
            }
        });

        setQueryParams(queryParams);
        setIsFilter(false);
    };

    const handleResetFilters = () => {
        yearsRef.current?.clearValue();
        subjectTypeRef.current?.clearValue();
        subjectAreaRef.current?.clearValue();

        setSelectedFilters((previousSelections) => {
            const newSelections = new Map(previousSelections);
            newSelections.clear();

            return newSelections;
        });

        setQueryParams({});
        setIsFilter(false);
    };

    const handlePerPageChange = (e: any) => {
        setPerPage(e.currentTarget.value);
    };

    return (
        <div className='subject-management-table'>
            <div className='subject-management-table__actions d-flex justify-content-between'>
                <div className='d-flex'>
                    <DebouncedInput
                        value={globalFilter ?? ''}
                        onChange={(value) => {
                            setSearch(String(value));
                            setPage(1);
                        }}
                        className='p-2 font-lg shadow border border-block'
                        placeholder='Search subject'
                    />
                    {/* {isRequestingCsv && (
                        <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                            <div className='d-flex'>
                                <div className='spinner-border me-3 text-primary' role='status' />
                                <div className='fs-5 d-flex align-items-center'>Requesting CSV download...</div>
                            </div>
                        </div>
                    )} */}
                    {isLoading && (
                        <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                            <div className='d-flex'>
                                <div className='spinner-border me-3 text-primary' role='status' />
                                <div className='fs-5 d-flex align-items-center'>Loading subjects...</div>
                            </div>
                        </div>
                    )}
                    {isRefetching && (
                        <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                            <div className='d-flex'>
                                <div className='spinner-border me-3 text-primary' role='status' />
                                <div className='fs-5 d-flex align-items-center'>Refreshing subjects...</div>
                            </div>
                        </div>
                    )}
                </div>
                <div className='button-actions d-flex'>
                    <div className='bulk me-5'>
                        <button
                            disabled={!!!table.getSelectedRowModel().rows.length}
                            className='btn bulk'
                            onClick={handleBulkAction}
                        >
                            <span className='fs-6'>bulk action</span>
                        </button>
                        <Filter title='Choose bulk action' isHidden={!isBulkAction} className='bulk__actions'>
                            <span onClick={handleToggleVisibility} className='filter-component__action fs-6'>
                                Toggle visibility
                            </span>
                        </Filter>
                    </div>
                    <div className='filter me-5'>
                        <button className='btn' onClick={handleFilterClick}>
                            <FilterIcon />
                            <span className='fs-6 ms-2'>Filter</span>
                        </button>
                        <Filter title='Select filter' isHidden={!isFilter} className='filter__dropdown'>
                            <span className='filter-component__filter-title mb-2'>Subject type</span>
                            <Select
                                isMulti
                                ref={subjectTypeRef}
                                value={selectedFilters.get(FilterKeys.SUBJECT_TYPE)}
                                name='subject_type'
                                onChange={(value) => handleFilterChange({ filter: FilterKeys.SUBJECT_TYPE, value })}
                                options={mapForMultiSelect(filters?.subject_types)}
                                className='basic-multi-select'
                                classNamePrefix='select'
                            />
                            <span className='filter-component__filter-title mb-2'>Subject area</span>
                            <Select
                                isMulti
                                ref={subjectAreaRef}
                                value={selectedFilters.get(FilterKeys.SUBJECT_AREA)}
                                name='subject_area'
                                onChange={(value) => handleFilterChange({ filter: FilterKeys.SUBJECT_AREA, value })}
                                options={mapForMultiSelect(filters?.subject_areas)}
                                className='basic-multi-select'
                                classNamePrefix='select'
                            />
                            <span className='filter-component__filter-title mb-2'>Years</span>
                            <Select
                                isMulti
                                ref={yearsRef}
                                value={selectedFilters.get(FilterKeys.STANDARD)}
                                name='years'
                                onChange={(value) => handleFilterChange({ filter: FilterKeys.STANDARD, value })}
                                options={mapForMultiSelect(filters?.standards)}
                                className='basic-multi-select'
                                classNamePrefix='select'
                            />
                            <span className='filter-component__filter-title mb-2'>Visibility</span>
                            <select
                                style={{
                                    margin: 0,
                                    border: '1px solid',
                                    backgroundColor: 'hsl(0, 0%, 100%)',
                                    borderColor: 'hsl(0, 0%, 80%)',
                                    borderRadius: 4,
                                    padding: 8,
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                value={selectedFilters.get(FilterKeys.VISIBILITY) ?? 'default-visibility'}
                                className='filter-component__dropdown fs-6 fw-bold'
                                onChange={(event) =>
                                    handleFilterChange({
                                        filter: FilterKeys.VISIBILITY,
                                        value: event.currentTarget.value,
                                    })
                                }
                            >
                                <option value='default-visibility' disabled>
                                    Choose an option
                                </option>
                                <option value='hidden'>Hidden</option>
                                <option value='visible'>Visible</option>
                            </select>
                            <div className='filter-component__button-row'>
                                <Button
                                    className='mb-6 fs-7 fw-normal filter-component__button grey btn-sm'
                                    onClick={handleResetFilters}
                                    title='Reset'
                                    type={ButtonType.GRAY}
                                />
                                <Button
                                    className='mb-6 fs-7 fw-normal filter-component__button btn-sm'
                                    onClick={handleApplyFilters}
                                    title='Apply'
                                    type={ButtonType.PRIMARY}
                                />
                            </div>
                        </Filter>
                    </div>
                    {/* <button className='btn export' onClick={handleExportCsv}>
                        <FileExport />
                        <span className='fs-6 ms-2'>Export</span>
                    </button> */}
                </div>
            </div>
            {!!Object.keys(queryParams).length && (
                <div className='my-2'>
                    <span className='fw-bold fs-6 me-2'>Your filters:</span>
                    {Object.entries(queryParams).map(([key, value]) => {
                        const content: any = {
                            [FilterKeys.STATE]: `State`,
                            [FilterKeys.SUBJECT_TYPE]: `Subject type`,
                            [FilterKeys.SUBJECT_AREA]: `Subject area`,
                            [FilterKeys.STANDARD]: `Years`,
                            [FilterKeys.VISIBILITY]: `Visibility`,
                        };

                        let text = value;

                        if (Array.isArray(value)) {
                            text = value.map((item) => item.label).join(', ');
                        } else if (key === FilterKeys.STATE) {
                            text = filters?.states.find((state) => state.id === Number(value))?.code;
                        }

                        return (
                            <span
                                style={{
                                    padding: '4px 6px',
                                    background: 'hsl(0, 0%, 90%)',
                                    borderRadius: '2px',
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                className='me-2'
                                key={key}
                            >
                                {content[key]}: {text}
                            </span>
                        );
                    })}
                </div>
            )}
            <Table
                className='subject-management-table__component'
                header={() =>
                    table.getHeaderGroups().map((headerGroup) => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                                <th className='fw-bold' key={header.id}>
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(header.column.columnDef.header, header.getContext())}
                                </th>
                            ))}
                        </tr>
                    ))
                }
                body={() =>
                    table.getRowModel().rows.map((row) => (
                        <tr key={row.id}>
                            {row.getVisibleCells().map((cell) => (
                                <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                            ))}
                        </tr>
                    ))
                }
            />
            <div className='h-2' />
            <div className='d-flex justify-content-between'>
                <div className='d-flex align-items-center'>
                    <span className='fs-6 fw-bold me-3'>Go to page:</span>
                    <input
                        type='number'
                        style={{
                            width: 60,
                            height: 40,
                            background: '#F6F8FA',
                        }}
                        className='form-control form-control-flush fw-bold fs-5'
                        defaultValue={table.getState().pagination.pageIndex + 1}
                        onChange={(e) => {
                            const page = e.target.value ? Number(e.target.value) : 0;
                            setPage(page);
                        }}
                    />
                </div>
                <div className='d-flex align-items-center'>
                    <div className='me-5'>
                        <span className='fs-6 fw-bold me-3'>Per page:</span>
                        <select
                            className='border'
                            style={{ width: 40, height: 30 }}
                            value={perPage}
                            onChange={handlePerPageChange}
                        >
                            <option value='10'>10</option>
                            <option value='20'>20</option>
                            <option value='30'>30</option>
                            <option value='40'>40</option>
                            <option value='50'>50</option>
                        </select>
                    </div>
                    <div className='me-5'>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage(1)}
                            disabled={!table.getCanPreviousPage()}
                        >
                            {'<<'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage((old) => Math.max(old - 1, 1))}
                            disabled={!table.getCanPreviousPage()}
                        >
                            {'<'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => {
                                setPage((old) => old + 1);
                            }}
                            disabled={!table.getCanNextPage()}
                        >
                            {'>'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage(table.getPageCount())}
                            disabled={!table.getCanNextPage()}
                        >
                            {'>>'}
                        </button>
                    </div>
                    <span className='d-flex items-center gap-2'>
                        <div>Page</div>
                        <strong>
                            {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                        </strong>
                    </span>
                </div>
            </div>
        </div>
    );
}

function mapForMultiSelect(items?: any[]): any[] {
    if (!items) {
        return [];
    }

    return items.map((item) => ({
        value: String(item.id),
        label: item?.name || item?.title || 'N/A',
    }));
}
