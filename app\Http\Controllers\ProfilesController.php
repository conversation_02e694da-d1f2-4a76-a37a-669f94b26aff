<?php

namespace App\Http\Controllers;

use A;
use App\ChildInvitee;
use App\IndustryCategory;
use App\ChildParent;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Standard;
use App\User;
use App\Profile;
use App\Stage;
use App\Plan;
use Auth;
use Newsletter;
use Illuminate\Http\Request;
use App\Country;
use App\ParentInvitee;
use App\Jobs\ProcessParentInvitation;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Services\UserAccessService;

class ProfilesController extends Controller
{
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        return view('profiles.show', [
            'profileUser' => $user,
            'activities' => Activity::feed($user),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id = '')
    {
        $stages = Stage::all();
        $countries = Country::All();
        if (!$id) {
            $user = Auth::user();
        } else {
            $user = User::findOrFail($id);
        }

        if (UserAccessService::currentUserCanAccess($user->id)) {
            $years = Standard::secondarySchool()->get();
            $breadcrumb = "edit-profile";
            $bannerTitle = (Auth::user()->isParent() || (Auth::user()->isTeacher() && $id)) ? "Account" : "Your Account";

            if (Auth::user()->isStaff()) {
                $administrators = User::where('organisation_id', Auth::user()->organisation_id)->whereHas('profile', function ($q) {
                    $q->where('access', 'Full');
                })->get();
            } else if (!Auth::user()->isStudent()) {
                $administrators = User::where('school_id', Auth::user()->school_id)->whereHas('profile', function ($q) {
                    $q->where('access', 'Full');
                })->get();
            }

            if (Auth::user()->isParent() && !$id) {
                $industrycategories = IndustryCategory::select('id', 'name')->get();
                $selectedIndustries = ChildParent::find($user->id)->industries()->pluck('industry_id');
                $invitees = ChildInvitee::where('parent_id', Auth::id())->with('child')->get();
                $invitations = ParentInvitee::whereEmail(Auth::user()->email)->whereProcessed('0')->get();
                return view('profiles.parent', compact('user', 'countries', 'invitees', 'invitations', 'industrycategories', 'selectedIndustries'));
            } elseif (Auth::user()->isStudent()) {
                if(Auth::user()->isUnderTertiary()){
                    return view('profiles.tertiaryStudent', compact('user', 'countries', 'years', 'stages', 'breadcrumb', 'bannerTitle', 'id'));
                }
                //    session()->forget('id');
                // if (Auth::user()->isParent()) {
                //     session(['id' => $id]);
                //     $id = session('id');
                // }
                $invitees = ParentInvitee::whereChildId($user->id)->whereProcessed('0')->get();
                return view('profiles.student', compact('user', 'countries', 'years', 'invitees', 'stages', 'breadcrumb', 'bannerTitle', 'id'));
            } elseif (session('studentView') == 'true' && Auth::user()->isTeacher()) {
                return view('profiles.teacher', compact('user', 'countries', 'years', 'breadcrumb', 'bannerTitle'));
            } elseif (Auth::user()->isTeacher() && !$id) {
                return view('profiles.teacher', compact('user', 'countries', 'years', 'administrators'));
            } elseif (session('studentView') == 'true' && Auth::user()->isStaff()) {
                return view('profiles.staff', compact('user', 'countries', 'years', 'breadcrumb', 'bannerTitle'));
            } elseif (Auth::user()->isStaff() && !$id) {
                return view('profiles.staff', compact('user', 'countries', 'years', 'administrators'));
            } elseif ((Auth::user()->isTeacher() && $id) || (Auth::user()->isStaff() && $id)) {
                $name = $user->name;
                return view('profiles.teacherStudentView', compact('user', 'breadcrumb', 'bannerTitle', 'id', 'name'));
            } elseif (Auth::user()->isParent() && $id) {
                $name = $user->name;
                return view('profiles.parentStudentView', compact('user', 'breadcrumb', 'bannerTitle', 'id', 'name'));
            } elseif (Auth::user()->isAdmin()) {
                return view('profiles.admin', compact('user', 'countries'));
            } elseif (Auth::user()->isCompassTeacher()) {
                return view('primaryschools.teachers.account');
            } else {
                return view('profiles.edit', compact('user', 'countries'));
            }
        }
        abort(403, 'You do not have permission to perform this action.');
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if ($request->new_avatar) {
            $avatar = $request->new_avatar->store('avatars', ['visibility' => 'public']);
        } else {
            $avatar = $request->current_avatar;
        }
        $user = User::find(Auth::id());

        if (Auth::user()->isAdmin() || Auth::user()->isMarker()) {
            $user->name = $request->fullname;
            $user->email = $request->email;
            if ($request->password) {
                $user->password = bcrypt($request->password);
            }
            $user->avatar_path = $avatar;
            $user->save();
        } else {

            $oldEmail = $user->email;
            $oldYear = $user->profile->standard_id;

            $user->name = $request->firstname . " " . $request->lastname;
            $user->email = $request->email;

            if(!Auth::user()->isUnderTertiary()){
                $user->state_id = $request->state;
            }

            $user->postcode = $request->postcode;
            if ($request->password) {
                $user->password = bcrypt($request->password);
            }
            $user->avatar_path = $avatar;

            $profile = $user->profile;
            $profile->firstname = $request->firstname;
            $profile->lastname = $request->lastname;

            if (request('other_gender')) {
                $gender = request('other_gender');
            } else {
                $gender = request('gender');
            }
            $profile->gender = $gender;

            if (Auth::user()->role->name == 'Individual Student' || Auth::user()->role->name == 'Student') {

                if (request('stage') == 'school') {
                    $profile->standard_id = $request->school_year;
                    $profile->stage_id = null;
                    $profile->graduate_year = null;
                    $profile->school = $request->school_name;
                } else {
                    // $profile->stage_id = $request->stage_id;
                    $profile->graduate_year = $request->graduate_year;
                    $profile->school = null;
                    $profile->standard_id = Standard::nonStudentId();
                    $latestPlan = Plan::where('user_id', Auth::id())->latest()->first();
                }
            } else {
                $profile->standard_id = $request->year;
            }

            $user->save();
            $user->profile()->save($profile);
            if (request('stage')) {
                if (request('stage') != 'school' && $latestPlan) {
                    $latestPlan->finishschool = null;
                    $latestPlan->save();
                }
            }

            if ($request->orgcampuses) {
                $user->orgCampuses()->sync($request->orgcampuses);
            }

            if ($request->campuses) {
                $user->campuses()->sync($request->campuses);
            }


            // User::addHelpcrunchAccount($user->id);

            if ($oldEmail != $request->email) {
                if (Auth::user()->isTeacher()) {
                    $list = 'teachers';
                } elseif (Auth::user()->isParent()) {
                    $list = 'parents';
                } else {
                    $list = 'students';
                }
                Newsletter::updateEmailAddress($oldEmail, $request->email, $list);

                if (Auth::user()->isParent()) {
                    User::updateAssociatedUsersMailchimpDetail($user->id);
                }
            }

            if (Auth::user()->isStudent() && Auth::user()->is_child && (($oldEmail != $request->email) || ($oldYear != $request->year))) {
                User::updateAssociatedUsersMailchimpDetail($user->id);
                if ($oldEmail != $request->email) {
                    Newsletter::updateEmailAddress($oldEmail, $request->email, 'students');
                }
            }
            if (!Auth::user()->isStaff()) {
                UpdateMailchimpAudienceJob::dispatch(auth()->user());
            }
        }


        Cache::forget('profile' . $user->id);
        Cache::forget('location' . $user->id);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Your profile has been updated successfully');
    }

    public function saveIndustries(Request $request, $id)
    {
        $parent = ChildParent::find($id);
        $parent->industries()->sync($request->industries);
        UpdateMailchimpAudienceJob::dispatch(auth()->user());
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Your industry preferences has been saved successfully!');
    }

    public function storeParentInvitees(Request $request)
    {
        $relation = $request->relation;
        foreach ($request->email as $key => $email) {
            $invitation = ParentInvitee::create([
                'child_id' => Auth::id(),
                'relation' => $relation[$key],
                'email' => $email,
                'token' => uniqid(Str::random(27)),
                'processed' => '0',
            ]);

            dispatch(new ProcessParentInvitation($invitation))->afterCommit();
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Invitation has been sent successfully!');
    }

    public function destroyParentInvitees($id)
    {
        $invitation = ParentInvitee::findOrFail($id);
        if (Auth::user()->isAdmin() || (Auth::id() == $invitation->child_id) || (Auth::user()->isParent() && Auth::user()->email == $invitation->email)) {
            $invitation->delete();
            $message = 'Parent invitation has been cancelled successfully!';
            if (Auth::user()->isParent()) {
                $message = "The invitaion has been declined!";
            }

            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', $message);
        }
        abort(403);
    }


    public function storeYear(Request $request)
    {
        $user = User::find(Auth::id());
        $profile = $user->profile;
        if ($request->stage == 'done') {
            $profile->graduate_year = $request->graduate_year;
            $profile->standard_id = Standard::nonStudentId();
            $latestPlan = Plan::where('user_id', Auth::id())->latest()->first();
        }
        if ($request->stage == 'school') {
            $profile->standard_id = $request->school_year;
            $profile->graduate_year = null;
        }

        $profile->year_popup = false;

        $user->profile()->save($profile);
        if (request('stage') == 'done' && $latestPlan) {
            $latestPlan->finishschool = null;
            $latestPlan->save();
        }
        // User::addHelpcrunchAccount($user->id);
        UpdateMailchimpAudienceJob::dispatch($user);

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Your details has been saved successfully!');
    }
    public function usersProfile()
    {
        User::whereNotIn('role_id', [1, 6, 8])->doesntHave('profile')->chunk(100, function ($users) {
            foreach ($users as $user) {
                $words = explode(' ', $user->name);
                $lastname = array_pop($words);
                $firstname = implode(' ', $words);

                $profile = new Profile;
                $profile->user_id = $user->id;
                $profile->firstname = $firstname;
                $profile->lastname = $lastname;
                $profile->save();
            }
        });

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Users profile has been saved successfully!');
    }
}
