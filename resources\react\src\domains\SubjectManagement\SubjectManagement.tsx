import dependencyInjection from '@/utils/dependencyInjection';
import { SchoolsService } from './services/Schools.service';
import { SubjectManagementService } from './services/SubjectManagement.service';
import SubjectManagementStatisticsView from './views/SubjectManagementStatisticsView/SubjectManagementStatisticsView';
import SubjectManagementTeacherLandingView from './views/SubjectManagementTeacherLandingView/SubjectManagementTeacherLandingView';
import StudentManagementView from './views/StudentManagementView/StudentManagementView';
import SubjectManagementView from './views/SubjectManagementView/SubjectManagementView';
import { SubjectSelectionService } from '../SubjectSelection/services/SubjectSelection.service';
import SubjectGalleryView from './views/SubjectGalleryView/SubjectGalleryView';
import { SubjectsService } from './services/Subjects.service';

const subjectManagementService = SubjectManagementService.getInstance();
const subjectSelectionService = SubjectSelectionService.getInstance();
const schoolsService = SchoolsService.getInstance();
const subjectsService = SubjectsService.getInstance();

const [SubjectManagementServiceProvider, useSubjectManagementDeps] = dependencyInjection({
    services: {
        subjectManagementService,
        schoolsService,
        subjectSelectionService,
        subjectsService,
    },
});

export function SubjectManagementTeacherLandingPage() {
    return <SubjectManagementTeacherLandingView />;
}

export function SubjectManagementStatisticsPage() {
    return <SubjectManagementStatisticsView />;
}

export function StudentManagementPage() {
    return <StudentManagementView />;
}

export function SubjectManagementPage() {
    return <SubjectManagementView />;
}

export function SubjectGalleryPage() {
    return (
        <SubjectManagementServiceProvider>
            <SubjectGalleryView />
        </SubjectManagementServiceProvider>
    );
}

export { SubjectManagementServiceProvider, useSubjectManagementDeps };
