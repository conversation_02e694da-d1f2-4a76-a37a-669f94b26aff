import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSubjectSelectionDispatch } from '../SubjectSelection';
import { setNavigation } from '../context/SubjectSelection.actions';
import { SelectionSteps } from '../models/SubjectSelection.model';
import {
    SUBJECTS_SELECTION_ELECTIVES,
    SUBJECTS_SELECTION_INTERESTS,
    SUBJECTS_SELECTION_JOBS,
    SUBJECTS_SELECTION_LANGUAGES,
    SUBJECTS_SELECTION_LEARNING_STYLE,
    SUBJECTS_SELECTION_SKILLS,
    SUBJECTS_SELECTION_STUDENT_LANDING,
} from '@/router/Router.constants';
import { SubjectSelectionQuizResponse } from '../models/SubjectSelection.api.model';

interface Props {
    currentStep: SelectionSteps;
}

export default function useSelectionNavigation({ currentStep }: Props) {
    const navigate = useNavigate();
    const dispatch = useSubjectSelectionDispatch();

    useEffect(() => {
        const { back, next } = navigationMap[currentStep];

        let clickBack = undefined;
        let clickNext = undefined;
        let submit = undefined;

        if (back) clickBack = () => navigate(back);
        if (next) clickNext = () => navigate(next);

        if (currentStep !== SelectionSteps.ELECTIVES) {
            submit = undefined;
        }

        dispatch(setNavigation({ clickBack, clickNext, currentStep, submit }));
    }, [dispatch, currentStep]);
}

export const navigationMap: Record<keyof SubjectSelectionQuizResponse, any> = {
    [SelectionSteps.INTERESTS]: {
        back: SUBJECTS_SELECTION_STUDENT_LANDING,
        next: SUBJECTS_SELECTION_SKILLS,
    },
    [SelectionSteps.SKILLS]: {
        back: SUBJECTS_SELECTION_INTERESTS,
        next: SUBJECTS_SELECTION_LEARNING_STYLE,
    },
    [SelectionSteps.STUDY_HABITS]: {
        back: SUBJECTS_SELECTION_SKILLS,
        next: SUBJECTS_SELECTION_JOBS,
    },
    [SelectionSteps.JOBS]: {
        back: SUBJECTS_SELECTION_LEARNING_STYLE,
        next: SUBJECTS_SELECTION_LANGUAGES,
    },
    [SelectionSteps.LANGUAGES]: {
        back: SUBJECTS_SELECTION_JOBS,
        next: SUBJECTS_SELECTION_ELECTIVES,
    },
    [SelectionSteps.ELECTIVES]: {
        back: SUBJECTS_SELECTION_LANGUAGES,
        next: undefined,
    },
};
