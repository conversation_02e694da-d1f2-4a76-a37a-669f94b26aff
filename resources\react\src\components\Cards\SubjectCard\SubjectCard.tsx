import DownArrow from '@/assets/icons/subjectSelection/DownArrow';
import { PillType } from '@/utils/enums';
import Pill from '../../Pill/Pill';
import { truncateText } from '@/utils/helpers';
import classnames from 'classnames';

interface SubjectCardProps {
    title: string;
    type?: string;
    course_type?: string | null;
    onDetailsClick?: () => void;
    classNames?: string;
    style?: {};
    hasAddButton?: boolean;
    onClick?: () => void;
    units?: string | null;
    selected?: boolean;
    imageUrl?: string | null;
    match?: string | null;
}

const SubjectCard = ({
    title,
    type,
    course_type,
    classNames,
    style,
    imageUrl,
    onDetailsClick,
    onClick,
    hasAddButton,
    units,
    match,
    selected = false,
}: SubjectCardProps) => {

    const handleClick: React.MouseEventHandler<HTMLSpanElement> = (event) => {
        event.preventDefault();
        event.stopPropagation();
        onClick!();
    };

    const handleDetailsClick: React.MouseEventHandler<HTMLDivElement> = (event) => {
        event.preventDefault();
        event.stopPropagation();
        onDetailsClick?.();
    };

    const addIconStyles = classnames({
        'subject-card__add-icon': true,
        'subject-card__add-icon--selected': selected,
    });

    return (
        <div
            className={`subject-card shadow mb-15 ${classNames} ${selected ? 'bg-black dark' : 'bg-white'}`}
            onClick={handleClick}
            style={style}
        >
            {imageUrl && (
                <div className='subject-card__image-container'>
                    <img src={imageUrl} alt='subject image' />
                </div>
            )}
            <div className='subject-card__details-container'>
                {hasAddButton && <span className={addIconStyles} />}
                <div className='subject-card__subject'>
                    <h1>{truncateText(title, 30)}</h1>
                    <div className='subject-card__pills-container row'>
                        {match && match !== '' && <Pill type={PillType.MATCH} text={`${match} Match`} />}
                        {type && <Pill type={type} text={type} />}
                        {course_type && <Pill type={PillType.COURSE_TYPE} text={course_type}  style={{ backgroundColor: '#5999ff' }} />}
                        {units && <Pill type={PillType.UNITS} text={`${units} units`} />}
                    </div>
                </div>
                <div className='subject-card__details-link' onClick={handleDetailsClick}>
                    <DownArrow />
                    <h2>Subject Details</h2>
                </div>
            </div>
        </div>
    );
};

export default SubjectCard;
