<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\RegisterationController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Vue\ClosuresController;
use App\Http\Controllers\Vue\LessonsController as VueLessonsController;
use App\Http\Controllers\Vue\WewSkillstrainingController as VueWewSkillstrainingController;
use App\Http\Controllers\Vue\ExploreWorkexperienceController as VueExploreWorkexperienceController;
use App\Http\Controllers\Vue\ExploreIndustriesController as VueExploreIndustriesController;
use App\Http\Resources\UserResource;
Route::get('authstatus', [AuthenticatedSessionController::class, 'authstatus'])
                ->name('authstatus');
Route::get('states', [ClosuresController::class, 'states'])
                ->name('states');
Route::get('courseStudyAreas', [ClosuresController::class, 'courseStudyAreas'])
->name('courseStudyAreas');
Route::get('courseInstitutions', [ClosuresController::class, 'courseInstitutions'])
->name('courseInstitutions');
Route::get('courseQualLevels', [ClosuresController::class, 'courseQualLevels'])
->name('courseQualLevels');
Route::get('years', [ClosuresController::class, 'years'])
                ->name('years');
Route::get('schoolSearch', [ClosuresController::class, 'schoolSearch'])
                ->name('schoolSearch');
Route::get('secondaryYears', [ClosuresController::class, 'secondaryYears'])
                ->name('secondaryYears');
Route::get('lessonCategories', [ClosuresController::class, 'lessonCategories'])
                ->name('lessonCategories');
Route::get('skillstrainingCategories', [ClosuresController::class, 'skillstrainingCategories'])
                ->name('skillstrainingCategories');
Route::get('vweIndustries', [ClosuresController::class, 'vweIndustries'])
                ->name('vweIndustries');
Route::get('vweSubjects', [ClosuresController::class, 'vweSubjects'])
                ->name('vweSubjects');
Route::get('getLessonSkills', [ClosuresController::class, 'getLessonSkills'])
                ->name('getSkills');
Route::get('getStSkills', [ClosuresController::class, 'getStSkills'])
                ->name('getStSkills');
Route::get('getVweSkills', [ClosuresController::class, 'getVweSkills'])
                ->name('getVweSkills');
Route::middleware(['guest'])->group(function () {
    // Route::get('register', [RegisteredUserController::class, 'create'])
    //             ->name('register');
    Route::post('register', [RegisterationController::class, 'store']);
    Route::post('parent-register', [RegisterationController::class, 'storeParentLicense']);

    Route::get('parent/validateToken', [RegisterController::class, 'store']);
    Route::get('login', [LoginController::class, 'create'])
                ->name('login');

    Route::post('login', [LoginController::class, 'login']);
    Route::post('checkmail', [LoginController::class, 'checkmail'])->name('checkmail');
    Route::post('checkUniqueEmail', [LoginController::class, 'checkUniqueEmail'])->name('checkUniqueEmail');
    Route::post('checkChildEmail', [LoginController::class, 'checkChildEmail'])->name('checkChildEmail');

    Route::post('forgot_password', [ResetPasswordController::class, 'store'])
    ->name('password.email');
    Route::post('reset-password', [ResetPasswordController::class, 'reset'])
    ->name('password.update');
    // Route::post('stageType', [AuthenticatedSessionController::class, 'stageType'])->name('stageType');


    // // Route::get('schoolPasswordView', [AuthenticatedSessionController::class, 'schoolPasswordView'])->name('schoolPasswordView');

    // Route::post('registerAs', [RegisteredUserController::class, 'registerAs'])->name('registerAs');
    // Route::get('stageOfLife', [RegisteredUserController::class, 'stageOfLife'])->name('stageOfLife');
    // Route::post('processStageOfLife', [RegisteredUserController::class, 'processStageOfLife'])->name('processStageOfLife');
    // Route::post('processSelectSchool', [RegisteredUserController::class, 'processSelectSchool'])->name('processSelectSchool');
    Route::post('validateSchoolDetail', [LoginController::class, 'validateSchoolDetail'])->name('validateSchoolDetail');
    Route::post('checkSchoolLimit', [LoginController::class, 'checkSchoolLimit'])->name('checkSchoolLimit');
    // Route::post('createAccount', [RegisteredUserController::class, 'createAccount'])->name('createAccount');
    // Route::post('parentCheckEmail', [RegisteredUserController::class, 'parentCheckEmail'])->name('parent.checkEmail');
    // Route::post('parentRegister', [RegisteredUserController::class, 'parentRegister'])->name('parent.register');

    // Route::get('selectSchool', [RegisteredUserController::class, 'selectSchool'])->name('selectSchool');
    // Route::get('schoolPassword', [RegisteredUserController::class, 'schoolPassword'])->name('schoolPassword');
    // Route::get('searchEmail', [RegisteredUserController::class, 'searchEmail'])->name('searchEmail');
    // Route::get('userRegister', [RegisteredUserController::class, 'userRegister'])->name('userRegister');


    // Route::get('individualBuylicense', [RegisteredUserController::class, 'individualBuylicense'])->name('individualBuylicense');
    // Route::get('parentBuyLicense', [RegisteredUserController::class, 'parentBuyLicense'])->name('parentBuyLicense');
    // Route::get('parentChildEmail', [RegisteredUserController::class, 'parentChildEmail'])->name('parentChildEmail');
    // Route::get('parentDetail', [RegisteredUserController::class, 'parentDetail'])->name('parentDetail');
    // Route::get('parentChooseAccount', [RegisteredUserController::class, 'parentChooseAccount'])->name('parentChooseAccount');

    // Route::get('limited-parent-dashboard', [LimitedParentController::class, 'dashboard'])->name('limitedParent.dashboard');
    // Route::get('premium-parent-dashboard', [PremiumParentController::class, 'dashboard'])->name('premiumParent.dashboard');
    // Route::get('student-dashboard', [StudentController::class, 'dashboard'])->name('student.dashboard');



    // Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
    //             ->name('password.request');

    // Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])
    //             ->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
                ->name('password.reset');
    Route::post('/stripe-parent-purchase-child-session',[RegisterationController::class, 'stripepurchasechildlicensesession'])->name('stripe.parentpurchasechildlicensesession');

    /*Social Login*/


    // Route::get('/auth/redirect', function () {
    //     return Socialite::driver('google')->redirect();
    // })->name('loginwithgoogle');

    // Route::get('/auth/callback', function () {
    //     $user = Socialite::driver('google')->user();
    //     dd($user);
    //     // $user->token
    // });
});

Route::middleware('auth')->group(function () {
    // Route::get('verify-email', [EmailVerificationPromptController::class, '__invoke'])
    //             ->name('verification.notice');

    // Route::get('verify-email/{id}/{hash}', [VerifyEmailController::class, '__invoke'])
    //             ->middleware(['signed', 'throttle:6,1'])
    //             ->name('verification.verify');

    // Route::post('email/verification-notification', [EmailVerificationNotificationController::class, 'store'])
    //             ->middleware('throttle:6,1')
    //             ->name('verification.send');

    // Route::get('confirm-password', [ConfirmablePasswordController::class, 'show'])
    //             ->name('password.confirm');

    // Route::post('confirm-password', [ConfirmablePasswordController::class, 'store']);

    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
                ->name('logout');
    // Route::get('addParentAccount', [AddParentController::class, 'index'])->name('addParentAccount');
    // Route::post('storeParentAccount', [AddParentController::class, 'store'])->name('storeParentAccount');
    Route::post('verify_token',function(){
        return new UserResource(Auth::user());
    });
    Route::post('resetIntendedUrl',function(){
        session()->forget('url.intended');
        return true;
    });

});

Route::post('setIntendedUrl',function(Request $request){
    // session()->forget('url.intended');
    session('url.intended',$request->url);
    return true;
});
// Auth::routes();
// Route::post('verify_token',function(){
//     return Auth::user();
// });
