<template>
    <!-- <FreeParent v-if="currentUser.isParent && currentUser.hasLimitedAccess" /> -->

    <Student v-if="currentUser.isStudent || currentUser.studentView" />

    <!-- <PremiumParent v-if="currentUser.isParent && currentUser.hasPremiumAccess"/> -->
    <PremiumParent v-if="currentUser.isParent" />

    <Teacher v-if="currentUser.isTeacher && !currentUser.studentView && currentUser.isSecondaryTeacher" />

    <PrimaryTeacher v-if="currentUser.isTeacher && !currentUser.studentView && currentUser.isPrimaryTeacher" />

</template>

<script lang="ts">
    import { defineComponent } from "vue";
    // import FreeParent from "@/components/dashboards/FreeParent.vue";
    import PremiumParent from "@/components/dashboards/PremiumParent.vue";
    import Student from "@/components/dashboards/Student.vue";
    import Teacher from "@/components/dashboards/Teacher.vue";
    import PrimaryTeacher from "@/components/dashboards/PrimaryTeacher.vue";
    import { useStore } from "vuex";
    // import Widget2 from "@/components/dashboard-default-widgets/Widget2.vue";
    // import Widget3 from "@/components/dashboard-default-widgets/Widget3.vue";
    // import Widget4 from "@/components/dashboard-default-widgets/Widget4.vue";
    // import Widget5 from "@/components/dashboard-default-widgets/Widget5.vue";
    // import Widget6 from "@/components/dashboard-default-widgets/Widget6.vue";
    // import Widget7 from "@/components/dashboard-default-widgets/Widget7.vue";
    // import Widget8 from "@/components/dashboard-default-widgets/Widget8.vue";
    // import Widget9 from "@/components/dashboard-default-widgets/Widget9.vue";
    // import Widget10 from "@/components/dashboard-default-widgets/Widget10.vue";
    // import MixedWidget5 from "@/components/widgets/mixed/Widget5.vue";

    export default defineComponent({
        name: "main-dashboard",
        components: {
            // FreeParent,
            PremiumParent,
            Student,
            Teacher,
            PrimaryTeacher
        },
        setup() {
            const store = useStore();
            const currentUser = store.getters.currentUser;
            return {
                currentUser
            }
        },
        props: {

        },
    });
</script>