import { SubjectSelectionService } from './services/SubjectSelection.service';

import SubjectSelectionOutlet from './outlets/SubjectSelectionOutlet/SubjectSelectionOutlet';

import dependencyInjection from '@/utils/dependencyInjection';

import SubjectResultsView from './views/SubjectResultsView/SubjectResultsView';
import SubjectSelectionMyElectivesView from './views/SubjectSelectionMyElectivesView/SubjectSelectionMyElectivesView';
import SubjectSelectionMyInterestsView from './views/SubjectSelectionMyInterestsView/SubjectSelectionMyInterestsView';
import SubjectSelectionMyJobsView from './views/SubjectSelectionMyJobsView/SubjectSelectionMyJobsView';
import SubjectSelectionMyLanguagesView from './views/SubjectSelectionMyLanguagesView/SubjectSelectionMyLanguagesView';
import SubjectSelectionMyLearningStyleView from './views/SubjectSelectionMyLearningStyleView/SubjectSelectionMyLearningStyleView';
import SubjectSelectionMySkillsView from './views/SubjectSelectionMySkillsView/SubjectSelectionMySkillsView';
import { SubjectSelectionState } from './context/SubjectSelection.types';
import makeContextStore from '@/utils/makeContextStore';
import SubjectSelectionReducer from './context/SubjectSelection.reducer';
import SubjectQuizFinishedView from './views/SubjectQuizFinishedView/SubjectQuizFinishedView';
import SubjectSelectionStudentLandingView from './views/SubjectSelectionStudentLandingView/SubjectSelectionStudentLandingView';
import SubjectDetailsView from './views/SubjectDetailsView/SubjectDetailsView';
import { useSubjects } from './consumers/useSubjectSelectionQuizApiConsumer';
import SubjectResultsPdfView from './views/SubjectResultsPdfView/SubjectResultsPdfView';

const subjectSelectionService = SubjectSelectionService.getInstance();

const [SubjectSelectionServiceProvider, useSubjectSelectionDeps] = dependencyInjection({
    services: {
        subjectSelectionService,
    },
});

const initialState: SubjectSelectionState = {
    navigation: {
        currentStep: undefined,
        clickBack: undefined,
        clickNext: () => {},
        submit: undefined,
    },
    quizSelection: {
        interests: [],
        skills: [],
        study_habits: [],
        jobs: [],
        languages: [],
        electives: [],
    },
};

const [SubjectSelectionContextProvider, useSubjectSelectionContextStore, useSubjectSelectionDispatch] =
    makeContextStore(SubjectSelectionReducer, initialState);

export function SubjectSelectionPage() {
    return (
        <SubjectSelectionContextProvider>
            <SubjectSelectionServiceProvider>
                <SubjectSelectionOutlet />
            </SubjectSelectionServiceProvider>
        </SubjectSelectionContextProvider>
    );
}

export function SubjectSelectionMyInterestsPage() {
    return <SubjectSelectionMyInterestsView />;
}

export function SubjectSelectionMySkillsPage() {
    return <SubjectSelectionMySkillsView />;
}

export function SubjectSelectionDetailsPage() {
    return <SubjectDetailsView />;
}

export function SubjectSelectionMyLearningStylePage() {
    return <SubjectSelectionMyLearningStyleView />;
}

export function SubjectSelectionMyJobsPage() {
    return <SubjectSelectionMyJobsView />;
}

export function SubjectSelectionMyLanguagesPage() {
    return <SubjectSelectionMyLanguagesView />;
}

export function SubjectSelectionMyElectivesPage() {
    return <SubjectSelectionMyElectivesView />;
}

export function SubjectSelectionResultsPage() {
    return <SubjectResultsView />;
}

export function SubjectSelectionResultsPdfPreviewPage() {
    return <SubjectResultsPdfView />;
}

export function SubjectSelectionLandingPage() {
    const { isLoading } = useSubjects();

    return (
        <>
            <SubjectSelectionStudentLandingView />
            {!isLoading && <SubjectResultsView />}
        </>
    );
}

export function SubjectQuizFinishedPage() {
    return <SubjectQuizFinishedView />;
}

export function SubjectDetailsPage() {
    return <SubjectDetailsView />;
}

export { useSubjectSelectionDeps, useSubjectSelectionContextStore, useSubjectSelectionDispatch };
