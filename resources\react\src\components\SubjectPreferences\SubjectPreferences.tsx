import { ButtonType, PillType } from '@/utils/enums';
import Button from '../base/Button/Button';
import { SubjectPreference } from '@/domains/SubjectSelection/models/SubjectSelection.model';
import Pill from '../Pill/Pill';
import { SubjectPreferenceStatus } from '@/domains/SubjectSelection/models/SubjectSelection.api.model';
import { SchoolRulesDetailsResponse } from '@/domains/SubjectManagement/models/Schools.service.api.model';
import classNames from 'classnames';

interface Props {
    items: SubjectPreference[];
    status?: SubjectPreferenceStatus;
    selectedRules?: number[];
    rules?: SchoolRulesDetailsResponse[];
    onRemove?: (subject: SubjectPreference) => void;
    isQuizSubmitted?: boolean;
    onSubmit?: () => void;
    onRuleSelected?: (ruleId: number) => void;
    isSubmitting?: boolean;
}

export default function SubjectPreferences({
    items,
    onRemove,
    isQuizSubmitted,
    onSubmit,
    status,
    rules,
    onRuleSelected,
    selectedRules,
    isSubmitting,
}: Props) {
    const isStatus =
        status &&
        !!items.length &&
        (status === SubjectPreferenceStatus.IN_PROGRESS || status === SubjectPreferenceStatus.SAVED);

    const statusMessage = {
        [SubjectPreferenceStatus.IN_PROGRESS]: 'in progress',
        [SubjectPreferenceStatus.SAVED]: 'submitted',
    };

    return (
        <div className='subject-preferences'>
            <div className='subject-preferences__headline'>
                <h1>My Subjects</h1>
                {isStatus && <div className={`status status--${status} mt-2`}>{statusMessage[status]}</div>}
            </div>

            <div className='subject-item__container'>
                {!items.length && (
                    <div className='h-100 d-flex align-items-center justify-content-center'>
                        {isQuizSubmitted && <span className='no-subjects'>No subjects selected!</span>}
                        {!isQuizSubmitted && <span className='no-subjects'>No saved subjects.</span>}
                    </div>
                )}
                {!!items.length &&
                    items.map((item) => {
                        return (
                            <div key={item.id} className='subject-item__element'>
                                <div key={item.id} className='subject-item__content'>
                                    <span className='title d-block'>{item.name}</span>
                                    <div className='d-flex'>



                                        {item?.match &&
                                            item.match !== '' &&
                                            item.match.toLowerCase() !== 'Nothing'.toLowerCase() && (
                                                <Pill  className='me-2' type={PillType.MATCH} text={`${item.match} Match`} />
                                            )}
                                        {item?.type && <Pill className='me-2' type={item.type} text={item.type} />}
                                        {item?.course_type && <Pill className='me-2' type={PillType.COURSE_TYPE} text={item.course_type} />}
                                        {item?.units && (
                                            <Pill type={PillType.UNITS} text={`${item.units} units`} />
                                        )}
                                    </div>
                                </div>
                                <div className='subject-item__actions'>
                                    {onRemove && <span onClick={() => onRemove(item)} className='x-element'></span>}
                                </div>
                            </div>
                        );
                    })}
            </div>
            <div className='subject-rules subject-rules__container'>
                <h3 className='fw-bold'>Your Subject Selection Guidelines</h3>
                <span className='subject-rules__description'>
                    You must check off each guideline to confirm you have read and understood it before submitting your
                    subjects.
                </span>
                <div className='subject-rules__item-container pe-2'>
                    {rules?.map((rule) => {
                        const selected = selectedRules?.includes(rule.id);

                        const checkboxClass = classNames('item__checkbox', {
                            'item__checkbox--selected': selected,
                        });

                        return (
                            <div key={rule.id} className='item' onClick={() => onRuleSelected?.(rule.id)}>
                                <span>{rule.name}</span>
                                <div className={checkboxClass}></div>
                            </div>
                        );
                    })}
                </div>
                {onSubmit && (
                    <div className='d-flex justify-content-center'>
                        <Button
                            disabled={
                                status !== SubjectPreferenceStatus.IN_PROGRESS ||
                                !!!items.length ||
                                rules?.length !== selectedRules?.length ||
                                isSubmitting
                            }
                            spinner={isSubmitting}
                            className='submit-preferences'
                            title='Submit'
                            type={ButtonType.SECONDARY}
                            onClick={onSubmit}
                        />
                    </div>
                )}
            </div>
        </div>
    );
}
