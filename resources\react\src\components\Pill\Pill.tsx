import { log } from '@/services/Logger.service';
import { PillType } from '@/utils/enums';
import classNames from 'classnames';

interface PillProps {
    className?: string;
    style?: React.CSSProperties;
    type?: string;
    course_type?: string;
    text: string;
}

const Pill = ({ type, course_type, text, className, style }: PillProps) => {
    const validPillTypes: string[] = [PillType.IBDP, PillType.HSC, PillType.COURSE_TYPE, PillType.UNITS, PillType.PERCENTAGE, PillType.MATCH];

    const isDefault = type && !validPillTypes.includes(type.toLocaleLowerCase());

    const pillClassNames = classNames('pill-container', {
        primary: type?.toLocaleLowerCase() === PillType.IBDP,
        secondary: type?.toLocaleLowerCase() === PillType.HSC || type?.toLocaleLowerCase() === PillType.COURSE_TYPE,
        match: type?.toLocaleLowerCase() === PillType.MATCH,
        gray: type?.toLocaleLowerCase() === PillType.UNITS || type === PillType.PERCENTAGE,
        default: isDefault,
    });

    return (
        <div style={style} className={`${pillClassNames} ${className}`}>
            {text}
        </div>
    );
};

export default Pill;
