import SubjectCard from '@/components/Cards/SubjectCard/SubjectCard';
import CourseDetails from '../../components/CourseDetails/CourseDetails';
import ContentCard from '@/components/Cards/ContentCard/ContentCard';
import { generatePath, useNavigate, useParams } from 'react-router-dom';
import {
    useQuizResults,
    useSubjectById,
    useSubjects,
    useSubmitPreferences,
} from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { useAppContextStore, useAppDispatch } from '@/context/App.context';
import { setRulesProgressByIdState, setSelectedPreferencesState } from '@/context/App.actions';
import { SUBJECT_DETAILS } from '@/router/Router.constants';
import Drawer, { useDrawer } from '@/components/portals/Drawer/Drawer';
import SubjectPreferences from '@/components/SubjectPreferences/SubjectPreferences';
import { SubjectPreference } from '../../models/SubjectSelection.model';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import { SubjectPreferenceStatus, SubjectsResponse } from '../../models/SubjectSelection.api.model';
import { useEffect, useRef, useState } from 'react';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { Roles } from '@/domains/Auth/models/Auth.model';
import { useSchoolRules } from '@/domains/SubjectManagement/consumers/useSchoolsApiConsumer';
import { useSaveStudentRulesProgress } from '../../consumers/useStudentsApiConsumer';
import axios from 'axios';

const SubjectDetailsView = () => {
    const taskRef = useRef<any>(null);

    const mutation = useSubmitPreferences();
    const saveRuleProgress = useSaveStudentRulesProgress();

    const { data: user } = useUserData();
    const { id } = useParams<{ id: string }>();
    const { data: subjects } = useSubjects();

    const { data: schoolRules } = useSchoolRules({ schoolId: user?.schoolId });
    const { isLoading: isLoadingResults } = useQuizResults();
    const navigate = useNavigate();

    const dispatch = useAppDispatch();

    const { isClosing, isOpen, toggle: handleToggleClick } = useDrawer();
    const { isQuizSubmitted, selectedPreferences, selectedPreferencesStatus, ruleProgress } = useAppContextStore();
    const selectedPreferencesIds = selectedPreferences.map((subject) => subject.id);

    const [subject, setSubject] = useState<SubjectsResponse>();

    useEffect(() => {
        if (!subjects || !id) return;

        const subject = subjects.find((subject) => subject.id.toString() === id);
        setSubject(subject);
    }, [subjects, id]);

    function handlePreferencesSubmit() {
        (async () => {
            await mutation.mutateAsync(SubjectPreferenceStatus.SAVED);
            handleToggleClick();
        })();
    }

    function handleRemoveSubjectPreference(subjectPreference: SubjectPreference) {
        dispatch(setSelectedPreferencesState(subjectPreference));
    }

    function handleDelayedSubjectPreferenceClick(subject: SubjectsResponse) {
        if (!subject) return;

        const subjectPreference = {
            id: subject?.id,
            type: subject?.subject_type.name,
            course_type: subject?.course_type ? subject.course_type.name : null,
            name: subject.title,
            score: subject?.score,
            percentage: subject?.percentage,
            match: subject?.match,
            units: subject.units,
        };

        if (isPreferenceSelected(selectedPreferences, subjectPreference.id)) {
            handleRemoveSubjectPreference(subjectPreference);
            triggerBackgroundTask();

            return;
        }

        dispatch(setSelectedPreferencesState(subjectPreference));
        triggerBackgroundTask();
    }

    function handleSubjectPreferenceClick(subject: SubjectsResponse) {
        if (!subject) return;

        const subjectPreference = {
            id: subject?.id,
            type: subject?.subject_type.name,
            course_type: subject?.course_type ? subject.course_type.name : null,
            name: subject.title,
            score: subject?.score,
            percentage: subject?.percentage,
            match: subject?.match,
            units: subject.units,
        };

        if (isPreferenceSelected(selectedPreferences, subjectPreference.id)) {
            handleRemoveSubjectPreference(subjectPreference);
            mutation.mutate(SubjectPreferenceStatus.IN_PROGRESS);
            return;
        }

        dispatch(setSelectedPreferencesState(subjectPreference));
        mutation.mutate(SubjectPreferenceStatus.IN_PROGRESS);
    }

    function triggerBackgroundTask() {
        if (taskRef.current) {
            return;
        }

        taskRef.current = setTimeout(() => {
            mutation.mutate(SubjectPreferenceStatus.IN_PROGRESS);
            taskRef.current = null;
        }, 10000);
    }

    function handleRuleSelected(ruleId: number) {
        dispatch(setRulesProgressByIdState(ruleId));
    }

    const handleDrawerClose = () => {
        user && saveRuleProgress.mutate({ studentId: user.id, ruleProgress });
    };

    if (!!!subjects?.length) {
        return (
            <div className='details-container'>
                <div className='details-container__hero'></div>
                <div className='details-container__container'>
                    <div className='details-container__title container'>
                        <div className='row' style={{ width: '100%' }}>
                            <div className='col-lg-8'>
                                <h1 className='mb-6'>Subject Detail</h1>
                                <span className='fs-5' style={{ color: 'white' }}>
                                    Loading subject{' '}
                                    <div
                                        className='spinner-border'
                                        style={{ width: '1rem', height: '1rem' }}
                                        role='status'
                                    />
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className='details-container'>
            <div className='details-container__hero'></div>
            <div className='details-container__container'>
                <div className='details-container__title container'>
                    <div className='row' style={{ width: '100%' }}>
                        <div className='col-lg-8'>
                            <h1 className='mb-6'>Subject Detail</h1>
                            <h2>
                                {subject?.subject_type.name} {subject?.title}
                            </h2>
                        </div>
                    </div>
                </div>
                <div className='details-container__details container'>
                    <div className='row'>
                        <div className='col-lg-8' style={{ position: 'relative' }}>
                            {subject && (
                                <CourseDetails
                                    subject={subject}
                                    isSelected={isPreferenceSelected(selectedPreferences, subject.id)}
                                    onAddSubjectPreferenceClick={() => handleSubjectPreferenceClick(subject)}
                                    isTeacher={user?.role === Roles.Teacher}
                                    isLoading={isLoadingResults}
                                />
                            )}
                            <div className='d-flex justify-content-between details-container__description'>
                                <div className='details-container__paragraph my-8'>
                                    {(subject?.description && subject?.description !== '' && (
                                        <div dangerouslySetInnerHTML={{ __html: subject?.description || '' }} />
                                    )) || (
                                        <>
                                            <h3>Description</h3>
                                            <p>No subject description</p>
                                        </>
                                    )}
                                </div>
                            </div>
                            {!!subject?.related_subjects.length && (
                                <div className='details-container__paragraph'>
                                <h3>Related Subjects</h3>
                                <div className='container-fluid g-0'>
                                    {(subject?.related_subjects.length === 0 && <p>No related subjects</p>) || (
                                        <div className='row pb-3'>
                                            {subject?.related_subjects.map((subject) => (
                                                <div key={subject.id} className='col-xl-6 col-md-6 col-sm-12'>
                                                    <SubjectCard
                                                        hasAddButton
                                                        imageUrl={subject.featured_image_url}
                                                        title={subject.title}
                                                        type={subject.subject_type.name}
                                                        course_type={subject.course_type ? subject.course_type.name : null}
                                                        onClick={() => handleDelayedSubjectPreferenceClick(subject)}
                                                        selected={selectedPreferencesIds.includes(subject.id)}
                                                        units={subject.units}
                                                        onDetailsClick={() =>
                                                            navigate(
                                                                generatePath(SUBJECT_DETAILS, {
                                                                    id: subject?.id.toString(),
                                                                }),
                                                            )
                                                        }
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                            )}
                            {!!subject?.pre_requisites.length && (
                                <div className='details-container__paragraph'>
                                    <h3>Prerequisites</h3>
                                    <div className='container-fluid g-0'>
                                        <div className='row pb-3'>
                                            {subject?.pre_requisites.map((subject) => (
                                                <div key={subject.id} className='col-xl-6 col-md-6 col-sm-12'>
                                                    <SubjectCard
                                                        hasAddButton
                                                        imageUrl={subject.featured_image_url}
                                                        title={subject.title}
                                                        type={subject.subject_type.name}
                                                        course_type={subject.course_type ? subject.course_type.name : null}
                                                        units={subject.units}
                                                        onClick={() => handleDelayedSubjectPreferenceClick(subject)}
                                                        selected={selectedPreferencesIds.includes(subject.id)}
                                                        onDetailsClick={() =>
                                                            navigate(
                                                                generatePath(SUBJECT_DETAILS, {
                                                                    id: subject?.id.toString(),
                                                                }),
                                                            )
                                                        }
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            )}
                            {!!subject?.related_content.length && (
                            <div className='details-container__paragraph'>
                                <h3>Related Content</h3>
                                <div className='container-fluid gx-0'>
                                    {(subject?.related_content.length === 0 && <p>No related content</p>) || (
                                        <div className='row justify-content-between'>
                                            {subject?.related_content.map((content) => (
                                                <div
                                                    key={content.id}
                                                    style={{ width: 280 }}
                                                    className='col-xxl-3 col-xl-6 col-lg-6 col-sm-12 pb-5'
                                                >
                                                    <ContentCard
                                                        mainTitle={content.type}
                                                        subTitle={content.title}
                                                        route={`/exploreindustries/${content.industry_id}/unit/${content.id}`}
                                                        imagePath={content.banner_url}
                                                    />
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                            )}
                        </div>
                    </div>
                </div>
                <div className='col-lg-8'>
                    {!isOpen && user?.role !== Roles.Teacher && (
                        <Button
                            className='button-fixed fs-4 fw-normal'
                            title='my subjects'
                            onClick={handleToggleClick}
                            type={ButtonType.PRIMARY}
                        />
                    )}
                    <Drawer
                        isClosing={isClosing}
                        isOpen={isOpen}
                        onClose={handleDrawerClose}
                        handleToggleClick={() => {
                            handleToggleClick();
                            triggerBackgroundTask();
                        }}
                    >
                        <SubjectPreferences
                            status={selectedPreferencesStatus}
                            items={selectedPreferences}
                            selectedRules={ruleProgress}
                            rules={schoolRules}
                            onRemove={handleRemoveSubjectPreference}
                            onSubmit={handlePreferencesSubmit}
                            isQuizSubmitted={isQuizSubmitted}
                            isSubmitting={mutation.isLoading}
                            onRuleSelected={handleRuleSelected}
                        />
                    </Drawer>
                </div>
                <div className='details-container__footer'></div>
            </div>
        </div>
    );
};

export default SubjectDetailsView;

function isPreferenceSelected(selectedPreferences: SubjectPreference[], preferenceId: number) {
    const preference = selectedPreferences.find((selectedPreference) => selectedPreference.id === preferenceId);

    return !!preference;
}
