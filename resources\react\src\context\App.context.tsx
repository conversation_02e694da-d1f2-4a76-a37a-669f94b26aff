import AppReducer from './App.reducer';
import { AppState } from './App.types';

import { ContextNames } from '@/utils/enums';
import makeContextStore from '@/utils/makeContextStore';

const initialState: AppState = {
    user: undefined,
    isQuizSubmitted: false,
    isPreferencesSubmitted: false,
    mappedSubjects: undefined,
    selectedPreferencesStatus: undefined,
    selectedPreferences: [],
    submittedQuizSelection: undefined,
    ruleProgress: [],
};

const [AppContextProvider, useAppContextStore, useAppDispatch] = makeContextStore(
    AppReducer,
    initialState,
    ContextNames.APP_GLOBAL,
);

export { AppContextProvider, useAppContextStore, useAppDispatch };
