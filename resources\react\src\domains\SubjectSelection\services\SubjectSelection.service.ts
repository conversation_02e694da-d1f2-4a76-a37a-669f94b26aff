import { AxiosResponse } from 'axios';
import ApiService from '@/services/Api.decode.service';
import {
    SubjectPreferencesRequestParams,
    SubjectPreferencesResponse,
    SubjectSelectionQuizResponse,
    SubjectSubmissionsResponse,
    SubjectsResponse,
} from '../models/SubjectSelection.api.model';
import { SubjectSelectionQuizState } from '../models/SubjectSelection.model';

/**
 * Handles student side subject selection quiz and subject preferences
 */
export class SubjectSelectionService {
    private apiService: ApiService;

    private static instance: SubjectSelectionService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): SubjectSelectionService {
        if (!this.instance) {
            this.instance = new SubjectSelectionService();
        }
        return this.instance;
    }

    public getQuiz(): Promise<AxiosResponse<SubjectSelectionQuizResponse>> {
        return this.apiService.get({ url: '/subject-selections-quiz' });
    }

    public getCourseType(): Promise<AxiosResponse<SubjectSelectionQuizResponse>> {
        return this.apiService.get({ url: '/getAllCourseType' });
    }

    public getQuizAnswers(): Promise<AxiosResponse<{ data: SubjectSubmissionsResponse }>> {
        return this.apiService.get({ url: '/subject-selections-quiz/inputs' });
    }

    public submitQuiz(data: SubjectSelectionQuizState): Promise<AxiosResponse<{ data: SubjectSubmissionsResponse }>> {
        return this.apiService.post({ url: '/subject-selections-quiz/inputs', data });
    }

    public getQuizStatus(): Promise<
        AxiosResponse<{ attachment_url: string; attachment: string; completed: boolean; data: SubjectsResponse[] }>
    > {
        return this.apiService.get({ url: '/subject-selections-quiz/status' });
    }

    public getSubjectPreferences(): Promise<AxiosResponse<{ data: SubjectPreferencesResponse }>> {
        return this.apiService.get({ url: '/subject-preferences' });
    }

    public submitSubjectPreferences(
        data: SubjectPreferencesRequestParams,
    ): Promise<AxiosResponse<{ data: SubjectPreferencesResponse }>> {
        return this.apiService.post({ url: '/subject-preferences', data });
    }

    public getSubjects(): Promise<AxiosResponse<{ data: SubjectsResponse[] }>> {
        return this.apiService.get({ url: '/subjects?year[]=5&year[]=6' });
    }

    public updateSubject(subjectId: string): Promise<AxiosResponse<{ data: SubjectsResponse }>> {
        return this.apiService.patch({ url: `/subjects/${subjectId}`, data: { selected: true } });
    }

    public getSubjectById(subjectId: string): Promise<AxiosResponse<{ data: SubjectsResponse }>> {
        return this.apiService.get({ url: `/subjects/${subjectId}` });
    }
}
