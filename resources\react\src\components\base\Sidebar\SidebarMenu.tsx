import { NavigationPages, NavigationSubPages } from '@/common/models/Navigation.model';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { Roles, UserResponse } from '@/domains/Auth/models/Auth.model';
import axios from 'axios';
import React, { FunctionComponent, SVGProps, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import studentNavigation from '../../../common/navigation/studentNavigation';
import Intercom from '@intercom/messenger-js-sdk';

interface Props {
    navigation: NavigationPages[];
}

export default function SidebarMenu({ navigation }: Props) {
    const [activeMenu, setActiveMenu] = useState<number>();
    const { isLoading, data: user } = useUserData();


    const toggleSubMenu = (index: number) => {
        if (activeMenu === index) {
            setActiveMenu(undefined);
        } else {
            setActiveMenu(index);
        }
    };

     const saveSession = () => {
        axios.get('/updateUserSession')
          .then(response => {
            console.log('Session updated');
          })
          .catch(error => {
            console.error('Error updating session:', error);
          });
      };


    // INTERCOM - START
    const bootIntercom = () => {
        if (user?.intercom) {
            Intercom({
                app_id: process.env.MIX_INTERCOM_APP_ID ? process.env.MIX_INTERCOM_APP_ID : '',
                region: 'ap',
                user_id: user.intercom.uuid, // IMPORTANT: Replace "user.id" with the variable you use to capture the user's ID
                name: user.name, // IMPORTANT: Replace "user.name" with the variable you use to capture the user's name
                email: user.email, // IMPORTANT: Replace "user.email" with the variable you use to capture the user's email
            });
        } else {
            console.log('No user intercom data found: REACT');
        }
    };

    const checkIntercomStatus = async () => {
        if (user?.intercom == null) {
            console.log('Intercom is Disabled on REACT , No user intercom data found');
            return;
        }

        try {
            const { data } = await axios.get('/api/intercom-status')
            
            if (data.is_enabled) {
                bootIntercom();
                console.log('Intercom is Enabled on REACT');
            } else {
                console.log('Intercom is Disabled on REACT');
            }
        } catch (error) {
            console.error("Error fetching Intercom status REACT :", error);
        }
    }; 

    const shutdownIntercom = () => {
        if (window.Intercom) {
            window.Intercom('shutdown');
        }
    };

    useEffect(() => {
        checkIntercomStatus();

        return () => {
            shutdownIntercom();
        };
    }, []);
    // INTERCOM - END


      useEffect(() => {
        if (user && user.role !== Roles.Admin) {
          saveSession();
          const intervalId = setInterval(() => {
            saveSession();
          }, 60000);

          return () => clearInterval(intervalId);
        }
      }, [user]);

    return (
        // <!--begin::sidebar menu-->
        <div className='aside-menu flex-column-fluid py-1'>
            {/* <!--begin::Menu wrapper--> */}
            <div
                id='kt_app_sidebar_menu_wrapper'
                className='app-sidebar-wrapper sidebar-menu hover-scroll-overlay-y my-20'
                data-kt-scroll='true'
                data-kt-scroll-activate='true'
                data-kt-scroll-height='auto'
                data-kt-scroll-dependencies='#kt_app_sidebar_logo, #kt_app_sidebar_footer'
                data-kt-scroll-wrappers='#kt_app_sidebar_menu'
                data-kt-scroll-offset='5px'
                data-kt-toggle='true'
                data-kt-scroll-save-state='true'
            >
                {/* <!--begin::Menu--> */}
                <div
                    id='#kt_app_sidebar_menu'
                    className='menu menu-column menu-title-gray-700
                    menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary
                    menu-arrow-gray-500 fw-semibold'
                    data-kt-menu='true'
                >
                    {navigation.map((item, index) => {
                        return (
                            <div key={`item-${index}`} className='menu-item py-5' onClick={() => toggleSubMenu(index)}>
                                <MenuItemLink item={item} svgIcon={item.svgIcon} />
                                {item.sub && activeMenu === index && (
                                    <div className='menu-sub-item__container fade-in'>
                                        {item.sub.map((subMenuItem, subMenuIndex) => {
                                            return <MenuSubItemLink key={subMenuIndex} item={subMenuItem} />;
                                        })}
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
                {/* <!--end::Menu--> */}
            </div>
            {/* <!--end::Menu wrapper--> */}
        </div>
        // <!--end::sidebar menu-->
    );
}

// ATOM components for SidebarMenu

interface HeadingProps {
    title?: string;
}

function MenuItemHeading({ title }: HeadingProps) {
    return (
        <div className='menu-item pt-5'>
            <div className='menu-content'>
                <span className='menu-heading fw-bold text-uppercase fs-7'>{title || ''}</span>
            </div>
        </div>
    );
}

interface MenuItemLinkProps {
    item: NavigationPages;
    svgIcon: FunctionComponent<SVGProps<SVGSVGElement>>;
}

function MenuItemLink({ item, svgIcon }: MenuItemLinkProps) {
    const Icon = svgIcon;

    const { title, route, sub } = item;

    function handleLinkClick(event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) {
        if (sub) {
            return;
        }

        event.currentTarget.href = `${window.location.origin}${route}`;
    }

    if (item.isExternal) {
        return (
            <a onClick={handleLinkClick} className='menu-link menu-center flex-column'>
                <span className='menu-icon m-0'>
                    <i className='bi fs-1'></i>
                    <span className='svg-icon svg-icon-2'>
                        <Icon />
                    </span>
                </span>
                <span className='menu-title'>{title || ''}</span>
            </a>
        );
    }

    return (
        <Link to={route} className='menu-link menu-center flex-column'>
            <span className='menu-icon m-0'>
                <i className='bi fs-1'></i>
                <span className='svg-icon svg-icon-2'>
                    <Icon />
                </span>
            </span>
            <span className='menu-title'>{title || ''}</span>
        </Link>
    );
}

interface MenuSubItemLinkProps {
    item: NavigationSubPages;
}

function MenuSubItemLink({ item }: MenuSubItemLinkProps) {
    if (item.isExternal) {
        return <a href={`${window.location.origin}${item.route}`}>{item.title}</a>;
    }

    return <Link to={item.route}>{item.title}</Link>;
}
