import { FilterApi } from '@/common/models/Api.model';
import ApiService from '@/services/Api.decode.service';
import { SubjectGalleryItemRequest, SubjectGalleryItemResponse } from '../models/Subjects.service.api.model';
import { AxiosResponse } from 'axios';

export class SubjectsService {
    private apiService: ApiService;

    private static instance: SubjectsService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): SubjectsService {
        if (!this.instance) {
            this.instance = new SubjectsService();
        }
        return this.instance;
    }

    public getSubjectGallery({
        subjectId,
        filter,
    }: {
        subjectId: number;
        filter?: FilterApi;
    }): Promise<AxiosResponse<{ data: SubjectGalleryItemResponse[] }>> {
        const params = {
            ...filter,
        };

        return this.apiService.get({ url: `/subjects/${subjectId}/gallery`, config: { params } });
    }

    public orderGalleryItemsInSubject({ subjectId, order }: { subjectId: number; order: { id: number }[] }) {
        return this.apiService.post({
            url: `/subjects/${subjectId}/gallery/order`,
            data: {
                order,
            },
        });
    }

    public addGalleryItemToSubject({ subjectId, data }: { subjectId: number; data: SubjectGalleryItemRequest }) {
        return this.apiService.post({
            url: `/subjects/${subjectId}/gallery`,
            data,
        });
    }

    public updateGalleryItemInSubject({
        subjectId,
        galleryItemId,
        data,
    }: {
        subjectId: number;
        galleryItemId: number;
        data: Partial<SubjectGalleryItemRequest>;
    }) {
        return this.apiService.patch({
            url: `/subjects/${subjectId}/gallery/${galleryItemId}`,
            data,
        });
    }

    public addGalleryImageToSubject({ subjectId, data }: { subjectId: number; data: SubjectGalleryItemRequest }) {
        const formData = new FormData();
        formData.append('file', data.file as File);
        formData.append('caption', data.caption);
        formData.append('description', data.description);
        formData.append('type', data.type);

        return this.apiService.post({
            url: `/subjects/${subjectId}/gallery/upload-image`,
            data: formData,
            config: {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            },
        });
    }

    public updateGalleryItemImageInSubject({
        subjectId,
        galleryItemId,
        data,
    }: {
        subjectId: number;
        galleryItemId: number;
        data: SubjectGalleryItemRequest;
    }) {
        const formData = new FormData();
        formData.append('file', data.file as File);
        formData.append('caption', data.caption);
        formData.append('description', data.description);
        formData.append('type', data.type);

        return this.apiService.post({
            url: `/subjects/${subjectId}/gallery/${galleryItemId}/edit`,
            data: formData,
            config: {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            },
        });
    }

    public removeGalleryItemsFromSubject(subjectId: number, galleryItemIds: number[]) {
        return this.apiService.delete({
            url: `/subjects/${subjectId}/gallery`,
            config: {
                data: {
                    ids: galleryItemIds,
                },
            },
        });
    }
}
