<?php

namespace App;

use App\Traits\UserSessionStatsTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Newsletter;
use Illuminate\Support\Facades\Cache;
use Overtrue\LaravelFavorite\Traits\Favoriter;
use App\Events\UserUpdated;
use App\Traits\HasIntercomUserInfo;

class Student extends Model
{
    use Favoriter, HasIntercomUserInfo;
    use UserSessionStatsTrait;

    protected $table = "users";
    protected $hidden = [
        'password',
        'remember_token',
        'stripe_id',
        'card_brand',
        'card_last_four',
        'trial_ends_at',
    ];
    protected $guarded = [];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['profile'];

    protected static function booted()
    {

        static::saved(function ($student) {
            event(new UserUpdated($student));
        });

        static::deleting(function ($student) {

            if ($student->industryunitGethelps) {
                $student->industryunitGethelps()->delete();
            }

            if ($student->industryunitSendtoparents) {
                $student->industryunitSendtoparents()->delete();
            }

            if ($student->unitFeedbacks) {
                $student->unitFeedbacks()->delete();
            }

            if ($student->templateProgress) {
                $student->templateProgress()->delete();
            }

            if ($student->checklistData) {
                $student->checklistData()->delete();
            }
            if ($student->thingstoknowChecklistData) {
                $student->thingstoknowChecklistData()->delete();
            }

            if ($student->sendaletterData) {
                $student->sendaletterData()->delete();
            }

            if ($student->childInvitations) {
                $student->childInvitations()->delete();
            }

            if ($student->parentInvitations) {
                $student->parentInvitations()->delete();
            }

            if ($student->notes) {
                $student->notes()->delete();
            }
            if ($student->subjects) {
                $student->subjects()->detach();
            }
            if ($student->courses) {
                $student->courses()->detach();
            }
            // if ($student->companies) {
            //     $student->companies()->delete();
            // }

            if ($student->workexperienceResponses) {
                $student->workexperienceResponses()->delete();
            }

            if ($student->lessonresponses) {
                $student->lessonresponses()->delete();
            }

            if ($student->workhealthsafetyResponses) {
                $student->workhealthsafetyResponses()->delete();
            }

            if ($student->skillstrainingResponses) {
                $student->skillstrainingResponses()->delete();
            }

            if ($student->resumesinterviewsResponses) {
                $student->resumesinterviewsResponses()->delete();
            }

            if ($student->experiences) {
                $student->experiences()->delete();
            }

            if ($student->activity) {
                $student->activity()->delete();
            }

            if ($student->cvs) {
                $student->cvs()->delete();
            }

            // if ($student->scholarships) {
            //     $student->scholarships()->detach();
            // }

            if ($student->industryunits) {
                $student->industryunits()->detach();
            }

            if ($student->thingstoknow) {
                $student->thingstoknow()->detach();
            }

            if ($student->scholarshipApplications) {
                $student->scholarshipApplications()->delete();
            }

            if ($student->campuses) {
                $student->campuses()->detach();
            }

            if ($student->orgCampuses) {
                $student->orgCampuses()->detach();
            }

            if ($student->nominees) {
                foreach ($student->nominees as $nominee) {
                    $nominee->traitResults()->delete();
                }
                $student->nominees()->delete();
            }

            if ($student->nonStudentPlans) {
                $student->nonStudentPlans()->delete();
            }

            if ($student->plans) {
                foreach ($student->plans as $plan) {
                    if ($plan->institituteOthers) {
                        $plan->institituteOthers()->delete();
                    }
                    if ($plan->tafes) {
                        $plan->tafes()->detach();
                    }
                    if ($plan->universities) {
                        $plan->universities()->detach();
                    }
                    if ($plan->colleges) {
                        $plan->colleges()->detach();
                    }
                    if ($plan->industries) {
                        $plan->industries()->detach();
                    }
                    $plan->delete();
                }
            }

            if ($student->sessions) {
                $student->sessions()->delete();
            }

            if ($student->parents) {
                $student->parents()->detach();
            }


            if (Newsletter::hasMember($student->email)) {
                Newsletter::unsubscribe($student->email, 'students');
                Newsletter::delete($student->email, 'students');
            }

            if ($student->profile) {
                $student->profile->delete();
            }
            // if ($student->school) {
            //     SchoolDetail::where("school_id", $student->school_id)->orWhere("school_id", $student->organisation_id)->decrement("student_count");
            // }
        });


        static::addGlobalScope('student', function (Builder $builder) {
            $builder->where('role_id', Role::whereName('Student')->value('id'));
        });
    }

    public function getEmailAttribute($value)
    {
        return (filter_var($value, FILTER_VALIDATE_EMAIL)) ? $value : "";
    }

    public function role()
    {
        return $this->hasOne(Role::class);
    }

    public function profile()
    {
        return $this->hasOne(Profile::class, 'user_id');
    }

    public function sessions()
    {
        return $this->hasMany(UserSession::class, 'user_id')->whereNotNull('duration');
    }

    public function lastSession()
    {
        return $this->hasOne(UserSession::class)->whereNotNull('duration')->latest();
    }

    public function login()
    {
        return $this->hasOne(Login::class, 'user_id');
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function organisation()
    {
        return $this->belongsTo(Organisation::class);
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function childInvitations()
    {
        return $this->hasMany(ChildInvitee::class, 'child_id');
    }

    public function parentInvitations()
    {
        return $this->hasMany(ParentInvitee::class, 'child_id');
    }

    public function notes()
    {
        return $this->hasMany(Note::class, 'user_id');
    }

    public function unitFeedbacks()
    {
        return $this->hasMany(UnitFeedback::class, 'user_id');
    }

    public function nonStudentPlans()
    {
        return $this->hasMany(NonStudentPlan::class, 'user_id');
    }

    public function plans()
    {
        return $this->hasMany(Plan::class, 'user_id');
    }

    public function activity()
    {
        return $this->hasMany(ActivityLog::class, 'causer_id');
    }

    public function nominees()
    {
        return $this->hasMany(Nominee::class);
    }

    public function templateProgress()
    {
        return $this->hasMany(StudentTemplateProgress::class, 'student_id');
    }

    public function checklistData()
    {
        return $this->hasMany("App\ChecklistData", 'student_id');
    }

    public function thingstoknowChecklistData()
    {
        return $this->hasMany("App\ThingstoknowChecklistData", 'student_id');
    }

    public function sendaletterData()
    {
        return $this->hasMany("App\SendaletterData", 'student_id');
    }

    public function subjects()
    {
        return $this->belongsToMany("App\Subject", 'gameplan_subjects', 'user_id');
    }

    public function courses()
    {
        return $this->belongsToMany("App\Course", 'gameplan_courses', 'user_id');
    }

    // public function companies()
    // {
    //     return $this->hasMany("App\GameplanCompany", 'user_id');
    // }

    public function cvs()
    {
        return $this->hasMany(Cv::class, 'student_id');
    }

    // public function scholarships()
    // {
    //     return $this->belongsToMany(Scholarship::class, 'scholarship_user', 'user_id');
    // }

    public function scholarshipApplications()
    {
        return $this->hasMany(Scholarshipapplication::class, 'user_id');
    }

    public function portfolioSetting()
    {
        return $this->has(Setting::class, 'user_id');
    }

    /**
     * Determine if this is users first login.
     *
     * @return bool
     */
    public function isFirstLogin()
    {
        if (!$this->profile->firstlogin) {
            return true;
        }
        return (Carbon::createFromFormat('Y-m-d H:i:s', $this->profile->firstlogin) === false);
    }

    public function hasProAccess()
    {

        if ($this->parents->isEmpty()) {
            $school = $org = false;
            if ($this->school_id && School::find($this->school_id)->plans()->exists()) {
                $yearId = $this->profile->standard_id;
                $school = School::where('id', $this->school_id)->with(['plans' => function ($q) {
                    $q->whereType('Premium');
                }])->first();
                if ($school->plans->isNotEmpty()) {
                    // if ($school->plans[0]->pivot->{'year_' . $yearId} == '1') {  //before primary plans
                    //     $school = true;
                    // }
                    foreach ($school->plans as $plan) {
                        if ($plan->pivot->{'year_' . $yearId} == '1') {
                            $school = true;
                        }
                    }
                } else {
                    $school = false;
                }
            } else {
                $school = false;
            }
            if ($this->organisation_id && Organisation::find($this->organisation_id)->plans()->exists()) {
                $yearId = $this->profile->standard_id;
                $school = Organisation::where('id', $this->organisation_id)->with(['plans' => function ($q) {
                    $q->whereType('Premium');
                }])->first();
                if ($school->plans->isNotEmpty()) {
                    if ($school->plans[0]->pivot->{'year_' . $yearId} == '1') {
                        $org = true;
                    }
                } else {
                    $org = false;
                }
            } else {
                $org = false;
            }

            return $school || $org;
        }

        return true;
    }

    public function profilerResult()
    {
        return $this->hasOne(StudentModuleResult::class, 'user_id')->where('module_id', Module::where('slug', 'profiler')->value('id'));
    }

    public function workexperienceResponses()
    {
        return $this->hasMany(WorkexperienceResponse::class);
    }

    public function lessonresponses()
    {
        return $this->hasMany(Lessonresponse::class);
    }

    public function workhealthsafetyResponses()
    {
        return $this->hasMany(WorkhealthsafetyResponse::class);
    }

    public function skillstrainingResponses()
    {
        return $this->hasMany(SkillstrainingResponse::class);
    }

    public function resumesinterviewsResponses()
    {
        return $this->hasMany(ResumesinterviewsResponse::class);
    }

    public function experiences()
    {
        return $this->hasMany(UserExperience::class, 'user_id');
    }

    public function parents()
    {
        return $this->belongsToMany(ChildParent::class, 'child_child_parent', 'child_id', 'parent_id');
    }

    public function getHasCompletedProfilerAttribute()
    {
        return $this->profilerResult()->exists();
    }

    public function getIsChildAttribute()
    {
        return $this->parents()->exists();
    }

    public function getIsIndividualAttribute()
    {
        $user = User::find($this->id);
        return $user->subscribed('Individual');
    }

    public function getCampusAttribute()
    {
        return @$this->campuses->first(); // not campuses()->first(); as it will run the query everytime
    }

    public function campuses()
    {
        return $this->belongsToMany(Campus::class, 'campus_user', 'user_id');
    }

    public function getOrgCampusAttribute()
    {
        return @$this->orgCampuses->first(); // not orgCampuses()->first(); as it will run the query everytime
    }

    public function orgCampuses()
    {
        return $this->belongsToMany(Campus::class, 'orgcampus_user', 'user_id');
    }

    public function getLoginCountAttribute()
    {
        return $this->sessions()->count();
    }


    public function getAvgSessionDurationAttribute()
    {
        return Cache::remember('avgsession-'.$this->id, 30, function () {
            return round($this->sessions()->avg('duration'));
        });
    }

    // public function getLoginCountAttribute()
    // {
    //     return $this->login->total_count;
    // }


    // public function getAvgSessionDurationAttribute()
    // {
    //     $login = $this->login;
    //     return round($login->total_duration/$login->total_count);
    // }

    public function industryunits()
    {
        return $this->belongsToMany(Industryunit::class, 'industryunit_user', 'user_id');
    }

    public function thingstoknow()
    {
        return $this->belongsToMany(ThingstoknowTemplate::class, 'thingstoknow_template_user', 'user_id');
    }

    public function industryunitSendtoparents() {
        return $this->hasMany(IndustryunitSendtoparent::class, 'user_id');
    }

    public function industryunitGethelps() {
        return $this->hasMany(IndustryunitGethelp::class, 'user_id')->where('type', 'Student');
    }

    public function ssqRules() {
        return $this->belongsToMany(SsqRule::class, 'student_rule', 'student_id', 'ssq_rule_id');
    }
}
