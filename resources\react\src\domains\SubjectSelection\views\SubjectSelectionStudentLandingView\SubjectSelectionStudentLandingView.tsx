import Button from '@/components/base/Button/Button';
import SubjectSelectionLandingLayout from '@/components/layouts/SubjectSelectionLandingLayout/SubjectSelectionLandingLayout';
import { SUBJECTS_SELECTION } from '@/router/Router.constants';
import { ButtonType } from '@/utils/enums';
import { useNavigate } from 'react-router-dom';
import Drawer, { useDrawer } from '@/components/portals/Drawer/Drawer';
import SubjectPreferences from '@/components/SubjectPreferences/SubjectPreferences';
import { useAppContextStore, useAppDispatch } from '@/context/App.context';
import { SubjectPreference } from '../../models/SubjectSelection.model';
import { setRulesProgressByIdState, setSelectedPreferencesState } from '@/context/App.actions';
import { useQuizResults, useSubjects, useSubmitPreferences } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { useRef } from 'react';
import useModal from '@/hooks/useModal/useModal';
import { useSchoolGuides } from '@/domains/SubjectManagement/consumers/useSubjectManagementApiConsumer';
import SubjectResultsPdfView from '../SubjectResultsPdfView/SubjectResultsPdfView';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { SubjectPreferenceStatus } from '../../models/SubjectSelection.api.model';
import { useSchoolRules } from '@/domains/SubjectManagement/consumers/useSchoolsApiConsumer';
import { useSaveStudentRulesProgress } from '../../consumers/useStudentsApiConsumer';

export default function SubjectSelectionStudentLandingView() {
    const taskRef = useRef<any>(null);

    const mutation = useSubmitPreferences();
    const saveRuleProgress = useSaveStudentRulesProgress();

    const scrollToRef = useRef<any>(null);
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const { isLoading } = useSubjects();
    const { data: user } = useUserData();
    const { data: schoolRules } = useSchoolRules({ schoolId: user?.schoolId });
    const { data: schoolGuides } = useSchoolGuides();
    const { isLoading: isLoadingResults, data: quizResults } = useQuizResults();

    const { isClosing, isOpen, toggle: handleToggleClick } = useDrawer();
    const { isQuizSubmitted, selectedPreferences, selectedPreferencesStatus, ruleProgress } = useAppContextStore();

    const {
        isOpen: isModalOpen,
        Modal,
        toggle,
    } = useModal({
        title: 'Download your subject guides',
        onClose: () => {},
    });

    const {
        isOpen: isResultsModalOpen,
        Modal: ResultsModal,
        toggle: toggleResultsModal,
    } = useModal({
        isPage: true,
    });

    function handleRemoveSubjectPreference(subjectPreference: SubjectPreference) {
        dispatch(setSelectedPreferencesState(subjectPreference));
    }

    async function handlePreferencesSubmit() {
        await mutation.mutate(SubjectPreferenceStatus.SAVED);
        handleToggleClick();
    }

    const handleViewSubjectsClick = () => {
        scrollToRef?.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
            offsetTop: 300,
        });
    };

    function handleShareResults(): void {
        toggleResultsModal();
    }

    function triggerBackgroundTask() {
        if (taskRef.current) {
            return;
        }

        taskRef.current = setTimeout(() => {
            mutation.mutate(SubjectPreferenceStatus.IN_PROGRESS);
            taskRef.current = null;
        }, 10000);
    }
    function handleRuleSelected(ruleId: number) {
        dispatch(setRulesProgressByIdState(ruleId));
    }

    const handleDrawerClose = () => {
        user && saveRuleProgress.mutate({ studentId: user.id, ruleProgress });
    };

    return (
        <>
            <SubjectSelectionLandingLayout>
                <div className='landing-view d-flex align-items-center justify-content-center h-100'>
                    <div
                        className='col-lg-4 col-xl-4 col-xxl-4'
                        style={{
                            padding: '40px 0 40px 70px',
                            margin: '0px auto 160px 50px',
                            background: 'white',
                            width: 530,
                        }}
                    >
                        <span className='fs-3 fw-normal'>Year 11</span>
                        <h1 className='landing-view__title mb-10 fs-2 fw-normal'>Subject Selections</h1>
                        <span id='subject-guide' className='fs-6 fw-normal'>
                            Choose your subjects and submit them for review. Download your subject selection guides{' '}
                            <strong
                                className='cursor-pointer'
                                style={{ paddingBottom: 3, borderBottom: '1px solid black' }}
                                onClick={toggle}
                            >
                                here.
                            </strong>
                        </span>
                        <div className='mt-10'>
                            {!isQuizSubmitted && !quizResults?.completed && (
                                <Button
                                    style={{ maxWidth: 360, padding: '20px 0' }}
                                    className='mb-6 fs-5 fw-normal'
                                    onClick={() => navigate(SUBJECTS_SELECTION)}
                                    title={isLoadingResults ? 'Fetching status...' : 'Start Quiz'}
                                    spinner={isLoadingResults}
                                    disabled={isLoadingResults}
                                    type={ButtonType.PRIMARY}
                                />
                            )}
                            {isQuizSubmitted && !quizResults?.completed && (
                                <Button
                                    style={{ maxWidth: 360, padding: '20px 0', cursor: 'default' }}
                                    className='mb-6'
                                    title='Results Pending'
                                    disabled
                                    type={ButtonType.SECONDARY}
                                />
                            )}
                            {quizResults?.completed && (
                                <Button
                                    style={{ maxWidth: 360, padding: '20px 0' }}
                                    className='mb-6'
                                    onClick={toggleResultsModal}
                                    title='View Results Report'
                                    type={ButtonType.SECONDARY}
                                />
                            )}
                            <Button
                                style={{ maxWidth: 360, padding: '20px 0' }}
                                className='fs-5 fw-normal'
                                onClick={handleViewSubjectsClick}
                                title={isLoading ? 'Fetching Subjects...' : 'View Subjects'}
                                spinner={isLoading}
                                type={ButtonType.OUTLINED_BLACK}
                            />
                        </div>
                    </div>
                    <div className='col-lg-7'>
                        {!isOpen && (
                            <Button
                                className='button-fixed fs-4 fw-normal'
                                title='my subjects'
                                onClick={handleToggleClick}
                                type={ButtonType.PRIMARY}
                            />
                        )}
                        <Drawer
                            isClosing={isClosing}
                            isOpen={isOpen}
                            onClose={handleDrawerClose}
                            handleToggleClick={() => {
                                handleToggleClick();
                                triggerBackgroundTask();
                            }}
                        >
                            <SubjectPreferences
                                status={selectedPreferencesStatus}
                                items={selectedPreferences}
                                selectedRules={ruleProgress}
                                rules={schoolRules}
                                onRemove={handleRemoveSubjectPreference}
                                onSubmit={handlePreferencesSubmit}
                                isQuizSubmitted={isQuizSubmitted}
                                onRuleSelected={handleRuleSelected}
                            />
                        </Drawer>
                    </div>
                </div>
                {isModalOpen && (
                    <Modal>
                        {schoolGuides?.data.map((guide) => {
                            return (
                                <div key={guide.id}>
                                    <div className='d-flex flex-stack'>
                                        <a href={guide.attachment_url} target='_blank'>
                                            {guide.name}
                                        </a>
                                    </div>
                                    <div className='separator separator-dashed my-4'></div>
                                </div>
                            );
                        })}
                    </Modal>
                )}
                {!isLoading && <div style={{ marginTop: '-133px' }} ref={scrollToRef}></div>}
            </SubjectSelectionLandingLayout>
            {isResultsModalOpen && (
                <ResultsModal>
                    <SubjectResultsPdfView
                        url={quizResults?.attachment_url}
                        onClose={toggleResultsModal}
                        onShare={handleShareResults}
                        studentName={user?.name}
                        onViewSubjects={() => {
                            toggleResultsModal();
                            handleViewSubjectsClick();
                        }}
                    />
                </ResultsModal>
            )}
        </>
    );
}
