import { SubjectPreferenceStatus } from '@/domains/SubjectSelection/models/SubjectSelection.api.model';

export interface StatisticsResponse {
    completed: number;
    in_progress: number;
    approved: number;
    subject_area: {
        name: string;
        count: number;
    }[];
    subject_type: {
        name: string;
        count: number;
    }[];
    subjects: {
        name: string;
        count: number;
    }[];
}

export interface StudentsQueryParams {
    school_id?: number;
    quiz?: 'incomplete' | 'completed';
    selections?: 'not_started' | 'in_progress' | 'submitted';
}

export interface StudentsResponse {
    data: Student[];
    links: {
        first: string;
        last: string;
        prev: string | null;
        next: string | null;
    };
    meta: {
        current_page: number;
        from: number;
        last_page: number;
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        path: string;
        per_page: number;
        to: number;
        total: number;
    };
}

export interface Student {
    id: number;
    name: string;
    email: string;
    year: string;
    last_login: string;
    attachment_url: string;
    status: SubjectPreferenceStatus;
    subject_preferences: SubjectPreferenceResponse[];
    completed: boolean;
    campus:string;
}

export interface SubjectPreferenceResponse {
    id: number;
    title: string;
    subject_type: {
        id: number;
        name: string;
        state_id: number;
    };
    course_type: {
        id: number;
        name: string;
        state_id: number;
    } | null;
    units: string;
}

export interface UploadSchoolGuideRequest {
    attachments: FormData;
}

export interface SchoolGuidesResponse {
    data: Attachment[];
}

export interface Attachment {
    id: number;
    name: string;
    attachment: string;
    attachment_url: string;
}
