import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';

interface ProgressCardProps {
    statisticsNumber: number;
    text: string;
    imagePath?: string;
    onClick: () => void;
    classNames?: string;
    style?: {};
    svgIcon?: JSX.Element;
}

const ProgressCard = ({ statisticsNumber, text, style, onClick, svgIcon }: ProgressCardProps) => {
    return (
        <div className='card h-lg-100 rounded-0' style={style}>
            <div className='card-body d-flex align-items-start flex-column justify-content-between'>
                <span className='progress-card__image-container'>{svgIcon}</span>
                <div className='d-flex flex-column my-14'>
                    <span className='border-0 w-100px p-0 fw-semibold fs-2halfx text-gray-800 lh-1 ls-n2 p-22'>
                        {statisticsNumber}
                    </span>
                    <div className='mt-2'>
                        <span className='fw-semibold fs-6 text-gray-400'>{text}</span>
                    </div>
                </div>
                <div className='progress-card__button-container'>
                    <Button title='Manage' onClick={onClick} type={ButtonType.GRAY} />
                </div>
            </div>
        </div>
    );
};

export default ProgressCard;
