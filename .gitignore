# Node & Composer dependencies
/node_modules
/vendor
!/vendor/rtconner/laravel-tagging/src/Taggable.php

# Laravel environment & config
.env
.env.*.php
.phpunit.result.cache
ignition.json
Homestead.json
Homestead.yaml
.rr.yaml
todo.txt

# Laravel Mix compiled assets
# public/js/resources_*.js
# public/js/vapp.js
# public/js/rapp.js
# public/js/app.js
# public/css/app.css
# /public/css/
/public/mix-manifest.json
/public/hot
/public/storage

# Laravel storage
/storage/*.key
storage/app/*
storage/excel/*
storage/logs/*
storage/fonts
storage/debugbar
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*

# IDEs and project config
.idea/
.vscode/
.history
.phpintel
.gitignore
.roo/
.github

# Redis
/data/redis

# OS junk
.DS_Store
Thumbs.db

# Test & coverage
coverage/

# old stuff 04-04-25
# /node_modules
# /public/storage
# /public/hot
# /storage/*.key
# /vendor
# !/vendor/rtconner/laravel-tagging/src/Taggable.php
# /.idea
# ignition.json
# Homestead.json
# Homestead.yaml
# .env
# .history
# .phpintel
# storage/debugbar
# storage/app/*
# storage/excel/*
# storage/logs/*
# storage/fonts
# rr
# .rr.yaml
# todo.txt

# # Redis
# /data/redis
