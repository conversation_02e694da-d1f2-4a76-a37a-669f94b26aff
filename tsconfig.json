{"compilerOptions": {"experimentalDecorators": true, "emitDecoratorMetadata": true, "noImplicitAny": false, "target": "es6", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "allowJs": true, "paths": {"@/*": ["resources/ts/src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["resources/ts/src/**/*.ts", "resources/ts/src/**/*.vue"], "exclude": ["node_modules"]}