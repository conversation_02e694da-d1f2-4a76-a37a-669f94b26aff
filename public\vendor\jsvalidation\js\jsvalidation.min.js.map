{"version": 3, "sources": ["resources/assets/js/jsvalidation.js", "node_modules/jquery-validation/dist/jquery.validate.js", "node_modules/php-date-formatter/js/php-date-formatter.js", "es-build/helpers.js", "resources/assets/js/timezones.js", "resources/assets/js/validations.js"], "names": ["laravelValidation", "factory", "define", "amd", "module", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "extend", "fn", "validate", "options", "this", "length", "validator", "data", "attr", "settings", "onsubmit", "on", "event", "submitButton", "currentTarget", "hasClass", "cancelSubmit", "undefined", "handle", "hidden", "result", "<PERSON><PERSON><PERSON><PERSON>", "formSubmitted", "name", "val", "appendTo", "currentForm", "debug", "call", "remove", "preventDefault", "form", "pendingRequest", "focusInvalid", "window", "console", "warn", "valid", "errorList", "is", "each", "element", "concat", "rules", "command", "argument", "staticRules", "existingRules", "param", "filtered", "isContentEditable", "closest", "normalizeRule", "messages", "split", "index", "method", "normalizeRules", "classRules", "attributeRules", "dataRules", "required", "remote", "trim", "str", "replace", "called", "expr", "pseudos", "blank", "a", "filled", "unchecked", "prop", "defaults", "init", "format", "source", "params", "arguments", "args", "makeArray", "unshift", "apply", "constructor", "Array", "slice", "i", "n", "RegExp", "groups", "errorClass", "pendingClass", "validClass", "errorElement", "focusCleanup", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignore", "ignoreTitle", "customElements", "onfocusin", "lastActive", "unhighlight", "hideThese", "errorsFor", "onfocusout", "checkable", "submitted", "optional", "onkeyup", "which", "elementValue", "inArray", "keyCode", "invalid", "onclick", "parentNode", "highlight", "type", "findByName", "addClass", "removeClass", "setDefaults", "email", "url", "date", "dateISO", "number", "digits", "equalTo", "maxlength", "minlength", "rangelength", "range", "max", "min", "step", "autoCreateRanges", "prototype", "labelContainer", "errorContext", "containers", "add", "valueCache", "pending", "reset", "delegate", "eventType", "key", "value", "join", "<PERSON><PERSON><PERSON><PERSON>", "checkForm", "errorMap", "<PERSON><PERSON><PERSON><PERSON>", "showErrors", "prepareForm", "elements", "currentElements", "check", "rs", "group", "cleanElement", "clean", "checkElement", "validationTargetFor", "v", "prepareElement", "testgroup", "push", "numberOfInvalids", "toHide", "errors", "map", "message", "successList", "grep", "defaultShowErrors", "resetForm", "hideErrors", "removeData", "removeAttr", "resetElements", "objectLength", "obj", "count", "not", "text", "addWrapper", "hide", "size", "findLastActive", "filter", "trigger", "e", "rulesCache", "find", "error", "selector", "resetInternals", "toShow", "idx", "$element", "validity", "badInput", "substr", "lastIndexOf", "rule", "normalizer", "rulesCount", "dependencyMismatch", "abortRequest", "parameters", "methods", "formatAndAdd", "log", "id", "TypeError", "customDataMessage", "char<PERSON>t", "toUpperCase", "substring", "toLowerCase", "customMessage", "m", "String", "findDefined", "defaultMessage", "title", "theregex", "test", "toToggle", "wrapper", "parent", "showLabel", "success", "validElements", "show", "invalidElements", "place", "errorID", "elementID", "idOrName", "describedBy", "escapeHtml", "html", "wrap", "append", "errorPlacement", "insertAfter", "parents", "escapeCssMeta", "match", "describer", "string", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "depend", "dependTypes", "boolean", "function", "elementAjaxPort", "startRequest", "stopRequest", "port", "ajaxAbort", "previousValue", "old", "destroy", "off", "classRuleSettings", "creditcard", "addClassRules", "className", "classes", "normalizeAttributeRule", "Number", "isNaN", "getAttribute", "depends", "keepRule", "parameter", "parts", "isArray", "transformed", "addMethod", "Date", "toString", "decimalPlaces", "num", "toInt", "Math", "round", "pow", "decimals", "errorMessage", "re", "Error", "target", "optionDataString", "previous", "originalMessage", "ajax", "mode", "dataType", "context", "response", "pendingRequests", "ajaxPrefilter", "_", "xhr", "ajaxSettings", "abort", "root", "Date<PERSON><PERSON><PERSON><PERSON>", "self", "$h", "DAY", "HOUR", "dateSettings", "days", "daysShort", "months", "monthsShort", "meridiem", "ordinal", "suffixes", "1", "2", "3", "floor", "separators", "validParts", "intParts", "tzParts", "tzClip", "getInt", "radix", "parseInt", "compare", "str1", "str2", "lpad", "chr", "merge", "out", "hasOwnProperty", "getIndex", "arr", "config", "getMonth", "parseDate", "vDate", "vFormat", "vFormatParts", "vDateParts", "vDatePart", "iDatePart", "vMonth", "vMeriIndex", "vMeriOffset", "len", "mer", "vDateFlag", "vTimeFlag", "vSettings", "year", "month", "day", "hour", "sec", "splice", "indexOf", "varY", "varM", "varD", "guessDate", "vDateStr", "vYear", "iPart", "iSec", "vParts", "vDigit", "setMonth", "setDate", "getFullYear", "setFullYear", "setHours", "setMinutes", "setSeconds", "parseFormat", "vChar", "doFormat", "t", "s", "fmt", "backslash", "d", "j", "D", "w", "getDate", "l", "N", "getDay", "z", "Y", "b", "W", "F", "M", "L", "o", "y", "A", "G", "B", "H", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "g", "getHours", "h", "getMinutes", "getSeconds", "u", "getMilliseconds", "exec", "I", "UTC", "O", "tzo", "getTimezoneOffset", "abs", "P", "T", "pop", "Z", "c", "r", "U", "getTime", "formatDate", "implicitRules", "arrayRules", "arrayRulesCache", "setupValidations", "cache", "tmpRules", "nameRegExp", "newRules", "helpers", "mergeRules", "regexFromWildcard", "formMethod", "ajaxOpts", "beforeSend", "token", "setRequestHeader", "validateLocalRules", "values", "validated", "implicit", "laravelValidationRemote", "isArrayRule", "localRulesResult", "arrayValue", "arrayRulesResult", "arrayEquals", "serializeArray", "always", "textStatus", "parseErrorResponse", "JSON", "stringify", "fieldName", "errorMessages", "encode", "isEmptyObject", "__webpack_modules__", "./node_modules/locutus/php/array/array_diff.js", "arr1", "retArr", "argl", "k1", "k", "arr1keys", "./node_modules/locutus/php/datetime/strtotime.js", "reSpace", "reSpaceOpt", "reMeridian", "reHour24", "reHour24lz", "reHour12", "reMinute", "reMinutelz", "reSecond", "reSecondlz", "reFrac", "reDayfull", "re<PERSON><PERSON><PERSON><PERSON>", "reDaytext", "reReltextnumber", "reReltexttext", "reReltextunit", "reYear", "reYear4", "re<PERSON><PERSON><PERSON>", "reMonthlz", "reDay", "reD<PERSON><PERSON>z", "reMonthFull", "reMonthAbbr", "reMonthText", "reTzCorrection", "reDateNoYear", "processMeridian", "meridian", "processYear", "yearStr", "lookupMonth", "monthStr", "jan", "january", "feb", "february", "ii", "mar", "march", "iii", "apr", "april", "iv", "may", "jun", "june", "vi", "jul", "july", "vii", "aug", "august", "viii", "sep", "sept", "september", "ix", "oct", "october", "x", "nov", "november", "xi", "dec", "december", "xii", "lookupWeekday", "dayStr", "desiredSundayNumber", "mon", "monday", "tue", "tuesday", "wed", "wednesday", "thu", "thursday", "fri", "friday", "sat", "saturday", "sun", "sunday", "processTzCorrection", "tzOffset", "oldValue", "sign", "hours", "minutes", "tzAbbrOffsets", "acdt", "acst", "addt", "adt", "aedt", "aest", "ahdt", "ahst", "akdt", "akst", "amt", "apt", "ast", "awdt", "awst", "awt", "bdst", "bdt", "bmt", "bst", "cast", "cat", "cddt", "cdt", "cemt", "cest", "cet", "cmt", "cpt", "cst", "cwt", "chst", "dmt", "eat", "eddt", "edt", "eest", "eet", "emt", "ept", "est", "ewt", "ffmt", "gdt", "gmt", "gst", "hdt", "hkst", "hkt", "hmt", "hpt", "hst", "hwt", "iddt", "idt", "imt", "ist", "jdt", "jmt", "jst", "kdt", "kmt", "kst", "lst", "mddt", "mdst", "mdt", "mest", "met", "mmt", "mpt", "msd", "msk", "mst", "mwt", "nddt", "ndt", "npt", "nst", "nwt", "nzdt", "nzmt", "nzst", "pddt", "pdt", "pkst", "pkt", "plmt", "pmt", "ppmt", "ppt", "pst", "pwt", "qmt", "rmt", "sast", "sdmt", "sjmt", "smt", "sst", "tbmt", "tmt", "uct", "utc", "wast", "wat", "wemt", "west", "wet", "wib", "wita", "wit", "wmt", "yddt", "ydt", "ypt", "yst", "ywt", "f", "p", "q", "formats", "yesterday", "regex", "callback", "rd", "resetTime", "now", "noon", "time", "midnightOrToday", "tomorrow", "timestamp", "dates", "zone", "firstOrLastDay", "firstOrLastDayOfMonth", "backOrFrontOf", "side", "minute", "weekdayOf", "mssqltime", "second", "frac", "oracledate", "monthText", "JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "ymd", "timeLong12", "timeShort12", "timeTiny12", "soap", "tzCorrection", "wddx", "exif", "xmlRpc", "xmlRpcNoColon", "clf", "iso8601long", "dateTextual", "pointedDate4", "pointedDate2", "timeLong24", "dateNoColon", "pgydotd", "timeShort24", "iso8601noColon", "iso8601dateSlash", "dateSlash", "american", "americanShort", "gnuDateShortOrIso8601date2", "iso8601date4", "gnuNoColon", "times", "gnuDateShorter", "pgTextReverse", "dateFull", "dateNoDay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pgTextShort", "dateNoYear", "date<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isoWeekDay", "week", "dayOfWeek", "relativeText", "rel<PERSON><PERSON><PERSON>", "relUnit", "relTextLower", "_lookupRelative", "amount", "last", "first", "next", "third", "fourth", "fifth", "sixth", "seventh", "eight", "eighth", "ninth", "tenth", "eleventh", "twelfth", "behavior", "ri", "rh", "rm", "ry", "weekday", "weekday<PERSON><PERSON><PERSON>or", "relative", "signs", "minuses", "dayText", "relativeTextWeek", "relText", "monthFullOrMonthAbbr", "tzAbbr", "abbr", "offset", "ago", "rf", "year4", "whitespace", "dateShortWithTimeLong", "dateShortWithTimeLong12", "dateShortWithTimeShort", "dateShortWithTimeShort12", "resultProto", "NaN", "zones", "toDate", "relativeTo", "dow", "diff", "setUTCFullYear", "setUTCHours", "Object", "create", "longestMatch", "finalRule", "./node_modules/locutus/php/info/ini_get.js", "__unused_webpack_exports", "__webpack_require__", "varname", "$global", "$locutus", "php", "ini", "local_value", "./node_modules/locutus/php/strings/strlen.js", "lgth", "prev", "code", "charCodeAt", "getWholeChar", "./node_modules/locutus/php/var/is_numeric.js", "mixedVar", "__webpack_module_cache__", "moduleId", "cachedModule", "getter", "__esModule", "definition", "defineProperty", "enumerable", "get", "globalThis", "Function", "Symbol", "toStringTag", "locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0__", "locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0___default", "locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1__", "locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1___default", "locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2__", "locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2___default", "locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3__", "locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3___default", "numericRules", "fileinfo", "field<PERSON>bj", "FileName", "files", "file", "extension", "names", "hasNumericRules", "hasRules", "found", "listRules", "arrayRule", "objRules", "_rules", "strlen", "getSize", "is_numeric", "parseFloat", "getLaravelValidation", "parseTime", "dateRule", "timeValue", "strtotime", "compareDates", "operator", "timeCompare", "dependentElement", "mixed_var", "arg", "arrayDiff", "arr2", "ruleName", "el", "tagName", "errorMsg", "newResponse", "responseText", "escapeRegExp", "nameParts", "rulesList", "findByArrayName", "sqName", "lookups", "elem", "reconstructed", "allElementValues", "isTimezone", "timezones", "africa", "america", "antarctica", "arctic", "asia", "atlantic", "australia", "europe", "indian", "pacific", "tzparts", "continent", "city", "jsRemoteTimer", "Sometimes", "Bail", "Nullable", "Filled", "Required", "RequiredWith", "currentObject", "RequiredWithAll", "RequiredWithout", "RequiredWithoutAll", "RequiredIf", "RequiredUnless", "Confirmed", "Same", "InArray", "equals", "targetName", "Distinct", "Different", "Accepted", "Boolean", "Integer", "Numeric", "Digits", "DigitsBetween", "Size", "Between", "Min", "Max", "In", "keys", "NotIn", "Ip", "Email", "Url", "File", "FileReader", "FileList", "Blob", "<PERSON><PERSON>", "lowerParams", "item", "Mimetypes", "Image", "Dimensions", "fr", "onload", "img", "height", "naturalHeight", "width", "naturalWidth", "ratio", "not<PERSON><PERSON><PERSON>", "eval", "onerror", "src", "readAsDataURL", "Alpha", "AlphaNum", "AlphaDash", "Regex", "invalidModifiers", "phpReg", "matches", "php_modifiers", "DateFormat", "Before", "BeforeOrEqual", "After", "AfterOrEqual", "Timezone", "Json", "parse", "ProengsoftNoop"], "mappings": "AASA,IAAAA,mBCDA,SAAAC,GACA,mBAAAC,QAAAA,OAAAC,IACAD,OAAA,CAAA,UAAAD,GACA,iBAAAG,QAAAA,OAAAC,QACAD,OAAAC,QAAAJ,EAAAK,QAAA,WAEAL,EAAAM,QANA,CAQA,SAAAC,GAEAA,EAAAC,OAAAD,EAAAE,GAAA,CAGAC,SAAA,SAAAC,GAGA,GAAAC,KAAAC,OAAA,CAQA,IAAAC,EAAAP,EAAAQ,KAAAH,KAAA,GAAA,aACA,OAAAE,EACAA,GAIAF,KAAAI,KAAA,aAAA,cAEAF,EAAA,IAAAP,EAAAO,UAAAH,EAAAC,KAAA,IACAL,EAAAQ,KAAAH,KAAA,GAAA,YAAAE,GAEAA,EAAAG,SAAAC,WAEAN,KAAAO,GAAA,iBAAA,UAAA,SAAAC,GAIAN,EAAAO,aAAAD,EAAAE,cAGAf,EAAAK,MAAAW,SAAA,YACAT,EAAAU,cAAA,QAIAC,IAAAlB,EAAAK,MAAAI,KAAA,oBACAF,EAAAU,cAAA,KAKAZ,KAAAO,GAAA,kBAAA,SAAAC,GAOA,SAAAM,IACA,IAAAC,EAAAC,EAcA,OAPAd,EAAAO,eAAAP,EAAAG,SAAAY,eAAAf,EAAAgB,iBACAH,EAAApB,EAAA,0BACAS,KAAA,OAAAF,EAAAO,aAAAU,MACAC,IAAAzB,EAAAO,EAAAO,cAAAW,OACAC,SAAAnB,EAAAoB,gBAGApB,EAAAG,SAAAY,gBAAAf,EAAAG,SAAAkB,SACAP,EAAAd,EAAAG,SAAAY,cAAAO,KAAAtB,EAAAA,EAAAoB,YAAAd,GACAO,GAGAA,EAAAU,cAEAZ,IAAAG,GACAA,GAQA,OArCAd,EAAAG,SAAAkB,OAGAf,EAAAkB,iBAkCAxB,EAAAU,cACAV,EAAAU,cAAA,EACAE,KAEAZ,EAAAyB,OACAzB,EAAA0B,iBACA1B,EAAAgB,eAAA,GAGAJ,KAEAZ,EAAA2B,gBACA,MAKA3B,GA7FAH,GAAAA,EAAAwB,OAAAO,OAAAC,SACAA,QAAAC,KAAA,yDAgGAC,MAAA,WACA,IAAAA,EAAA/B,EAAAgC,EAgBA,OAdAvC,EAAAK,KAAA,IAAAmC,GAAA,QACAF,EAAAjC,KAAAF,WAAA6B,QAEAO,EAAA,GACAD,GAAA,EACA/B,EAAAP,EAAAK,KAAA,GAAA2B,MAAA7B,WACAE,KAAAoC,KAAA,YACAH,EAAA/B,EAAAmC,QAAArC,OAAAiC,KAEAC,EAAAA,EAAAI,OAAApC,EAAAgC,cAGAhC,EAAAgC,UAAAA,GAEAD,GAIAM,MAAA,SAAAC,EAAAC,GACA,IAEApC,EAAAqC,EAAAC,EAAAxC,EAAAyC,EAAAC,EAFAR,EAAArC,KAAA,GACA8C,OAAA,IAAA9C,KAAAI,KAAA,oBAAA,UAAAJ,KAAAI,KAAA,mBAIA,GAAA,MAAAiC,KAIAA,EAAAV,MAAAmB,IACAT,EAAAV,KAAA3B,KAAA+C,QAAA,QAAA,GACAV,EAAAlB,KAAAnB,KAAAI,KAAA,SAGA,MAAAiC,EAAAV,MAAA,CAIA,GAAAa,EAIA,OAFAE,GADArC,EAAAV,EAAAQ,KAAAkC,EAAAV,KAAA,aAAAtB,UACAkC,MACAI,EAAAhD,EAAAO,UAAAwC,YAAAL,GACAG,GACA,IAAA,MACA7C,EAAAC,OAAA+C,EAAAhD,EAAAO,UAAA8C,cAAAP,WAGAE,EAAAM,SACAP,EAAAL,EAAAlB,MAAAwB,EACAF,EAAAQ,WACA5C,EAAA4C,SAAAZ,EAAAlB,MAAAxB,EAAAC,OAAAS,EAAA4C,SAAAZ,EAAAlB,MAAAsB,EAAAQ,WAEA,MACA,IAAA,SACA,OAAAR,GAIAI,EAAA,GACAlD,EAAAyC,KAAAK,EAAAS,MAAA,MAAA,SAAAC,EAAAC,GACAP,EAAAO,GAAAT,EAAAS,UACAT,EAAAS,KAEAP,WARAH,EAAAL,EAAAlB,MACAwB,GAkCA,OAvBAxC,EAAAR,EAAAO,UAAAmD,eACA1D,EAAAC,OACA,GACAD,EAAAO,UAAAoD,WAAAjB,GACA1C,EAAAO,UAAAqD,eAAAlB,GACA1C,EAAAO,UAAAsD,UAAAnB,GACA1C,EAAAO,UAAAwC,YAAAL,IACAA,IAGAoB,WACAb,EAAAzC,EAAAsD,gBACAtD,EAAAsD,SACAtD,EAAAR,EAAAC,OAAA,CAAA6D,SAAAb,GAAAzC,IAIAA,EAAAuD,SACAd,EAAAzC,EAAAuD,cACAvD,EAAAuD,OACAvD,EAAAR,EAAAC,OAAAO,EAAA,CAAAuD,OAAAd,KAGAzC,MAKA,SAAAwD,EAAAC,GAGA,OAAAA,EAAAC,QAAA,qCAAA,IAHA,IA2uCAC,EApuCAnE,EAAAC,OAAAD,EAAAoE,KAAAC,SAAArE,EAAAoE,KAAA,KAAA,CAGAE,MAAA,SAAAC,GACA,OAAAP,EAAA,GAAAhE,EAAAuE,GAAA9C,QAIA+C,OAAA,SAAAD,GACA,IAAA9C,EAAAzB,EAAAuE,GAAA9C,MACA,OAAA,OAAAA,KAAAuC,EAAA,GAAAvC,IAIAgD,UAAA,SAAAF,GACA,OAAAvE,EAAAuE,GAAAG,KAAA,cAKA1E,EAAAO,UAAA,SAAAH,EAAA4B,GACA3B,KAAAK,SAAAV,EAAAC,QAAA,EAAA,GAAAD,EAAAO,UAAAoE,SAAAvE,GACAC,KAAAsB,YAAAK,EACA3B,KAAAuE,QAIA5E,EAAAO,UAAAsE,OAAA,SAAAC,EAAAC,GACA,OAAA,IAAAC,UAAA1E,OACA,WACA,IAAA2E,EAAAjF,EAAAkF,UAAAF,WAEA,OADAC,EAAAE,QAAAL,GACA9E,EAAAO,UAAAsE,OAAAO,MAAA/E,KAAA4E,UAGA/D,IAAA6D,IAGA,EAAAC,UAAA1E,QAAAyE,EAAAM,cAAAC,QACAP,EAAA/E,EAAAkF,UAAAF,WAAAO,MAAA,IAEAR,EAAAM,cAAAC,QACAP,EAAA,CAAAA,IAEA/E,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAC,GACAX,EAAAA,EAAAZ,QAAA,IAAAwB,OAAA,MAAAF,EAAA,MAAA,KAAA,WACA,OAAAC,OAVAX,IAgBA9E,EAAAC,OAAAD,EAAAO,UAAA,CAEAoE,SAAA,CACArB,SAAA,GACAqC,OAAA,GACA/C,MAAA,GACAgD,WAAA,QACAC,aAAA,UACAC,WAAA,QACAC,aAAA,QACAC,cAAA,EACA9D,cAAA,EACA+D,eAAAjG,EAAA,IACAkG,oBAAAlG,EAAA,IACAW,UAAA,EACAwF,OAAA,UACAC,aAAA,EACAC,eAAA,GACAC,UAAA,SAAA5D,GACArC,KAAAkG,WAAA7D,EAGArC,KAAAK,SAAAsF,eACA3F,KAAAK,SAAA8F,aACAnG,KAAAK,SAAA8F,YAAA3E,KAAAxB,KAAAqC,EAAArC,KAAAK,SAAAkF,WAAAvF,KAAAK,SAAAoF,YAEAzF,KAAAoG,UAAApG,KAAAqG,UAAAhE,MAGAiE,WAAA,SAAAjE,GACArC,KAAAuG,UAAAlE,MAAAA,EAAAlB,QAAAnB,KAAAwG,YAAAxG,KAAAyG,SAAApE,IACArC,KAAAqC,QAAAA,IAGAqE,QAAA,SAAArE,EAAA7B,GAqBA,IAAAA,EAAAmG,OAAA,KAAA3G,KAAA4G,aAAAvE,KAAA,IAAA1C,EAAAkH,QAAArG,EAAAsG,QALA,CACA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GACA,GAAA,GAAA,GAAA,GAAA,IAAA,QAKAzE,EAAAlB,QAAAnB,KAAAwG,WAAAnE,EAAAlB,QAAAnB,KAAA+G,UACA/G,KAAAqC,QAAAA,IAGA2E,QAAA,SAAA3E,GAGAA,EAAAlB,QAAAnB,KAAAwG,UACAxG,KAAAqC,QAAAA,GAGAA,EAAA4E,WAAA9F,QAAAnB,KAAAwG,WACAxG,KAAAqC,QAAAA,EAAA4E,aAGAC,UAAA,SAAA7E,EAAAkD,EAAAE,GACA,UAAApD,EAAA8E,KACAnH,KAAAoH,WAAA/E,EAAAlB,MAAAkG,SAAA9B,GAAA+B,YAAA7B,GAEA9F,EAAA0C,GAAAgF,SAAA9B,GAAA+B,YAAA7B,IAGAU,YAAA,SAAA9D,EAAAkD,EAAAE,GACA,UAAApD,EAAA8E,KACAnH,KAAAoH,WAAA/E,EAAAlB,MAAAmG,YAAA/B,GAAA8B,SAAA5B,GAEA9F,EAAA0C,GAAAiF,YAAA/B,GAAA8B,SAAA5B,KAMA8B,YAAA,SAAAlH,GACAV,EAAAC,OAAAD,EAAAO,UAAAoE,SAAAjE,IAGA4C,SAAA,CACAQ,SAAA,0BACAC,OAAA,yBACA8D,MAAA,sCACAC,IAAA,4BACAC,KAAA,6BACAC,QAAA,mCACAC,OAAA,+BACAC,OAAA,4BACAC,QAAA,qCACAC,UAAApI,EAAAO,UAAAsE,OAAA,6CACAwD,UAAArI,EAAAO,UAAAsE,OAAA,yCACAyD,YAAAtI,EAAAO,UAAAsE,OAAA,6DACA0D,MAAAvI,EAAAO,UAAAsE,OAAA,6CACA2D,IAAAxI,EAAAO,UAAAsE,OAAA,mDACA4D,IAAAzI,EAAAO,UAAAsE,OAAA,sDACA6D,KAAA1I,EAAAO,UAAAsE,OAAA,oCAGA8D,kBAAA,EAEAC,UAAA,CAEAhE,KAAA,WACAvE,KAAAwI,eAAA7I,EAAAK,KAAAK,SAAAwF,qBACA7F,KAAAyI,aAAAzI,KAAAwI,eAAAvI,QAAAD,KAAAwI,gBAAA7I,EAAAK,KAAAsB,aACAtB,KAAA0I,WAAA/I,EAAAK,KAAAK,SAAAuF,gBAAA+C,IAAA3I,KAAAK,SAAAwF,qBACA7F,KAAAwG,UAAA,GACAxG,KAAA4I,WAAA,GACA5I,KAAA4B,eAAA,EACA5B,KAAA6I,QAAA,GACA7I,KAAA+G,QAAA,GACA/G,KAAA8I,QAEA,IAEAvG,EAFAjB,EAAAtB,KAAAsB,YACAgE,EAAAtF,KAAAsF,OAAA,GAeA,SAAAyD,EAAAvI,GACA,IAcAN,EACA8I,EACA3I,EAhBAyC,OAAA,IAAAnD,EAAAK,MAAAI,KAAA,oBAAA,UAAAT,EAAAK,MAAAI,KAAA,oBAGAJ,KAAA2B,MAAAmB,IACA9C,KAAA2B,KAAAhC,EAAAK,MAAA+C,QAAA,QAAA,GACA/C,KAAAmB,KAAAxB,EAAAK,MAAAI,KAAA,SAKAkB,IAAAtB,KAAA2B,OAIAzB,EAAAP,EAAAQ,KAAAH,KAAA2B,KAAA,aACAqH,EAAA,KAAAxI,EAAA2G,KAAAtD,QAAA,YAAA,KACAxD,EAAAH,EAAAG,UACA2I,KAAArJ,EAAAK,MAAAmC,GAAA9B,EAAAyF,SACAzF,EAAA2I,GAAAxH,KAAAtB,EAAAF,KAAAQ,IAhCAb,EAAAyC,KAAApC,KAAAK,SAAAiF,OAAA,SAAA2D,EAAAC,GACA,iBAAAA,IACAA,EAAAA,EAAAhG,MAAA,OAEAvD,EAAAyC,KAAA8G,EAAA,SAAA/F,EAAAhC,GACAmE,EAAAnE,GAAA8H,MAGA1G,EAAAvC,KAAAK,SAAAkC,MACA5C,EAAAyC,KAAAG,EAAA,SAAA0G,EAAAC,GACA3G,EAAA0G,GAAAtJ,EAAAO,UAAA8C,cAAAkG,KA8BAvJ,EAAAK,KAAAsB,aACAf,GAAA,oDANA,CAAA,QAAA,oBAAA,gBAAA,SAAA,WAAA,kBAAA,kBACA,eAAA,eAAA,iBAAA,oBAAA,gBAAA,iBACA,gBAAA,gBAAA,0BAAA,iBAAA,iBACA,iBAAA,oBAAA,oBAAA,mBAGA+B,OAAAtC,KAAAK,SAAA2F,gBAAAmD,KAAA,MAAAJ,GAIAxI,GAAA,iBANA,CAAA,SAAA,SAAA,iBAAA,qBAMA+B,OAAAtC,KAAAK,SAAA2F,gBAAAmD,KAAA,MAAAJ,GAEA/I,KAAAK,SAAA+I,gBACAzJ,EAAAK,KAAAsB,aAAAf,GAAA,wBAAAP,KAAAK,SAAA+I,iBAKAzH,KAAA,WAQA,OAPA3B,KAAAqJ,YACA1J,EAAAC,OAAAI,KAAAwG,UAAAxG,KAAAsJ,UACAtJ,KAAA+G,QAAApH,EAAAC,OAAA,GAAAI,KAAAsJ,UACAtJ,KAAAiC,SACAtC,EAAAK,KAAAsB,aAAAiI,eAAA,eAAA,CAAAvJ,OAEAA,KAAAwJ,aACAxJ,KAAAiC,SAGAoH,UAAA,WACArJ,KAAAyJ,cACA,IAAA,IAAAtE,EAAA,EAAAuE,EAAA1J,KAAA2J,gBAAA3J,KAAA0J,WAAAA,EAAAvE,GAAAA,IACAnF,KAAA4J,MAAAF,EAAAvE,IAEA,OAAAnF,KAAAiC,SAIAI,QAAA,SAAAA,GACA,IAIAwH,EAAAC,EAJAC,EAAA/J,KAAAgK,MAAA3H,GACA4H,EAAAjK,KAAAkK,oBAAAH,GACAI,EAAAnK,KACAgB,GAAA,EA2CA,YAxCAH,IAAAoJ,SACAjK,KAAA+G,QAAAgD,EAAA5I,OAEAnB,KAAAoK,eAAAH,GACAjK,KAAA2J,gBAAAhK,EAAAsK,IAIAH,EAAA9J,KAAAsF,OAAA2E,EAAA9I,QAEAxB,EAAAyC,KAAApC,KAAAsF,OAAA,SAAAnE,EAAAkJ,GACAA,IAAAP,GAAA3I,IAAA8I,EAAA9I,OACA4I,EAAAI,EAAAD,oBAAAC,EAAAH,MAAAG,EAAA/C,WAAAjG,OACA4I,EAAA5I,QAAAgJ,EAAApD,UACAoD,EAAAR,gBAAAW,KAAAP,GACA/I,EAAAmJ,EAAAP,MAAAG,IAAA/I,KAMA6I,GAAA,IAAA7J,KAAA4J,MAAAK,GACAjJ,EAAAA,GAAA6I,EAEA7J,KAAA+G,QAAAkD,EAAA9I,OADA0I,EAMA7J,KAAAuK,qBAGAvK,KAAAwK,OAAAxK,KAAAwK,OAAA7B,IAAA3I,KAAA0I,aAEA1I,KAAAwJ,aAGA7J,EAAA0C,GAAAjC,KAAA,gBAAAyJ,IAGA7I,GAIAwI,WAAA,SAAAiB,GACA,IACAvK,EADAuK,IACAvK,EAAAF,KAGAL,EAAAC,OAAAI,KAAAsJ,SAAAmB,GACAzK,KAAAkC,UAAAvC,EAAA+K,IAAA1K,KAAAsJ,SAAA,SAAAqB,EAAAxJ,GACA,MAAA,CACAwJ,QAAAA,EACAtI,QAAAnC,EAAAkH,WAAAjG,GAAA,MAKAnB,KAAA4K,YAAAjL,EAAAkL,KAAA7K,KAAA4K,YAAA,SAAAvI,GACA,QAAAA,EAAAlB,QAAAsJ,MAGAzK,KAAAK,SAAAmJ,WACAxJ,KAAAK,SAAAmJ,WAAAhI,KAAAxB,KAAAA,KAAAsJ,SAAAtJ,KAAAkC,WAEAlC,KAAA8K,qBAKAC,UAAA,WACApL,EAAAE,GAAAkL,WACApL,EAAAK,KAAAsB,aAAAyJ,YAEA/K,KAAA+G,QAAA,GACA/G,KAAAwG,UAAA,GACAxG,KAAAyJ,cACAzJ,KAAAgL,aACA,IAAAtB,EAAA1J,KAAA0J,WACAuB,WAAA,iBACAC,WAAA,gBAEAlL,KAAAmL,cAAAzB,IAGAyB,cAAA,SAAAzB,GACA,IAAAvE,EAEA,GAAAnF,KAAAK,SAAA8F,YACA,IAAAhB,EAAA,EAAAuE,EAAAvE,GAAAA,IACAnF,KAAAK,SAAA8F,YAAA3E,KAAAxB,KAAA0J,EAAAvE,GACAnF,KAAAK,SAAAkF,WAAA,IACAvF,KAAAoH,WAAAsC,EAAAvE,GAAAhE,MAAAmG,YAAAtH,KAAAK,SAAAoF,iBAGAiE,EACApC,YAAAtH,KAAAK,SAAAkF,YACA+B,YAAAtH,KAAAK,SAAAoF,aAIA8E,iBAAA,WACA,OAAAvK,KAAAoL,aAAApL,KAAA+G,UAGAqE,aAAA,SAAAC,GAEA,IACAlG,EADAmG,EAAA,EAEA,IAAAnG,KAAAkG,OAIAxK,IAAAwK,EAAAlG,IAAA,OAAAkG,EAAAlG,KAAA,IAAAkG,EAAAlG,IACAmG,IAGA,OAAAA,GAGAN,WAAA,WACAhL,KAAAoG,UAAApG,KAAAwK,SAGApE,UAAA,SAAAqE,GACAA,EAAAc,IAAAvL,KAAA0I,YAAA8C,KAAA,IACAxL,KAAAyL,WAAAhB,GAAAiB,QAGAzJ,MAAA,WACA,OAAA,IAAAjC,KAAA2L,QAGAA,KAAA,WACA,OAAA3L,KAAAkC,UAAAjC,QAGA4B,aAAA,WACA,GAAA7B,KAAAK,SAAAwB,aACA,IACAlC,EAAAK,KAAA4L,kBAAA5L,KAAAkC,UAAAjC,QAAAD,KAAAkC,UAAA,GAAAG,SAAA,IACAwJ,OAAA,YACAC,QAAA,SAGAA,QAAA,WACA,MAAAC,MAOAH,eAAA,WACA,IAAA1F,EAAAlG,KAAAkG,WACA,OAAAA,GAEA,IAFAvG,EAAAkL,KAAA7K,KAAAkC,UAAA,SAAAkD,GACA,OAAAA,EAAA/C,QAAAlB,OAAA+E,EAAA/E,OACAlB,QAAAiG,GAGAwD,SAAA,WACA,IAAAxJ,EAAAF,KACAgM,EAAA,GAIA,OAAArM,EAAAK,KAAAsB,aACA2K,KAJA,CAAA,QAAA,SAAA,WAAA,qBAIA3J,OAAAtC,KAAAK,SAAA2F,gBAAAmD,KAAA,OACAoC,IAAA,sCACAA,IAAAvL,KAAAK,SAAAyF,QACA+F,OAAA,WACA,IAAA1K,EAAAnB,KAAAmB,MAAAxB,EAAAK,MAAAI,KAAA,QACA0C,OAAA,IAAAnD,EAAAK,MAAAI,KAAA,oBAAA,UAAAT,EAAAK,MAAAI,KAAA,mBAaA,OAXAe,GAAAjB,EAAAG,SAAAkB,OAAAO,OAAAC,SACAA,QAAAmK,MAAA,0BAAAlM,MAIA8C,IACA9C,KAAA2B,KAAAhC,EAAAK,MAAA+C,QAAA,QAAA,GACA/C,KAAAmB,KAAAA,GAIAnB,KAAA2B,OAAAzB,EAAAoB,gBAKAH,KAAA6K,IAAA9L,EAAAkL,aAAAzL,EAAAK,MAAAuC,YAIAyJ,EAAA7K,IAAA,OAKA6I,MAAA,SAAAmC,GACA,OAAAxM,EAAAwM,GAAA,IAGA1B,OAAA,WACA,IAAAlF,EAAAvF,KAAAK,SAAAkF,WAAArC,MAAA,KAAAiG,KAAA,KACA,OAAAxJ,EAAAK,KAAAK,SAAAqF,aAAA,IAAAH,EAAAvF,KAAAyI,eAGA2D,eAAA,WACApM,KAAA4K,YAAA,GACA5K,KAAAkC,UAAA,GACAlC,KAAAsJ,SAAA,GACAtJ,KAAAqM,OAAA1M,EAAA,IACAK,KAAAwK,OAAA7K,EAAA,KAGAmJ,MAAA,WACA9I,KAAAoM,iBACApM,KAAA2J,gBAAAhK,EAAA,KAGA8J,YAAA,WACAzJ,KAAA8I,QACA9I,KAAAwK,OAAAxK,KAAAyK,SAAA9B,IAAA3I,KAAA0I,aAGA0B,eAAA,SAAA/H,GACArC,KAAA8I,QACA9I,KAAAwK,OAAAxK,KAAAqG,UAAAhE,IAGAuE,aAAA,SAAAvE,GACA,IAGAjB,EAAAkL,EAHAC,EAAA5M,EAAA0C,GACA8E,EAAA9E,EAAA8E,KACArE,OAAA,IAAAyJ,EAAAnM,KAAA,oBAAA,UAAAmM,EAAAnM,KAAA,mBAGA,MAAA,UAAA+G,GAAA,aAAAA,EACAnH,KAAAoH,WAAA/E,EAAAlB,MAAA0K,OAAA,YAAAzK,MACA,WAAA+F,QAAA,IAAA9E,EAAAmK,SACAnK,EAAAmK,SAAAC,SAAA,MAAAF,EAAAnL,OAIAA,EADA0B,EACAyJ,EAAAf,OAEAe,EAAAnL,MAGA,SAAA+F,EAGA,mBAAA/F,EAAAsL,OAAA,EAAA,IACAtL,EAAAsL,OAAA,IAMA,IADAJ,EAAAlL,EAAAuL,YAAA,OAOA,IADAL,EAAAlL,EAAAuL,YAAA,OAJAvL,EAAAsL,OAAAJ,EAAA,GAUAlL,EAGA,iBAAAA,EACAA,EAAAyC,QAAA,MAAA,IAEAzC,IAGAwI,MAAA,SAAAvH,GACAA,EAAArC,KAAAkK,oBAAAlK,KAAAgK,MAAA3H,IAEA,IAMArB,EAAAoC,EAAAwJ,EAAAC,EANAtK,EAAA5C,EAAA0C,GAAAE,QACAuK,EAAAnN,EAAA+K,IAAAnI,EAAA,SAAA6C,EAAAD,GACA,OAAAA,IACAlF,OACA8M,GAAA,EACA3L,EAAApB,KAAA4G,aAAAvE,GAwBA,IAAAe,KApBApD,KAAAgN,aAAA3K,GAIA,mBAAAE,EAAAsK,WACAA,EAAAtK,EAAAsK,WACA,mBAAA7M,KAAAK,SAAAwM,aACAA,EAAA7M,KAAAK,SAAAwM,YAMAA,IACAzL,EAAAyL,EAAArL,KAAAa,EAAAjB,UAGAmB,EAAAsK,YAGAtK,EAAA,CACAqK,EAAA,CAAAxJ,OAAAA,EAAA6J,WAAA1K,EAAAa,IACA,IAKA,GAAA,yBAJApC,EAAArB,EAAAO,UAAAgN,QAAA9J,GAAA5B,KAAAxB,KAAAoB,EAAAiB,EAAAuK,EAAAK,cAIA,IAAAH,EAAA,CACAC,GAAA,EACA,SAIA,GAFAA,GAAA,EAEA,YAAA/L,EAEA,YADAhB,KAAAwK,OAAAxK,KAAAwK,OAAAe,IAAAvL,KAAAqG,UAAAhE,KAIA,IAAArB,EAEA,OADAhB,KAAAmN,aAAA9K,EAAAuK,IACA,EAEA,MAAAb,GAQA,MAPA/L,KAAAK,SAAAkB,OAAAO,OAAAC,SACAA,QAAAqL,IAAA,4CAAA/K,EAAAgL,GAAA,gBAAAT,EAAAxJ,OAAA,YAAA2I,GAEAA,aAAAuB,YACAvB,EAAApB,SAAA,+CAAAtI,EAAAgL,GAAA,gBAAAT,EAAAxJ,OAAA,aAGA2I,GAGA,IAAAgB,EAMA,OAHA/M,KAAAoL,aAAA7I,IACAvC,KAAA4K,YAAAN,KAAAjI,IAEA,GAMAkL,kBAAA,SAAAlL,EAAAe,GACA,OAAAzD,EAAA0C,GAAAlC,KAAA,MAAAiD,EAAAoK,OAAA,GAAAC,cACArK,EAAAsK,UAAA,GAAAC,gBAAAhO,EAAA0C,GAAAlC,KAAA,QAIAyN,cAAA,SAAAzM,EAAAiC,GACA,IAAAyK,EAAA7N,KAAAK,SAAA4C,SAAA9B,GACA,OAAA0M,IAAAA,EAAA7I,cAAA8I,OAAAD,EAAAA,EAAAzK,KAIA2K,YAAA,WACA,IAAA,IAAA5I,EAAA,EAAAA,EAAAR,UAAA1E,OAAAkF,IACA,QAAAtE,IAAA8D,UAAAQ,GACA,OAAAR,UAAAQ,IAeA6I,eAAA,SAAA3L,EAAAuK,GACA,iBAAAA,IACAA,EAAA,CAAAxJ,OAAAwJ,IAGA,IAAAjC,EAAA3K,KAAA+N,YACA/N,KAAA4N,cAAAvL,EAAAlB,KAAAyL,EAAAxJ,QACApD,KAAAuN,kBAAAlL,EAAAuK,EAAAxJ,SAGApD,KAAAK,SAAA0F,aAAA1D,EAAA4L,YAAApN,EACAlB,EAAAO,UAAA+C,SAAA2J,EAAAxJ,QACA,2CAAAf,EAAAlB,KAAA,aAEA+M,EAAA,gBAOA,MANA,mBAAAvD,EACAA,EAAAA,EAAAnJ,KAAAxB,KAAA4M,EAAAK,WAAA5K,GACA6L,EAAAC,KAAAxD,KACAA,EAAAhL,EAAAO,UAAAsE,OAAAmG,EAAA9G,QAAAqK,EAAA,QAAAtB,EAAAK,aAGAtC,GAGAwC,aAAA,SAAA9K,EAAAuK,GACA,IAAAjC,EAAA3K,KAAAgO,eAAA3L,EAAAuK,GAEA5M,KAAAkC,UAAAoI,KAAA,CACAK,QAAAA,EACAtI,QAAAA,EACAe,OAAAwJ,EAAAxJ,SAGApD,KAAAsJ,SAAAjH,EAAAlB,MAAAwJ,EACA3K,KAAAwG,UAAAnE,EAAAlB,MAAAwJ,GAGAc,WAAA,SAAA2C,GAIA,OAHApO,KAAAK,SAAAgO,UACAD,EAAAA,EAAAzF,IAAAyF,EAAAE,OAAAtO,KAAAK,SAAAgO,WAEAD,GAGAtD,kBAAA,WAEA,IADA,IAAApB,EAAAwC,EACA/G,EAAA,EAAAnF,KAAAkC,UAAAiD,GAAAA,IACA+G,EAAAlM,KAAAkC,UAAAiD,GACAnF,KAAAK,SAAA6G,WACAlH,KAAAK,SAAA6G,UAAA1F,KAAAxB,KAAAkM,EAAA7J,QAAArC,KAAAK,SAAAkF,WAAAvF,KAAAK,SAAAoF,YAEAzF,KAAAuO,UAAArC,EAAA7J,QAAA6J,EAAAvB,SAKA,GAHA3K,KAAAkC,UAAAjC,SACAD,KAAAqM,OAAArM,KAAAqM,OAAA1D,IAAA3I,KAAA0I,aAEA1I,KAAAK,SAAAmO,QACA,IAAArJ,EAAA,EAAAnF,KAAA4K,YAAAzF,GAAAA,IACAnF,KAAAuO,UAAAvO,KAAA4K,YAAAzF,IAGA,GAAAnF,KAAAK,SAAA8F,YACA,IAAAhB,EAAA,EAAAuE,EAAA1J,KAAAyO,gBAAA/E,EAAAvE,GAAAA,IACAnF,KAAAK,SAAA8F,YAAA3E,KAAAxB,KAAA0J,EAAAvE,GAAAnF,KAAAK,SAAAkF,WAAAvF,KAAAK,SAAAoF,YAGAzF,KAAAwK,OAAAxK,KAAAwK,OAAAe,IAAAvL,KAAAqM,QACArM,KAAAgL,aACAhL,KAAAyL,WAAAzL,KAAAqM,QAAAqC,QAGAD,cAAA,WACA,OAAAzO,KAAA2J,gBAAA4B,IAAAvL,KAAA2O,oBAGAA,gBAAA,WACA,OAAAhP,EAAAK,KAAAkC,WAAAwI,IAAA,WACA,OAAA1K,KAAAqC,WAIAkM,UAAA,SAAAlM,EAAAsI,GACA,IAAAiE,EAAA9E,EAAA+E,EAAA1E,EACA+B,EAAAlM,KAAAqG,UAAAhE,GACAyM,EAAA9O,KAAA+O,SAAA1M,GACA2M,EAAArP,EAAA0C,GAAAjC,KAAA,oBAEA8L,EAAAjM,QAGAiM,EAAA5E,YAAAtH,KAAAK,SAAAoF,YAAA4B,SAAArH,KAAAK,SAAAkF,YAGAvF,KAAAK,UAAAL,KAAAK,SAAA4O,WACA/C,EAAAV,KAAAb,GAAA,IAEAuB,EAAAgD,KAAAvE,GAAA,MAKAuB,EAAAvM,EAAA,IAAAK,KAAAK,SAAAqF,aAAA,KACAtF,KAAA,KAAA0O,EAAA,UACAzH,SAAArH,KAAAK,SAAAkF,YAEAvF,KAAAK,UAAAL,KAAAK,SAAA4O,WACA/C,EAAAV,KAAAb,GAAA,IAEAuB,EAAAgD,KAAAvE,GAAA,IAIAiE,EAAA1C,EACAlM,KAAAK,SAAAgO,UAIAO,EAAA1C,EAAAR,OAAAgD,OAAAS,KAAA,IAAAnP,KAAAK,SAAAgO,QAAA,MAAAC,UAEAtO,KAAAwI,eAAAvI,OACAD,KAAAwI,eAAA4G,OAAAR,GACA5O,KAAAK,SAAAgP,eACArP,KAAAK,SAAAgP,eAAA7N,KAAAxB,KAAA4O,EAAAjP,EAAA0C,IAEAuM,EAAAU,YAAAjN,GAIA6J,EAAA/J,GAAA,SAGA+J,EAAA9L,KAAA,MAAA0O,GAIA,IAAA5C,EAAAqD,QAAA,cAAAvP,KAAAwP,cAAAV,GAAA,MAAA7O,SACA4O,EAAA3C,EAAA9L,KAAA,MAGA4O,EAEAA,EAAAS,MAAA,IAAApK,OAAA,MAAArF,KAAAwP,cAAAX,GAAA,UAGAG,GAAA,IAAAH,GAJAG,EAAAH,EAMAlP,EAAA0C,GAAAjC,KAAA,mBAAA4O,IAGAlF,EAAA9J,KAAAsF,OAAAjD,EAAAlB,SAEAgJ,EAAAnK,KACAL,EAAAyC,KAAA+H,EAAA7E,OAAA,SAAAnE,EAAAkJ,GACAA,IAAAP,GACAnK,EAAA,UAAAwK,EAAAqF,cAAArO,GAAA,KAAAgJ,EAAA7I,aACAlB,KAAA,mBAAA8L,EAAA9L,KAAA,aAMAuK,GAAA3K,KAAAK,SAAAmO,UACAtC,EAAAV,KAAA,IACA,iBAAAxL,KAAAK,SAAAmO,QACAtC,EAAA7E,SAAArH,KAAAK,SAAAmO,SAEAxO,KAAAK,SAAAmO,QAAAtC,EAAA7J,IAGArC,KAAAqM,OAAArM,KAAAqM,OAAA1D,IAAAuD,IAGA7F,UAAA,SAAAhE,GACA,IAAAlB,EAAAnB,KAAAwP,cAAAxP,KAAA+O,SAAA1M,IACAqN,EAAA/P,EAAA0C,GAAAjC,KAAA,oBACA+L,EAAA,cAAAhL,EAAA,kBAAAA,EAAA,OAQA,OALAuO,IACAvD,EAAAA,EAAA,MAAAnM,KAAAwP,cAAAE,GACA7L,QAAA,OAAA,QAGA7D,KACAyK,SACAoB,OAAAM,IAMAqD,cAAA,SAAAG,GACA,YAAA9O,IAAA8O,EACA,GAGAA,EAAA9L,QAAA,yCAAA,SAGAkL,SAAA,SAAA1M,GACA,OAAArC,KAAAsF,OAAAjD,EAAAlB,QAAAnB,KAAAuG,UAAAlE,IAAAA,EAAAgL,IAAAhL,EAAAlB,MAGA+I,oBAAA,SAAA7H,GAQA,OALArC,KAAAuG,UAAAlE,KACAA,EAAArC,KAAAoH,WAAA/E,EAAAlB,OAIAxB,EAAA0C,GAAAkJ,IAAAvL,KAAAK,SAAAyF,QAAA,IAGAS,UAAA,SAAAlE,GACA,MAAA,kBAAA8L,KAAA9L,EAAA8E,OAGAC,WAAA,SAAAjG,GACA,OAAAxB,EAAAK,KAAAsB,aAAA2K,KAAA,UAAAjM,KAAAwP,cAAArO,GAAA,OAGAyO,UAAA,SAAA1G,EAAA7G,GACA,OAAAA,EAAAwN,SAAAlC,eACA,IAAA,SACA,OAAAhO,EAAA,kBAAA0C,GAAApC,OACA,IAAA,QACA,GAAAD,KAAAuG,UAAAlE,GACA,OAAArC,KAAAoH,WAAA/E,EAAAlB,MAAA0K,OAAA,YAAA5L,OAGA,OAAAiJ,EAAAjJ,QAGA6P,OAAA,SAAAlN,EAAAP,GACA,OAAArC,KAAA+P,mBAAAnN,IAAA5C,KAAA+P,mBAAAnN,GAAAA,EAAAP,IAGA0N,YAAA,CACAC,QAAA,SAAApN,GACA,OAAAA,GAEA+M,OAAA,SAAA/M,EAAAP,GACA,QAAA1C,EAAAiD,EAAAP,EAAAV,MAAA1B,QAEAgQ,SAAA,SAAArN,EAAAP,GACA,OAAAO,EAAAP,KAIAoE,SAAA,SAAApE,GACA,IAAAjB,EAAApB,KAAA4G,aAAAvE,GACA,OAAA1C,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAoB,EAAAiB,IAAA,uBAGA6N,gBAAA,SAAA7N,GACA,MAAA,WAAAA,EAAAlB,MAGAgP,aAAA,SAAA9N,GACArC,KAAA6I,QAAAxG,EAAAlB,QACAnB,KAAA4B,iBACAjC,EAAA0C,GAAAgF,SAAArH,KAAAK,SAAAmF,cACAxF,KAAA6I,QAAAxG,EAAAlB,OAAA,IAIAiP,YAAA,SAAA/N,EAAAJ,GACAjC,KAAA4B,iBAGA5B,KAAA4B,eAAA,IACA5B,KAAA4B,eAAA,UAEA5B,KAAA6I,QAAAxG,EAAAlB,MACAxB,EAAA0C,GAAAiF,YAAAtH,KAAAK,SAAAmF,cACAvD,GAAA,IAAAjC,KAAA4B,gBAAA5B,KAAAkB,eAAAlB,KAAA2B,QAAA,IAAA3B,KAAA4B,gBACAjC,EAAAK,KAAAsB,aAAAwK,QAAA,UAMA9L,KAAAS,cACAd,EAAA,sBAAAK,KAAAS,aAAAU,KAAA,KAAAnB,KAAAsB,aAAAG,SAGAzB,KAAAkB,eAAA,IACAe,GAAA,IAAAjC,KAAA4B,gBAAA5B,KAAAkB,gBACAvB,EAAAK,KAAAsB,aAAAiI,eAAA,eAAA,CAAAvJ,OACAA,KAAAkB,eAAA,IAIA8L,aAAA,SAAA3K,GACA,IAAAgO,EAEArQ,KAAA6I,QAAAxG,EAAAlB,QACAkP,EAAArQ,KAAAkQ,gBAAA7N,GACA1C,EAAA2Q,UAAAD,GAEArQ,KAAA4B,iBAGA5B,KAAA4B,eAAA,IACA5B,KAAA4B,eAAA,UAGA5B,KAAA6I,QAAAxG,EAAAlB,MACAxB,EAAA0C,GAAAiF,YAAAtH,KAAAK,SAAAmF,gBAIA+K,cAAA,SAAAlO,EAAAe,GAGA,OAFAA,EAAA,iBAAAA,GAAAA,GAAA,SAEAzD,EAAAQ,KAAAkC,EAAA,kBAAA1C,EAAAQ,KAAAkC,EAAA,gBAAA,CACAmO,IAAA,KACAvO,OAAA,EACA0I,QAAA3K,KAAAgO,eAAA3L,EAAA,CAAAe,OAAAA,OAKAqN,QAAA,WACAzQ,KAAA+K,YAEApL,EAAAK,KAAAsB,aACAoP,IAAA,aACAzF,WAAA,aACAgB,KAAA,0BACAyE,IAAA,qBACApJ,YAAA,yBACA2E,KAAA,2BACAyE,IAAA,sBACApJ,YAAA,0BACA2E,KAAA,gCACAyE,IAAA,2BACApJ,YAAA,+BACA2E,KAAA,mCACAyE,IAAA,8BACApJ,YAAA,kCACA2E,KAAA,8BACAyE,IAAA,yBACApJ,YAAA,+BAKAqJ,kBAAA,CACAlN,SAAA,CAAAA,UAAA,GACA+D,MAAA,CAAAA,OAAA,GACAC,IAAA,CAAAA,KAAA,GACAC,KAAA,CAAAA,MAAA,GACAC,QAAA,CAAAA,SAAA,GACAC,OAAA,CAAAA,QAAA,GACAC,OAAA,CAAAA,QAAA,GACA+I,WAAA,CAAAA,YAAA,IAGAC,cAAA,SAAAC,EAAAvO,GACAuO,EAAA9L,cAAA8I,OACA9N,KAAA2Q,kBAAAG,GAAAvO,EAEA5C,EAAAC,OAAAI,KAAA2Q,kBAAAG,IAIAxN,WAAA,SAAAjB,GACA,IAAAE,EAAA,GACAwO,EAAApR,EAAA0C,GAAAjC,KAAA,SASA,OAPA2Q,GACApR,EAAAyC,KAAA2O,EAAA7N,MAAA,KAAA,WACAlD,QAAAL,EAAAO,UAAAyQ,mBACAhR,EAAAC,OAAA2C,EAAA5C,EAAAO,UAAAyQ,kBAAA3Q,SAIAuC,GAGAyO,uBAAA,SAAAzO,EAAA4E,EAAA/D,EAAA8F,GAIA,eAAAiF,KAAA/K,KAAA,OAAA+D,GAAA,oBAAAgH,KAAAhH,MACA+B,EAAA+H,OAAA/H,GAGAgI,MAAAhI,KACAA,OAAArI,IAIAqI,GAAA,IAAAA,EACA3G,EAAAa,GAAA8F,EACA/B,IAAA/D,GAAA,UAAA+D,IAIA5E,EAAA,SAAA4E,EAAA,UAAA/D,IAAA,IAIAG,eAAA,SAAAlB,GACA,IAGAe,EAAA8F,EAHA3G,EAAA,GACAgK,EAAA5M,EAAA0C,GACA8E,EAAA9E,EAAA8O,aAAA,QAGA,IAAA/N,KAAAzD,EAAAO,UAAAgN,QAaAhE,EAVA,aAAA9F,GAKA,MAJA8F,EAAA7G,EAAA8O,aAAA/N,MAKA8F,GAAA,KAIAA,GAEAqD,EAAAnM,KAAAgD,GAGApD,KAAAgR,uBAAAzO,EAAA4E,EAAA/D,EAAA8F,GAQA,OAJA3G,EAAAwF,WAAA,uBAAAoG,KAAA5L,EAAAwF,mBACAxF,EAAAwF,UAGAxF,GAGAiB,UAAA,SAAAnB,GACA,IAGAe,EAAA8F,EAHA3G,EAAA,GACAgK,EAAA5M,EAAA0C,GACA8E,EAAA9E,EAAA8O,aAAA,QAGA,IAAA/N,KAAAzD,EAAAO,UAAAgN,QAIA,MAHAhE,EAAAqD,EAAApM,KAAA,OAAAiD,EAAAoK,OAAA,GAAAC,cAAArK,EAAAsK,UAAA,GAAAC,kBAIAzE,GAAA,GAGAlJ,KAAAgR,uBAAAzO,EAAA4E,EAAA/D,EAAA8F,GAEA,OAAA3G,GAGAG,YAAA,SAAAL,GACA,IAAAE,EAAA,GACArC,EAAAP,EAAAQ,KAAAkC,EAAAV,KAAA,aAKA,OAHAzB,EAAAG,SAAAkC,QACAA,EAAA5C,EAAAO,UAAA8C,cAAA9C,EAAAG,SAAAkC,MAAAF,EAAAlB,QAAA,IAEAoB,GAGAc,eAAA,SAAAd,EAAAF,GAmEA,OAhEA1C,EAAAyC,KAAAG,EAAA,SAAA8B,EAAAjD,GAGA,IAAA,IAAAA,GAIA,GAAAA,EAAAwB,OAAAxB,EAAAgQ,QAAA,CACA,IAAAC,GAAA,EACA,cAAAjQ,EAAAgQ,SACA,IAAA,SACAC,IAAA1R,EAAAyB,EAAAgQ,QAAA/O,EAAAV,MAAA1B,OACA,MACA,IAAA,WACAoR,EAAAjQ,EAAAgQ,QAAA5P,KAAAa,EAAAA,GAGAgP,EACA9O,EAAA8B,QAAAxD,IAAAO,EAAAwB,OAAAxB,EAAAwB,OAEAjD,EAAAQ,KAAAkC,EAAAV,KAAA,aAAAwJ,cAAAxL,EAAA0C,WACAE,EAAA8B,iBAjBA9B,EAAA8B,KAuBA1E,EAAAyC,KAAAG,EAAA,SAAAqK,EAAA0E,GACA/O,EAAAqK,GAAA,mBAAA0E,GAAA,eAAA1E,EAAA0E,EAAAjP,GAAAiP,IAIA3R,EAAAyC,KAAA,CAAA,YAAA,aAAA,WACAG,EAAAvC,QACAuC,EAAAvC,MAAAiR,OAAA1O,EAAAvC,UAGAL,EAAAyC,KAAA,CAAA,cAAA,SAAA,WACA,IAAAmP,EACAhP,EAAAvC,QACAiF,MAAAuM,QAAAjP,EAAAvC,OACAuC,EAAAvC,MAAA,CAAAiR,OAAA1O,EAAAvC,MAAA,IAAAiR,OAAA1O,EAAAvC,MAAA,KACA,iBAAAuC,EAAAvC,QACAuR,EAAAhP,EAAAvC,MAAA6D,QAAA,UAAA,IAAAX,MAAA,UACAX,EAAAvC,MAAA,CAAAiR,OAAAM,EAAA,IAAAN,OAAAM,EAAA,SAKA5R,EAAAO,UAAAoI,mBAGA,MAAA/F,EAAA6F,KAAA,MAAA7F,EAAA4F,MACA5F,EAAA2F,MAAA,CAAA3F,EAAA6F,IAAA7F,EAAA4F,YACA5F,EAAA6F,WACA7F,EAAA4F,KAEA,MAAA5F,EAAAyF,WAAA,MAAAzF,EAAAwF,YACAxF,EAAA0F,YAAA,CAAA1F,EAAAyF,UAAAzF,EAAAwF,kBACAxF,EAAAyF,iBACAzF,EAAAwF,YAIAxF,GAIAS,cAAA,SAAA7C,GACA,IACAsR,EAMA,MAPA,iBAAAtR,IACAsR,EAAA,GACA9R,EAAAyC,KAAAjC,EAAA+C,MAAA,MAAA,WACAuO,EAAAzR,OAAA,IAEAG,EAAAsR,GAEAtR,GAIAuR,UAAA,SAAAvQ,EAAAiC,EAAAuH,GACAhL,EAAAO,UAAAgN,QAAA/L,GAAAiC,EACAzD,EAAAO,UAAA+C,SAAA9B,QAAAN,IAAA8J,EAAAA,EAAAhL,EAAAO,UAAA+C,SAAA9B,GACAiC,EAAAnD,OAAA,GACAN,EAAAO,UAAA2Q,cAAA1P,EAAAxB,EAAAO,UAAA8C,cAAA7B,KAKA+L,QAAA,CAGAzJ,SAAA,SAAAyF,EAAA7G,EAAAO,GAGA,IAAA5C,KAAA8P,OAAAlN,EAAAP,GACA,MAAA,sBAEA,GAAA,WAAAA,EAAAwN,SAAAlC,cAMA,OAAA3N,KAAAuG,UAAAlE,GACA,EAAArC,KAAA4P,UAAA1G,EAAA7G,GAEA6G,MAAAA,GAAA,EAAAA,EAAAjJ,OANA,IAAAmB,EAAAzB,EAAA0C,GAAAjB,MACA,OAAAA,GAAA,EAAAA,EAAAnB,QASAuH,MAAA,SAAA0B,EAAA7G,GAMA,OAAArC,KAAAyG,SAAApE,IAAA,wIAAA8L,KAAAjF,IAIAzB,IAAA,SAAAyB,EAAA7G,GAMA,OAAArC,KAAAyG,SAAApE,IAAA,khBAAA8L,KAAAjF,IAIAxB,MACA5D,GAAA,EAEA,SAAAoF,EAAA7G,GAcA,OAbAyB,IACAA,GAAA,EACA9D,KAAAK,SAAAkB,OAAAO,OAAAC,SACAA,QAAAC,KACA,uTASAhC,KAAAyG,SAAApE,KAAA,cAAA8L,KAAA,IAAAwD,KAAAzI,GAAA0I,cAKAjK,QAAA,SAAAuB,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAA,+DAAA8L,KAAAjF,IAIAtB,OAAA,SAAAsB,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAA,gDAAA8L,KAAAjF,IAIArB,OAAA,SAAAqB,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAA,QAAA8L,KAAAjF,IAIAlB,UAAA,SAAAkB,EAAA7G,EAAAO,GACA,IAAA3C,EAAAgF,MAAAuM,QAAAtI,GAAAA,EAAAjJ,OAAAD,KAAA4P,UAAA1G,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAAO,GAAA3C,GAIA8H,UAAA,SAAAmB,EAAA7G,EAAAO,GACA,IAAA3C,EAAAgF,MAAAuM,QAAAtI,GAAAA,EAAAjJ,OAAAD,KAAA4P,UAAA1G,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAApC,GAAA2C,GAIAqF,YAAA,SAAAiB,EAAA7G,EAAAO,GACA,IAAA3C,EAAAgF,MAAAuM,QAAAtI,GAAAA,EAAAjJ,OAAAD,KAAA4P,UAAA1G,EAAA7G,GACA,OAAArC,KAAAyG,SAAApE,IAAApC,GAAA2C,EAAA,IAAA3C,GAAA2C,EAAA,IAIAwF,IAAA,SAAAc,EAAA7G,EAAAO,GACA,OAAA5C,KAAAyG,SAAApE,IAAAO,GAAAsG,GAIAf,IAAA,SAAAe,EAAA7G,EAAAO,GACA,OAAA5C,KAAAyG,SAAApE,IAAA6G,GAAAtG,GAIAsF,MAAA,SAAAgB,EAAA7G,EAAAO,GACA,OAAA5C,KAAAyG,SAAApE,IAAA6G,GAAAtG,EAAA,IAAAsG,GAAAtG,EAAA,IAIAyF,KAAA,SAAAa,EAAA7G,EAAAO,GAMA,SAAAiP,EAAAC,GACA,IAAArC,GAAA,GAAAqC,GAAArC,MAAA,iBACA,OAAAA,GAKAA,EAAA,GAAAA,EAAA,GAAAxP,OAJA,EAMA,SAAA8R,EAAAD,GACA,OAAAE,KAAAC,MAAAH,EAAAE,KAAAE,IAAA,GAAAC,IAfA,IAkBAA,EAlBAhL,EAAAxH,EAAA0C,GAAAjC,KAAA,QACAgS,EAAA,gCAAAjL,EAAA,qBAEAkL,EAAA,IAAAhN,OAAA,MAAA8B,EAAA,OAcAlF,GAAA,EAKA,GAlBAkF,IAAAkL,EAAAlE,KAFA,CAAA,OAAA,SAAA,SAEAhF,QAmBA,MAAA,IAAAmJ,MAAAF,GAUA,OAPAD,EAAAN,EAAAjP,IAGAiP,EAAA3I,GAAAiJ,GAAAJ,EAAA7I,GAAA6I,EAAAnP,IAAA,KACAX,GAAA,GAGAjC,KAAAyG,SAAApE,IAAAJ,GAIA6F,QAAA,SAAAoB,EAAA7G,EAAAO,GAGA,IAAA2P,EAAA5S,EAAAiD,GAMA,OALA5C,KAAAK,SAAAiG,YAAAiM,EAAAhH,IAAA,0BAAAtL,QACAsS,EAAAlL,SAAA,yBAAA9G,GAAA,wBAAA,WACAZ,EAAA0C,GAAAJ,UAGAiH,IAAAqJ,EAAAnR,OAIAsC,OAAA,SAAAwF,EAAA7G,EAAAO,EAAAQ,GACA,GAAApD,KAAAyG,SAAApE,GACA,MAAA,sBAGAe,EAAA,iBAAAA,GAAAA,GAAA,SAEA,IACAlD,EAAAC,EAAAqS,EADAC,EAAAzS,KAAAuQ,cAAAlO,EAAAe,GAWA,OARApD,KAAAK,SAAA4C,SAAAZ,EAAAlB,QACAnB,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAA,IAEAsR,EAAAC,gBAAAD,EAAAC,iBAAA1S,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAAiC,GACApD,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAAiC,GAAAqP,EAAA9H,QAEA/H,EAAA,iBAAAA,EAAA,CAAA6E,IAAA7E,GAAAA,EACA4P,EAAA7S,EAAAiD,MAAAjD,EAAAC,OAAA,CAAAO,KAAA+I,GAAAtG,EAAAzC,OACA,OAAAsS,EAAAxQ,OAAAwQ,EAAAjC,MAAAgC,EACAC,EAAAxQ,OAGAwQ,EAAAjC,IAAAgC,EACAC,EAAAxQ,MAAA,MACA/B,EAAAF,MACAmQ,aAAA9N,IACAlC,EAAA,IACAkC,EAAAlB,MAAA+H,EACAvJ,EAAAgT,KAAAhT,EAAAC,QAAA,EAAA,CACAgT,KAAA,QACAvC,KAAArQ,KAAAkQ,gBAAA7N,GACAwQ,SAAA,OACA1S,KAAAA,EACA2S,QAAA5S,EAAAoB,YACAkN,QAAA,SAAAuE,GACA,IACAtI,EAAAE,EAAAnE,EADAvE,GAAA,IAAA8Q,GAAA,SAAAA,EAGA7S,EAAAG,SAAA4C,SAAAZ,EAAAlB,MAAAiC,GAAAqP,EAAAC,gBACAzQ,GACAuE,EAAAtG,EAAAgB,cACAhB,EAAAsK,OAAAtK,EAAAmG,UAAAhE,GACAnC,EAAAgB,cAAAsF,EACAtG,EAAA0K,YAAAN,KAAAjI,GACAnC,EAAA6G,QAAA1E,EAAAlB,OAAA,EACAjB,EAAAsJ,eAEAiB,EAAA,GACAE,EAAAoI,GAAA7S,EAAA8N,eAAA3L,EAAA,CAAAe,OAAAA,EAAA6J,WAAA/D,IACAuB,EAAApI,EAAAlB,MAAAsR,EAAA9H,QAAAA,EACAzK,EAAA6G,QAAA1E,EAAAlB,OAAA,EACAjB,EAAAsJ,WAAAiB,IAEAgI,EAAAxQ,MAAAA,EACA/B,EAAAkQ,YAAA/N,EAAAJ,KAEAW,IACA,eAWA,IACA+P,EADAK,EAAA,GAmCA,OA/BArT,EAAAsT,cACAtT,EAAAsT,cAAA,SAAA5S,EAAA6S,EAAAC,GACA,IAAA9C,EAAAhQ,EAAAgQ,KACA,UAAAhQ,EAAAuS,OACAjT,EAAA2Q,UAAAD,GACA2C,EAAA3C,GAAA8C,MAMAR,EAAAhT,EAAAgT,KACAhT,EAAAgT,KAAA,SAAAtS,GACA,IAAAuS,GAAA,SAAAvS,EAAAA,EAAAV,EAAAyT,cAAAR,KACAvC,GAAA,SAAAhQ,EAAAA,EAAAV,EAAAyT,cAAA/C,KACA,MAAA,UAAAuC,GACAjT,EAAA2Q,UAAAD,GACA2C,EAAA3C,GAAAsC,EAAA5N,MAAA/E,KAAA2E,WACAqO,EAAA3C,IAEAsC,EAAA5N,MAAA/E,KAAA2E,aAKAhF,EAAA2Q,UAAA,SAAAD,GACA2C,EAAA3C,KACA2C,EAAA3C,GAAAgD,eACAL,EAAA3C,KAGA1Q,ICxpDA,SAAA2T,EAAAlU,GAEA,mBAAAC,QAAAA,OAAAC,IAEAD,OAAA,GAAAD,GAGA,iBAAAG,QAAAA,OAAAC,QAEAD,OAAAC,QAAAJ,IAEAkU,EAAAC,cAAAnU,IAXA,CAcA,oBAAAoU,KAAAA,KAAAxT,KAAA,WACA,IAIAyT,EAAA,CACAC,IAAA,MACAC,KAAA,KACArP,SAAA,CACAsP,aAAA,CACAC,KAAA,CAAA,SAAA,SAAA,UAAA,YAAA,WAAA,SAAA,YACAC,UAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,OACAC,OAAA,CACA,UAAA,WAAA,QAAA,QAAA,MAAA,OAAA,OACA,SAAA,YAAA,UAAA,WAAA,YAEAC,YAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,MAAA,OACAC,SAAA,CAAA,KAAA,MACAC,QAAA,SAAAtM,GACA,IAAAxC,EAAAwC,EAAA,GAAAuM,EAAA,CAAAC,EAAA,KAAAC,EAAA,KAAAC,EAAA,MACA,OAAA,IAAAtC,KAAAuC,MAAA3M,EAAA,IAAA,KAAAuM,EAAA/O,GAAA+O,EAAA/O,GAAA,OAGAoP,WAAA,eACAC,WAAA,2CACAC,SAAA,qBACAC,QAAA,uIACAC,OAAA,eAEAC,OAAA,SAAAjR,EAAAkR,GACA,OAAAC,SAAAnR,EAAAkR,GAAA,KAEAE,QAAA,SAAAC,EAAAC,GACA,MAAA,iBAAA,GAAA,iBAAA,GAAAD,EAAAtH,gBAAAuH,EAAAvH,eAEAwH,KAAA,SAAAjM,EAAAjJ,EAAAmV,GACA,IAAAhU,EAAA8H,EAAA0I,WAEA,OADAwD,EAAAA,GAAA,IACAhU,EAAAnB,OAAAA,EAAAwT,EAAA0B,KAAAC,EAAAhU,EAAAnB,GAAAmB,GAEAiU,MAAA,SAAAC,GACA,IAAAnQ,EAAAkG,EAEA,IADAiK,EAAAA,GAAA,GACAnQ,EAAA,EAAAA,EAAAR,UAAA1E,OAAAkF,IAEA,GADAkG,EAAA1G,UAAAQ,GAIA,IAAA,IAAA8D,KAAAoC,EACAA,EAAAkK,eAAAtM,KACA,iBAAAoC,EAAApC,GACAwK,EAAA4B,MAAAC,EAAArM,GAAAoC,EAAApC,IAEAqM,EAAArM,GAAAoC,EAAApC,IAKA,OAAAqM,GAEAE,SAAA,SAAApU,EAAAqU,GACA,IAAA,IAAAtQ,EAAA,EAAAA,EAAAsQ,EAAAxV,OAAAkF,IACA,GAAAsQ,EAAAtQ,GAAAwI,gBAAAvM,EAAAuM,cACA,OAAAxI,EAGA,OAAA,IASAoO,EAAA,SAAAxT,GACA,IAAAyT,EAAAxT,KAAA0V,EAAAjC,EAAA4B,MAAA5B,EAAAnP,SAAAvE,GACAyT,EAAAI,aAAA8B,EAAA9B,aACAJ,EAAAgB,WAAAkB,EAAAlB,WACAhB,EAAAiB,WAAAiB,EAAAjB,WACAjB,EAAAkB,SAAAgB,EAAAhB,SACAlB,EAAAmB,QAAAe,EAAAf,QACAnB,EAAAoB,OAAAc,EAAAd,QA+hBA,OAzhBArB,EAAAhL,UAAA,CACAvD,YAAAuO,EACAoC,SAAA,SAAAvU,GACA,IACA+D,EAAAsO,EAAA+B,SAAApU,EADApB,KACA4T,aAAAI,aAAA,EAIA,OAHA,IAAA7O,IACAA,EAAAsO,EAAA+B,SAAApU,EAHApB,KAGA4T,aAAAG,QAAA,GAEA5O,GAEAyQ,UAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,EAAA7Q,EAAA8Q,EAAAC,EACAC,EAAAC,EAAAC,EAAAC,EAAAC,EADAC,GAAA,EAAAC,GAAA,EACAC,EADA1W,KACA4T,aACA0B,EAAA,CAAA5N,KAAA,KAAAiP,KAAA,KAAAC,MAAA,KAAAC,IAAA,KAAAC,KAAA,EAAA1O,IAAA,EAAA2O,IAAA,GACA,IAAAlB,EACA,OAAA,KAEA,GAAAA,aAAAlE,KACA,OAAAkE,EAEA,GAAA,MAAAC,EAEA,OADA3Q,EAAAsO,EAAAoB,OAAAgB,IACA,IAAAlE,KAAA,IAAAxM,GAAA0Q,EAEA,cAAAA,GACA,IAAA,SACA,OAAA,IAAAlE,KAAAkE,GACA,IAAA,SACA,MACA,QACA,OAAA,KAGA,KADAE,EAAAD,EAAArG,MArBAzP,KAqBAyU,cACA,IAAAsB,EAAA9V,OACA,MAAA,IAAAqS,MAAA,mCAEA,IAAAnN,EAAA4Q,EAAA9V,OAAA,EAAA,GAAAkF,EAAAA,IACA,MAAA4Q,EAAA5Q,IACA4Q,EAAAiB,OAAA7R,EAAA,GAIA,IADA6Q,EAAAH,EAAAhS,QA9BA7D,KA8BAwU,WAAA,MAAAtR,MAAA,MACAiC,EAAA,EAAAA,EAAA6Q,EAAA/V,OAAAkF,IAGA,OAFA8Q,EAAAD,EAAA7Q,GACA+Q,EAAAzC,EAAAoB,OAAAoB,GACAF,EAAA5Q,IACA,IAAA,IACA,IAAA,IACA,IAAA+Q,EAIA,OAAA,KAHAI,EAAAL,EAAAhW,OACAqV,EAAAqB,KAAA,IAAAL,EAAA7C,EAAAoB,QAAAqB,EAAA,GAAA,KAAA,MAAAD,GAAAC,EAIAM,GAAA,EACA,MACA,IAAA,IACA,IAAA,IACA,IAAA,IACA,IAAA,IACA,GAAAtF,MAAAgF,GAAA,CAEA,KAAA,GADAC,EAlDAnW,KAkDA2V,SAAAM,KAIA,OAAA,KAFAX,EAAAsB,MAAAT,MAIA,CACA,KAAA,GAAAD,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAsB,MAAAV,EAKAM,GAAA,EACA,MACA,IAAA,IACA,IAAA,IACA,KAAA,GAAAN,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAuB,IAAAX,EAIAM,GAAA,EACA,MACA,IAAA,IACA,IAAA,IAIA,GADAD,EAAAP,EAFAI,GAAA,EAAAL,EAAAkB,QAAA,KAAAlB,EAAAkB,QAAA,MACA,EAAAlB,EAAAkB,QAAA,KAAAlB,EAAAkB,QAAA,MAAA,IAEA,IAAAb,EACAC,EAAA5C,EAAAuB,QAAAuB,EAAAG,EAAAzC,SAAA,IAAA,EACAR,EAAAuB,QAAAuB,EAAAG,EAAAzC,SAAA,IAAA,IAAA,EACA,GAAAiC,GAAAA,GAAA,KAAA,GAAAG,EACAf,EAAAwB,KAAAZ,EAAA,IAAA,EAAAG,EAAAH,EAAAG,EAEA,GAAAH,GAAAA,GAAA,KACAZ,EAAAwB,KAAAZ,OAGA,CACA,KAAA,GAAAA,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAwB,KAAAZ,EAKAO,GAAA,EACA,MACA,IAAA,IACA,IAAA,IACA,KAAA,GAAAP,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAwB,KAAAZ,EAIAO,GAAA,EACA,MACA,IAAA,IACA,KAAA,GAAAP,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAlN,IAAA8N,EAIAO,GAAA,EACA,MACA,IAAA,IACA,KAAA,GAAAP,GAAAA,GAAA,IAGA,OAAA,KAFAZ,EAAAyB,IAAAb,EAIAO,GAAA,EAIA,IAAA,IAAAD,EAAA,CACA,IAAAU,EAAA5B,EAAAqB,MAAA,EAAAQ,EAAA7B,EAAAsB,MAAAtB,EAAAsB,MAAA,EAAA,EAAAQ,EAAA9B,EAAAuB,KAAA,EACAvB,EAAA5N,KAAA,IAAAiK,KAAAuF,EAAAC,EAAAC,EAAA9B,EAAAwB,KAAAxB,EAAAlN,IAAAkN,EAAAyB,IAAA,OACA,CACA,IAAA,IAAAN,EACA,OAAA,KAEAnB,EAAA5N,KAAA,IAAAiK,KAAA,EAAA,EAAA,EAAA2D,EAAAwB,KAAAxB,EAAAlN,IAAAkN,EAAAyB,IAAA,GAEA,OAAAzB,EAAA5N,MAEA2P,UAAA,SAAAC,EAAAxB,GACA,GAAA,iBAAAwB,EACA,OAAAA,EAEA,IAAAhB,EACAiB,EAAApS,EAAAC,EAAAoS,EAAAC,EADAC,EAAAJ,EAAAzT,QAAA7D,KAAAwU,WAAA,MAAAtR,MAAA,MACA6S,EAAAD,EAAArG,MADAzP,KACAyU,YAAAoB,EAAA,IAAAlE,KAAAgG,EAAA,EAEA,IAHA,WAGAxJ,KAAA4H,EAAA,IACA,OAAAuB,EAGA,IAAAnS,EAAA,EAAAA,EAAAuS,EAAAzX,OAAAkF,IAAA,CAIA,GAHAwS,EAAA,EACAH,EAAAE,EAAAvS,GACAsS,EAAAhE,EAAAoB,OAAA2C,EAAA9K,OAAA,EAAA,IACAwE,MAAAuG,GACA,OAAA,KAEA,OAAAtS,GACA,KAAA,EACA,MAAA4Q,EAAA,IAAA,MAAAA,EAAA,GACAF,EAAA+B,SAAAH,EAAA,GAEA5B,EAAAgC,QAAAJ,GAEA,MACA,KAAA,EACA,MAAA1B,EAAA,IAAA,MAAAA,EAAA,GACAF,EAAAgC,QAAAJ,GAEA5B,EAAA+B,SAAAH,EAAA,GAEA,MACA,KAAA,EAKA,GAJAF,EAAA1B,EAAAiC,cAEAH,GADArB,EAAAkB,EAAAvX,QACA,EAAAqW,EAAA,IACAiB,EAAA9D,EAAAoB,OAAAyB,EAAA,EAAAiB,EAAA3F,WAAAlF,OAAA,EAAA,EAAA4J,GAAAkB,EAAAA,EAAA9K,OAAA,EAAA,KAEA,OAAA,KAEAmJ,EAAAkC,YAAAR,GACA,MACA,KAAA,EACA1B,EAAAmC,SAAAP,GACA,MACA,KAAA,EACA5B,EAAAoC,WAAAR,GACA,MACA,KAAA,EACA5B,EAAAqC,WAAAT,GAIA,GADArS,EAAAoS,EAAA9K,OAAAiL,IACA1X,QACAyX,EAAAV,OAAA7R,EAAA,EAAA,EAAAC,GAGA,OAAAyQ,GAEAsC,YAAA,SAAAC,EAAAvC,GACA,SAAAwC,EAAAC,EAAAC,GACA,OAAAC,EAAAF,GAAAE,EAAAF,KAAAC,EADA,IAAA/E,EAAAxT,KAAA0W,EAAAlD,EAAAI,aAAA6E,EAAA,YAGAD,EAAA,CAQAE,EAAA,WACA,OAAAjF,EAAA0B,KAAAqD,EAAAG,IAAA,IAMAC,EAAA,WACA,OAAAlC,EAAA5C,UAAA0E,EAAAK,MAMAF,EAAA,WACA,OAAA9C,EAAAiD,WAMAC,EAAA,WACA,OAAArC,EAAA7C,KAAA2E,EAAAK,MAMAG,EAAA,WACA,OAAAR,EAAAK,KAAA,GAMAA,EAAA,WACA,OAAAhD,EAAAoD,UAMAC,EAAA,WACA,IAAAhV,EAAA,IAAAyN,KAAA6G,EAAAW,IAAAX,EAAApT,IAAA,EAAAoT,EAAAG,KAAAS,EAAA,IAAAzH,KAAA6G,EAAAW,IAAA,EAAA,GACA,OAAAnH,KAAAC,OAAA/N,EAAAkV,GAAA3F,EAAAC,MAUA2F,EAAA,WACA,IAAAnV,EAAA,IAAAyN,KAAA6G,EAAAW,IAAAX,EAAApT,IAAA,EAAAoT,EAAAG,IAAAH,EAAAQ,IAAA,GAAAI,EAAA,IAAAzH,KAAAzN,EAAA4T,cAAA,EAAA,GACA,OAAArE,EAAA0B,KAAA,EAAAnD,KAAAC,OAAA/N,EAAAkV,GAAA3F,EAAAC,IAAA,GAAA,IAUA4F,EAAA,WACA,OAAA5C,EAAA3C,OAAA8B,EAAAF,aAMA9H,EAAA,WACA,OAAA4F,EAAA0B,KAAAqD,EAAApT,IAAA,IAMAmU,EAAA,WACA,OAAA7C,EAAA1C,YAAA6B,EAAAF,aAMAvQ,EAAA,WACA,OAAAyQ,EAAAF,WAAA,GAMA2C,EAAA,WACA,OAAA,IAAA3G,KAAA6G,EAAAW,IAAAX,EAAApT,IAAA,GAAA0T,WAUAU,EAAA,WACA,IAAAL,EAAAX,EAAAW,IACA,OAAAA,EAAA,GAAA,GAAAA,EAAA,KAAA,GAAAA,EAAA,KAAA,EAAA,EAAA,GAMAM,EAAA,WACA,IAAArU,EAAAoT,EAAApT,IAAAiU,EAAAb,EAAAa,IACA,OADAb,EAAAW,KACA,KAAA/T,GAAAiU,EAAA,EAAA,EAAA,IAAAjU,GAAA,EAAAiU,GAAA,EAAA,IAMAF,EAAA,WACA,OAAAtD,EAAAiC,eAMA4B,EAAA,WACA,OAAAlB,EAAAW,IAAAvH,WAAA1M,OAAA,IAUAhB,EAAA,WACA,OAAAsU,EAAAmB,IAAAhM,eAMAgM,EAAA,WACA,IAAAvU,EAAAoT,EAAAoB,IAAA,GAAA,EAAA,EACA,OAAAlD,EAAAzC,SAAA7O,IAMAyU,EAAA,WACA,IAAAC,EAAAjE,EAAAkE,cAAAtG,EAAAE,KAAAxO,EAAA,GAAA0Q,EAAAmE,gBAAAzB,EAAA1C,EAAAoE,gBACA,OAAAxG,EAAA0B,KAAAnD,KAAAuC,OAAAuF,EAAA3U,EAAAoT,EAAA9E,EAAAE,MAAA,MAAA,IAAA,IAMAuG,EAAA,WACA,OAAA1B,EAAAoB,IAAA,IAAA,IAMAA,EAAA,WACA,OAAA/D,EAAAsE,YAMAC,EAAA,WACA,OAAA3G,EAAA0B,KAAAqD,EAAA0B,IAAA,IAMAJ,EAAA,WACA,OAAArG,EAAA0B,KAAAqD,EAAAoB,IAAA,IAMAzU,EAAA,WACA,OAAAsO,EAAA0B,KAAAU,EAAAwE,aAAA,IAMA9B,EAAA,WACA,OAAA9E,EAAA0B,KAAAU,EAAAyE,aAAA,IAMAC,EAAA,WACA,OAAA9G,EAAA0B,KAAA,IAAAU,EAAA2E,kBAAA,IAUAzO,EAAA,WAEA,MADA,WAAA0O,KAAA3M,OAAA+H,IAAA,IACA,8BAMA6E,EAAA,WAGA,OAFA,IAAA/I,KAAA6G,EAAAW,IAAA,GAAAxH,KAAAgJ,IAAAnC,EAAAW,IAAA,IACA,IAAAxH,KAAA6G,EAAAW,IAAA,GAAAxH,KAAAgJ,IAAAnC,EAAAW,IAAA,GACA,EAAA,GAMAyB,EAAA,WACA,IAAAC,EAAAhF,EAAAiF,oBAAA5W,EAAA8N,KAAA+I,IAAAF,GACA,OAAA,EAAAA,EAAA,IAAA,KAAApH,EAAA0B,KAAA,IAAAnD,KAAAuC,MAAArQ,EAAA,IAAAA,EAAA,GAAA,IAMA8W,EAAA,WACA,IAAAJ,EAAApC,EAAAoC,IACA,OAAAA,EAAAlO,OAAA,EAAA,GAAA,IAAAkO,EAAAlO,OAAA,EAAA,IAMAuO,EAAA,WAEA,OADAnN,OAAA+H,GAAApG,MAAA+D,EAAAmB,UAAA,CAAA,KAAAuG,MAAArX,QAAA2P,EAAAoB,OAAA,KACA,OAMAuG,EAAA,WACA,OAAA,IAAAtF,EAAAiF,qBAUAM,EAAA,WACA,MAAA,iBAAAvX,QAAA4U,EAAAJ,IAMAgD,EAAA,WACA,MAAA,mBAAAxX,QAAA4U,EAAAJ,IAMAiD,EAAA,WACA,OAAAzF,EAAA0F,UAAA,KAAA,IAGA,OAAAlD,EAAAD,EAAAA,IAEAoD,WAAA,SAAA3F,EAAAC,GACA,IAAA3Q,EAAAC,EAAAkR,EAAA1S,EAAAwU,EAAAd,EAAA,GACA,GAAA,iBAAAzB,KACAA,EAFA7V,KAEA4V,UAAAC,EAAAC,IAEA,OAAA,KAGA,GAAAD,aAAAlE,KAAA,CAEA,IADA2E,EAAAR,EAAA7V,OACAkF,EAAA,EAAAA,EAAAmR,EAAAnR,IAEA,OADAiT,EAAAtC,EAAAtI,OAAArI,KAVA,OAWAiT,IAGA,EAAAjT,GAdA,OAcA2Q,EAAAtI,OAAArI,EAAA,GACAmS,GAAAc,GAGAxU,EAlBA5D,KAkBAmY,YAAAC,EAAAvC,GACA1Q,IAAAmR,EAAA,GAnBAtW,KAmBA0U,SAAAvG,KAAAiK,IAAA,MAAAtC,EAAAtI,OAAArI,EAAA,KACAC,EAAAqO,EAAAoB,OAAAjR,IAAA,EACAA,GArBA5D,KAqBA4T,aAAAM,QAAA9O,IAEAkS,GAAA1T,IAEA,OAAA0T,EAEA,MAAA,KAGA/D,IFloBApU,kBAAA,CAEAsc,cAAA,CAAA,WAAA,aAKAlX,KAAA,WAIA,IAAAS,EAAArF,EAAAE,GAAAC,SACAH,EAAAE,GAAAC,SAAA,SAAAC,GACA,IAAAoB,EAAA,0BAMA,OAJA,IADAxB,EAAAK,MAAAiM,KAAA,eAAA9K,EAAA,MACAlB,QACAN,EAAA,WAAAS,KAAA,CAAA+G,KAAA,SAAAhG,KAAAA,IAAAE,SAAArB,MAGAgF,EAAAD,MAAA/E,KAAA,CAAAD,KAIAJ,EAAAO,UAAAyQ,kBAAA,GACAhR,EAAAO,UAAAqD,eAAA,aAEA5D,EAAAO,UAAAsD,UAAAxD,KAAA0b,WACA/b,EAAAO,UAAAqI,UAAAoT,gBAAA,GAGA3b,KAAA4b,oBAGAF,WAAA,SAAArZ,GAEA,IAAAE,EAAA,GACArC,EAAAP,EAAAQ,KAAAkC,EAAAV,KAAA,aACAka,EAAA3b,EAAAyb,gBAGA,OAAA,IAAAtZ,EAAAlB,KAAA8V,QAAA,OAIA5U,EAAAlB,QAAA0a,IACAA,EAAAxZ,EAAAlB,MAAA,IAGAxB,EAAAyC,KAAAlC,EAAAG,SAAAkC,MAAA,SAAApB,EAAA2a,GACA,IAKAC,EAEAC,EAPA7a,KAAA0a,EAAAxZ,EAAAlB,MACAoB,EAAApD,kBAAA8c,QAAAC,WAAA3Z,EAAAsZ,EAAAxZ,EAAAlB,MAAAA,KAEA0a,EAAAxZ,EAAAlB,MAAAA,GAAA,GAEA4a,EAAA5c,kBAAA8c,QAAAE,kBAAAhb,GACAkB,EAAAlB,KAAAsO,MAAAsM,KACAC,EAAArc,EAAAO,UAAA8C,cAAA8Y,IAAA,GACAD,EAAAxZ,EAAAlB,MAAAA,GAAA6a,EAEAzZ,EAAApD,kBAAA8c,QAAAC,WAAA3Z,EAAAyZ,QAlBAzZ,GA0BAqZ,iBAAA,WA2CA,SAAAQ,EAAAlc,GACA,IAAAkc,EAAAzc,EAAAO,EAAAoB,aAAAlB,KAAA,UAKA,OAJAT,EAAAO,EAAAoB,aAAA2K,KAAA,yBAAAhM,SACAmc,EAAAzc,EAAAO,EAAAoB,aAAA2K,KAAA,yBAAA7K,OAGAgb,EAYA,SAAAC,EAAAnc,EAAAmC,EAAAqC,EAAAvE,GACA,MAAA,CACAyS,KAAA,QACAvC,KAAA,WAAAhO,EAAAlB,KACA0R,SAAA,OACA1S,KAAAA,EACA2S,QAAA5S,EAAAoB,YACAmG,IAAA9H,EAAAO,EAAAoB,aAAAlB,KAAA,UACA+G,KAAAiV,EAAAlc,GACAoc,WAAA,SAAAnJ,GACA,IAAAoJ,EAAA7X,EA9DA,GAAA,GAAA,GA+DA,GAAA,QAAA0X,EAAAlc,IAAAqc,EACA,OAAApJ,EAAAqJ,iBAAA,eAAAD,KAeA,SAAAE,EAAAvc,EAAAwc,EAAAra,EAAAE,GACA,IAAAoa,GAAA,EACAlK,EAAAvS,EAAAqQ,cAAAlO,GAwDA,OAtDA1C,EAAAyC,KAAAG,EAAA,SAAA4C,EAAAvC,GACA,IAAAga,EAAAha,EAAA,KAAA,IAAAzD,kBAAAsc,cAAAxE,QAAArU,EAAA,IACAgK,EAAAhK,EAAA,GACA+H,EAAA/H,EAAA,GAEA,OAAAga,GAAA1c,EAAAuG,SAAApE,KACAsa,EAAA,6BAIA9b,IAAA1B,kBAAA+N,QAAAN,GACAjN,EAAAyC,KAAAsa,EAAA,SAAAvZ,EAAA+F,GAuBA,IAAA,KAtBAyT,EAAAxd,kBAAA+N,QAAAN,GAAApL,KAAAtB,EAAAgJ,EAAA7G,EAAAO,EAAA,GAAA,SAAAX,GAEA,IACAuE,EAOAiE,EATAvK,EAAAG,SAAA4C,SAAAZ,EAAAlB,MAAA0b,wBAAApK,EAAAC,gBACAzQ,GACAuE,EAAAtG,EAAAgB,cACAhB,EAAAkK,eAAA/H,GACAnC,EAAAgB,cAAAsF,EACAtG,EAAA0K,YAAAN,KAAAjI,UACAnC,EAAA6G,QAAA1E,EAAAlB,MACAjB,EAAAsJ,gBAEAiB,EAAA,IACApI,EAAAlB,MACAsR,EAAA9H,QACA,mBAAAA,EAAAA,EAAAzB,GAAAyB,EACAzK,EAAA6G,QAAA1E,EAAAlB,OAAA,EACAjB,EAAAsJ,WAAAiB,IAEAvK,EAAAsJ,WAAAtJ,EAAAoJ,UACAmJ,EAAAxQ,MAAAA,KAKA,OAAA,IAIA0a,GAAA,GAGA,IAAAA,GACAzc,EAAAG,SAAA4C,SAAAZ,EAAAlB,QACAjB,EAAAG,SAAA4C,SAAAZ,EAAAlB,MAAA,IAGAjB,EAAAG,SAAA4C,SAAAZ,EAAAlB,MAAAhC,kBAAAwL,GAEA,QAPA,KAYAgS,EAOAhd,EAAAO,UAAAwR,UAAA,oBAAA,SAAAxI,EAAA7G,EAAAqC,GACA,IAAAnC,EAAA,GACAmZ,EAAA,GACA/b,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAvC,GAEA,IAAAka,GAAA,IAAAla,EAAA,GAAAqU,QAAA,KACArU,EAAA,KAAA,IAAAzD,kBAAAsc,cAAAxE,QAAArU,EAAA,IACAka,EAAApB,EAAA5W,QAAAlC,GAAAL,EAAAuC,QAAAlC,GAEAka,EAAApB,EAAApR,KAAA1H,GAAAL,EAAA+H,KAAA1H,KAKA,IAAAma,EAAAN,EAAAzc,KAAA,CAAAkJ,GAAA7G,EAAAE,GAGAya,EAAA/X,MAAAuM,QAAAtI,GAAAA,EAAA,CAAAA,GACA+T,EAAAR,EAAAzc,KAAAgd,EAAA3a,EAAAqZ,GAEA,OAAAqB,GAAAE,GACA,IAMAtd,EAAAO,UAAAwR,UAAA,0BAAA,SAAAxI,EAAA7G,EAAAqC,GAEA,GA1JAA,EA0JAA,EAzJAkY,GAAA,EACAjd,EAAAyC,KAAAsC,EAAA,SAAAS,EAAA8H,GACA2P,EAAAA,GAAA3P,EAAA,MAGA2P,GAoJA5c,KAAAyG,SAAApE,GACA,MAAA,sBA3JA,IAAAqC,EACAkY,EA8JA1c,EAAAC,EADAsS,EAAAzS,KAAAuQ,cAAAlO,GASA,OANArC,KAAAK,SAAA4C,SAAAZ,EAAAlB,QACAnB,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAA,IAEAsR,EAAAC,gBAAA1S,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAA0b,wBACA7c,KAAAK,SAAA4C,SAAAZ,EAAAlB,MAAA0b,wBAAApK,EAAA9H,QAEAxL,kBAAA8c,QAAAiB,YAAAzK,EAAAjC,IAAAtH,IAAAuJ,EAAAjC,MAAAtH,EACAuJ,EAAAxQ,OAGAwQ,EAAAjC,IAAAtH,GACAhJ,EAAAF,MACAmQ,aAAA9N,IAEAlC,EAAAR,EAAAO,EAAAoB,aAAA6b,kBACA7S,KAAA,CAAAnJ,KAAA,gBAAA+H,MAAA7G,EAAAlB,OACAhB,EAAAmK,KAAA,CAAAnJ,KAAA,6BAAA+H,MAAAxE,EA1LA,GAAA,GAAA,KA4LA/E,EAAAgT,KAAA0J,EAAAnc,EAAAmC,EAAAqC,EAAAvE,IACAid,OAAA,SAAArK,EAAAsK,GACA,IAAA5S,EAAAE,EAAAnE,EAAAvE,EAEA,GAAA,UAAAob,EACApb,GAAA,EACA8Q,EAAA5T,kBAAA8c,QAAAqB,mBAAAvK,OACA,CAAA,GAAA,YAAAsK,EAGA,OAFApb,GAAA,IAAA8Q,GAAA,SAAAA,EAKA7S,EAAAG,SAAA4C,SAAAZ,EAAAlB,MAAA0b,wBAAApK,EAAAC,gBAEAzQ,GACAuE,EAAAtG,EAAAgB,cACAhB,EAAAkK,eAAA/H,GACAnC,EAAAgB,cAAAsF,EACAtG,EAAA0K,YAAAN,KAAAjI,UACAnC,EAAA6G,QAAA1E,EAAAlB,MACAjB,EAAAsJ,eAEAiB,EAAA,GACAE,EAAAoI,GAAA7S,EAAA8N,eAAA3L,EAAA,UACAoI,EAAApI,EAAAlB,MACAsR,EAAA9H,QACA,mBAAAA,EAAAA,EAAAzB,GAAAyB,EAAA,GACAzK,EAAA6G,QAAA1E,EAAAlB,OAAA,EACAjB,EAAAsJ,WAAAiB,IAEAvK,EAAAsJ,WAAAtJ,EAAAoJ,UACAmJ,EAAAxQ,MAAAA,EACA/B,EAAAkQ,YAAA/N,EAAAJ,KAGA,YACA,IAKAtC,EAAAO,UAAAwR,UAAA,+BAAA,SAAAxI,EAAA7G,EAAAqC,GAEA,IAAAxE,EAAAF,KACAyS,EAAAvS,EAAAqQ,cAAAlO,GAEAlC,EAAAR,EAAAO,EAAAoB,aAAA6b,iBAIA,OAHAhd,EAAAmK,KAAA,CAAAnJ,KAAA,4BAAA+H,MAAA,IAGAqU,KAAAC,UAAA/K,EAAAjC,OAAA+M,KAAAC,UAAArd,IACAsS,EAAAxQ,OACA/B,EAAAsJ,WAAAiJ,EAAAhI,QAAA,IAGAgI,EAAAxQ,QAGAwQ,EAAAjC,IAAArQ,EACAH,KAAAmQ,aAAA9N,GAEA1C,EAAAgT,KAAA0J,EAAAnc,EAAAmC,EAAAqC,EAAAvE,IACAid,OAAA,SAAArK,EAAAsK,GACA,IAAA5S,EAAA,GACAxI,EAAA,YAAAob,KAAA,IAAAtK,GAAA,SAAAA,GAEA9Q,GACA/B,EAAAkM,iBACAlM,EAAAsK,OAAAtK,EAAAmG,UAAAhE,KAEA1C,EAAAyC,KAAA2Q,EAAA,SAAA0K,EAAAC,GACA,IAAAhY,EAAAvG,kBAAA8c,QAAA7U,WAAAlH,EAAAud,GAAA,GACA/X,IACA+E,EAAA/E,EAAAvE,MAAAhC,kBAAA8c,QAAA0B,OAAAD,EAAA,IAAA,OAMA/d,EAAAie,cAAAnT,KACAxI,GAAA,IAIAwQ,EAAAxQ,MAAAA,EACAwQ,EAAAhI,OAAAA,EACAvK,EAAAsJ,WAAAiB,GACAvK,EAAAkQ,YAAA/N,EAAAJ,KAGA,YACA,MAIAtC,EAAA,WACAR,kBAAAoF,SG7XA,wBAEA,IAAAsZ,EAAA,CAEAC,iDAIA,SAAAve,GAIAA,EAAAC,QAAA,SAAAue,GAQA,IAKAtI,EALAuI,EAAA,GACAC,EAAAtZ,UAAA1E,OACAie,EAAA,GACA/Y,EAAA,EACAgZ,EAAA,GAGAC,EAAA,IAAAF,KAAAH,EACA,IAAA5Y,EAAA,EAAAA,EAAA8Y,EAAA9Y,IAAA,CAEA,IAAAgZ,KADA1I,EAAA9Q,UAAAQ,GAEA,GAAAsQ,EAAA0I,KAAAJ,EAAAG,GAEA,SAAAE,EAGAJ,EAAAE,GAAAH,EAAAG,GAIA,OAAAF,IAMAK,mDAIA,SAAA9e,GAIA,IAAA+e,EAAA,UACAC,EAAA,UACAC,EAAA,gCACAC,EAAA,sBACAC,EAAA,qBACAC,EAAA,mBACAC,EAAA,gBACAC,EAAA,eACAC,EAAA,mBACAC,EAAA,kBACAC,EAAA,kBAEAC,EAAA,2DACAC,EAAA,8BACAC,EAAAF,EAAA,IAAAC,EAAA,aAEAE,EAAA,qFACAC,EAAA,0BACAC,EAAA,8EAAAH,EAEAI,EAAA,eAEAC,EAAA,aAEAC,EAAA,mBACAC,EAAA,kBACAC,EAAA,0CACAC,EAAA,4BAEAC,EAAA,wFACAC,EAAA,oDAEAC,EAAA,IAAAF,EAAA,IAAAC,EAAA,iCAEAE,EAAA,kBAAAvB,EAAA,KAAAG,EAAA,KAKAqB,EAAAF,EAAA,YAAAJ,EAAA,kBAEA,SAAAO,EAAApJ,EAAAqJ,GAGA,OAFAA,EAAAA,GAAAA,EAAAxS,eAGA,IAAA,IACAmJ,GAAA,KAAAA,GAAA,GAAA,EACA,MACA,IAAA,IACAA,GAAA,KAAAA,EAAA,GAAA,EAIA,OAAAA,EAGA,SAAAsJ,EAAAC,GACA,IAAA1J,GAAA0J,EAMA,OAJAA,EAAApgB,OAAA,GAAA0W,EAAA,MACAA,GAAAA,EAAA,GAAA,IAAA,MAGAA,EAGA,SAAA2J,EAAAC,GACA,MAAA,CACAC,IAAA,EACAC,QAAA,EACAtb,EAAA,EACAub,IAAA,EACAC,SAAA,EACAC,GAAA,EACAC,IAAA,EACAC,MAAA,EACAC,IAAA,EACAC,IAAA,EACAC,MAAA,EACAC,GAAA,EACAC,IAAA,EACAhX,EAAA,EACAiX,IAAA,EACAC,KAAA,EACAC,GAAA,EACAC,IAAA,EACAC,KAAA,EACAC,IAAA,EACAC,IAAA,EACAC,OAAA,EACAC,KAAA,EACAC,IAAA,EACAC,KAAA,EACAC,UAAA,EACAC,GAAA,EACAC,IAAA,EACAC,QAAA,EACAC,EAAA,EACAC,IAAA,GACAC,SAAA,GACAC,GAAA,GACAC,IAAA,GACAC,SAAA,GACAC,IAAA,IACAlC,EAAA5S,eAGA,SAAA+U,EAAAC,EAAA,GACA,IAAAC,EAAA,EAAAje,UAAA1E,aAAAY,IADA,EAAA,EACA,EAmBA,MAjBA,CACAgiB,IAAA,EACAC,OAAA,EACAC,IAAA,EACAC,QAAA,EACAC,IAAA,EACAC,UAAA,EACAC,IAAA,EACAC,SAAA,EACAC,IAAA,EACAC,OAAA,EACAC,IAAA,EACAC,SAAA,EACAC,IAAA,EACAC,OAAA,GAGAf,EAAAhV,gBAAAiV,EAoCA,SAAAe,EAAAC,EAAAC,GAIA,KAFAD,EAAAA,GAAAA,EAAAnU,MADA,sCAIA,OAAAoU,EAGA,IAAAC,EAAA,MAAAF,EAAA,IAAA,EAAA,EACAG,GAAAH,EAAA,GACAI,GAAAJ,EAAA,GAQA,OANAA,EAAA,IAAAA,EAAA,KACAI,EAAAhS,KAAAuC,MAAAwP,EAAA,KACAA,EAAA/R,KAAAuC,MAAAwP,EAAA,MAIAD,GAAA,GAAAC,EAAAC,GAAA,GAIA,IAAAC,EAAA,CACAC,KAAA,MACAC,KAAA,MACAC,MAAA,KACAC,KAAA,MACAC,KAAA,MACAC,KAAA,KACAC,MAAA,MACAC,MAAA,KACAC,MAAA,MACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,KACAC,KAAA,KACAC,KAAA,MACAC,IAAA,KACAC,KAAA,MACAC,IAAA,KACAC,MAAA,MACAC,KAAA,KACAC,KAAA,MACAC,KAAA,KACAC,IAAA,KACAC,KAAA,MACAC,KAAA,KACAC,KAAA,MACAC,KAAA,KACAC,KAAA,KACAC,KAAA,KACAC,IAAA,MACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,IAAA,KACAC,KAAA,MACAC,KAAA,MACAC,KAAA,KACAC,KAAA,MACAC,MAAA,MACApO,KAAA,KACAqO,IAAA,MACAC,IAAA,EACAC,IAAA,KACAC,KAAA,MACAC,KAAA,MACAC,IAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,KACAC,KAAA,MACAC,KAAA,MACAC,IAAA,MACAC,IAAA,MACAC,IAAA,KACAC,IAAA,KACAC,IAAA,KACAC,IAAA,MACAC,IAAA,KACAC,IAAA,KACAC,IAAA,MACAC,IAAA,KACAC,MAAA,KACAC,KAAA,MACAC,KAAA,MACAC,KAAA,KACAC,IAAA,KACAC,IAAA,KACAC,KAAA,MACAC,IAAA,MACAC,IAAA,MACAC,KAAA,MACAC,KAAA,MACAC,MAAA,KACAC,KAAA,KACAC,KAAA,IACAC,KAAA,MACAC,KAAA,IACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,IAAA,KACAC,KAAA,MACAC,KAAA,MACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,IAAA,KACAC,KAAA,KACAC,MAAA,MACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,IAAA,MACAC,IAAA,EACAC,IAAA,EACAC,KAAA,KACAC,IAAA,KACAC,KAAA,KACAC,KAAA,KACAC,IAAA,EACAC,IAAA,MACAC,KAAA,MACAC,IAAA,MACAC,IAAA,KACAC,MAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACAC,KAAA,MACArnB,EAAA,KACAkV,EAAA,KACAgC,EAAA,MACA1C,EAAA,MACA3M,EAAA,KACAyf,EAAA,MACAtR,EAAA,MACAE,EAAA,MACAjV,EAAA,MACAgZ,EAAA,KACApF,EAAA,MACAlL,EAAA,MACAzI,GAAA,KACAqU,GAAA,KACAgS,GAAA,MACAC,GAAA,MACArQ,GAAA,KACA9C,GAAA,MACAD,GAAA,MACAiC,GAAA,MACApQ,GAAA,MACA0O,GAAA,KACAsJ,GAAA,MACAzI,GAAA,MACAR,EAAA,GAGAyS,EAAA,CACAC,UAAA,CACAC,MAAA,cACA1qB,KAAA,YACA2qB,SAAA,WAEA,QADA9rB,KAAA+rB,GACA/rB,KAAAgsB,cAIAC,IAAA,CACAJ,MAAA,QACA1qB,KAAA,OAIA+qB,KAAA,CACAL,MAAA,SACA1qB,KAAA,OACA2qB,SAAA,WACA,OAAA9rB,KAAAgsB,aAAAhsB,KAAAmsB,KAAA,GAAA,EAAA,EAAA,KAIAC,gBAAA,CACAP,MAAA,qBACA1qB,KAAA,mBACA2qB,SAAA,WACA,OAAA9rB,KAAAgsB,cAIAK,SAAA,CACAR,MAAA,aACA1qB,KAAA,WACA2qB,SAAA,WAEA,OADA9rB,KAAA+rB,IAAA,EACA/rB,KAAAgsB,cAIAM,UAAA,CACAT,MAAA,aACA1qB,KAAA,YACA2qB,SAAA,SAAArc,EAAA6c,GAOA,OANAtsB,KAAA6J,KAAAyiB,EACAtsB,KAAA0Z,EAAA,KACA1Z,KAAA6N,EAAA,EACA7N,KAAA0Y,EAAA,EACA1Y,KAAAusB,MAAA,EAEAvsB,KAAAgsB,aAAAhsB,KAAAwsB,KAAA,KAIAC,eAAA,CACAZ,MAAA,wBACA1qB,KAAA,yBACA2qB,SAAA,SAAArc,EAAAoH,GACA,UAAAA,EAAAlJ,cACA3N,KAAA0sB,sBAAA,EAEA1sB,KAAA0sB,uBAAA,IAKAC,cAAA,CACAd,MAAAxmB,OAAA,oBAAAoZ,EAAAF,EAAAC,EAAA,IAAA,KACArd,KAAA,mBACA2qB,SAAA,SAAArc,EAAAmd,EAAA7I,EAAA5D,GACA,IACArJ,GAAAiN,EACA8I,EAAA,GASA,MAXA,SAAAD,EAAAjf,kBAKAmJ,EACA+V,EAAA,IAGA/V,EAAAoJ,EAAApJ,EAAAqJ,GAEAngB,KAAAgsB,aAAAhsB,KAAAmsB,KAAArV,EAAA+V,EAAA,EAAA,KAIAC,UAAA,CACAjB,MAAAxmB,OAAA,KAAA+Z,EAAA,IAAAC,EAAA,IAAAf,EAAA,IAAAW,EAAA,IAAAC,EAAA,IAAAZ,EAAA,KAAA,KACAnd,KAAA,aAIA4rB,UAAA,CACAlB,MAAAxmB,OAAA,IAAAsZ,EAAA,IAAAE,EAAA,IAAAE,EAAA,eAAAP,EAAA,KACArd,KAAA,YACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAAG,EAAAC,EAAA9M,GACA,OAAAngB,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,IAAA0M,GAAAG,GAAAC,EAAAvgB,OAAA,EAAA,MAIAwgB,WAAA,CACArB,MAAA,gCACA1qB,KAAA,QACA2qB,SAAA,SAAArc,EAAAoH,EAAAsW,EAAAxW,GACA,IAAAC,EAAA,CACAwW,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,EACAC,IAAA,GACAC,IAAA,IACAZ,EAAA1f,eACA,OAAAzN,KAAAguB,IAAA,IAAAjZ,SAAA4B,EAAA,IAAAC,EAAA7B,SAAA8B,EAAA,OAIAoX,WAAA,CACApC,MAAAxmB,OAAA,IAAAsZ,EAAA,OAAAC,EAAA,OAAAG,EAAAR,EAAAC,EAAA,KACArd,KAAA,aACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAAG,EAAA7M,GACA,OAAAngB,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,IAAA0M,GAAAG,EAAA,KAIAkB,YAAA,CACArC,MAAAxmB,OAAA,IAAAsZ,EAAA,OAAAE,EAAAN,EAAAC,EAAA,KACArd,KAAA,cACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAA1M,GACA,OAAAngB,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,IAAA0M,EAAA,EAAA,KAIAsB,WAAA,CACAtC,MAAAxmB,OAAA,IAAAsZ,EAAAJ,EAAAC,EAAA,KACArd,KAAA,aACA2qB,SAAA,SAAArc,EAAAqH,EAAAqJ,GACA,OAAAngB,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,GAAA,EAAA,EAAA,KAIAiO,KAAA,CACAvC,MAAAxmB,OAAA,IAAAma,EAAA,IAAAE,EAAA,IAAAE,EAAA,IAAAlB,EAAA,IAAAG,EAAA,IAAAE,EAAAC,EAAAgB,EAAA,IAAA,KACA7e,KAAA,OACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,EAAAC,EAAA+V,EAAAG,EAAAC,EAAAoB,GACA,OAAAruB,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,GAAAC,EAAAvgB,OAAA,EAAA,KAAA1M,KAAAwsB,KAAA7I,EAAA0K,MAIAC,KAAA,CACAzC,MAAAxmB,OAAA,IAAAma,EAAA,IAAAC,EAAA,IAAAE,EAAA,IAAAlB,EAAA,IAAAG,EAAA,IAAAE,GACA3d,KAAA,OACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,EAAAC,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAuB,KAAA,CACA1C,MAAAxmB,OAAA,IAAAma,EAAA,IAAAE,EAAA,IAAAE,EAAA,IAAAlB,EAAA,IAAAG,EAAA,IAAAE,EAAA,KACA5d,KAAA,OACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,EAAAC,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAwB,OAAA,CACA3C,MAAAxmB,OAAA,IAAAma,EAAAE,EAAAE,EAAA,IAAAnB,EAAA,IAAAI,EAAA,IAAAE,GACA5d,KAAA,SACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,EAAAC,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAyB,cAAA,CACA5C,MAAAxmB,OAAA,IAAAma,EAAAE,EAAAE,EAAA,OAAAnB,EAAAI,EAAAE,GACA5d,KAAA,gBACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,EAAAC,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIA0B,IAAA,CACA7C,MAAAxmB,OAAA,IAAAsa,EAAA,KAAAG,EAAA,KAAAN,EAAA,IAAAd,EAAA,IAAAG,EAAA,IAAAE,EAAAT,EAAA0B,EAAA,KACA7e,KAAA,MACA2qB,SAAA,SAAArc,EAAAoH,EAAAD,EAAAD,EAAAG,EAAA+V,EAAAG,EAAAqB,GACA,OAAAruB,KAAAguB,KAAArX,EAAA2J,EAAA1J,IAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,IAAAhtB,KAAAwsB,KAAA7I,EAAA0K,MAIAM,YAAA,CACA9C,MAAAxmB,OAAA,MAAAoZ,EAAA,OAAAG,EAAA,OAAAE,EAAAE,EAAA,KACA7d,KAAA,cACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAAG,EAAAC,GACA,OAAAjtB,KAAAmsB,MAAArV,GAAA+V,GAAAG,GAAAC,EAAAvgB,OAAA,EAAA,MAIAkiB,YAAA,CACA/C,MAAAxmB,OAAA,IAAA0a,EAAA,YAAAJ,EAAA,kBAAAJ,EAAA,KACApe,KAAA,cACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAF,GACA,OAAA3W,KAAAguB,IAAA5N,EAAAzJ,GAAA2J,EAAA1J,IAAAC,KAIAgY,aAAA,CACAhD,MAAAxmB,OAAA,IAAAsa,EAAA,UAAAF,EAAA,OAAAD,GACAre,KAAA,eACA2qB,SAAA,SAAArc,EAAAoH,EAAAD,EAAAD,GACA,OAAA3W,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,KAIAiY,aAAA,CACAjD,MAAAxmB,OAAA,IAAAsa,EAAA,SAAAF,EAAA,iBACAte,KAAA,eACA2qB,SAAA,SAAArc,EAAAoH,EAAAD,EAAAD,GACA,OAAA3W,KAAAguB,IAAA5N,EAAAzJ,GAAAC,EAAA,GAAAC,KAIAkY,WAAA,CACAlD,MAAAxmB,OAAA,MAAAoZ,EAAA,OAAAG,EAAA,OAAAE,GACA3d,KAAA,aACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAgC,YAAA,CACAnD,MAAAxmB,OAAA,IAAAma,EAAAE,EAAAE,GACAze,KAAA,cACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,KAIAoY,QAAA,CACApD,MAAAxmB,OAAA,IAAAma,EAAA,gEACAre,KAAA,UACA2qB,SAAA,SAAArc,EAAAkH,EAAAE,GACA,OAAA7W,KAAAguB,KAAArX,EAAA,GAAAE,KAIAqY,YAAA,CACArD,MAAAxmB,OAAA,MAAAoZ,EAAA,OAAAG,EAAA,KACAzd,KAAA,cACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,GACA,OAAA7sB,KAAAmsB,MAAArV,GAAA+V,EAAA,EAAA,KAIAsC,eAAA,CACAtD,MAAAxmB,OAAA,MAAAqZ,EAAAG,EAAAE,EAAA,KACA5d,KAAA,iBACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAoC,iBAAA,CAIAvD,MAAAxmB,OAAA,IAAAma,EAAA,IAAAE,EAAA,IAAAE,EAAA,KACAze,KAAA,mBACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,KAIAwY,UAAA,CACAxD,MAAAxmB,OAAA,IAAAma,EAAA,IAAAC,EAAA,IAAAE,GACAxe,KAAA,YACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,KAIAyY,SAAA,CACAzD,MAAAxmB,OAAA,IAAAoa,EAAA,IAAAE,EAAA,IAAAJ,GACApe,KAAA,WACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAF,GACA,OAAA3W,KAAAguB,IAAA5N,EAAAzJ,GAAAC,EAAA,GAAAC,KAIA0Y,cAAA,CACA1D,MAAAxmB,OAAA,IAAAoa,EAAA,IAAAE,GACAxe,KAAA,gBACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,GACA,OAAA7W,KAAAguB,IAAAhuB,KAAA0Z,EAAA9C,EAAA,GAAAC,KAIA2Y,2BAAA,CAEA3D,MAAAxmB,OAAA,IAAAka,EAAA,IAAAE,EAAA,IAAAE,GACAxe,KAAA,8BACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,IAAA5N,EAAAzJ,GAAAC,EAAA,GAAAC,KAIA4Y,aAAA,CACA5D,MAAAxmB,OAAA,oBAAAqa,EAAA,IAAAE,GACAze,KAAA,eACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,KAAArX,EAAAC,EAAA,GAAAC,KAIA6Y,WAAA,CACA7D,MAAAxmB,OAAA,MAAAqZ,EAAAG,EAAA,KACA1d,KAAA,aACA2qB,SAAA,SAAArc,EAAAqH,EAAA+V,GAGA,OAAA7sB,KAAA2vB,OACA,KAAA,EACA,OAAA3vB,KAAAmsB,MAAArV,GAAA+V,EAAA,EAAA7sB,KAAAwrB,GACA,KAAA,EAIA,OAHAxrB,KAAA0Z,EAAA,IAAA5C,IAAA+V,EACA7sB,KAAA2vB,SAEA,EACA,QACA,OAAA,KAKAC,eAAA,CACA/D,MAAAxmB,OAAA,IAAAma,EAAA,IAAAC,GACAte,KAAA,iBACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,GACA,OAAA5W,KAAAguB,KAAArX,EAAAC,EAAA,EAAA,KAIAiZ,cAAA,CAGAhE,MAAAxmB,OAAA,gCAAAya,EAAA,KAAAF,EAAA,KACAze,KAAA,gBACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,EAAAC,GACA,OAAA7W,KAAAguB,IAAA5N,EAAAzJ,GAAA2J,EAAA1J,IAAAC,KAIAiZ,SAAA,CACAjE,MAAAxmB,OAAA,IAAAsa,EAAA,YAAAI,EAAA,YAAAR,EAAA,KACApe,KAAA,WACA2qB,SAAA,SAAArc,EAAAoH,EAAAD,EAAAD,GACA,OAAA3W,KAAAguB,IAAA5N,EAAAzJ,GAAA2J,EAAA1J,IAAAC,KAIAkZ,UAAA,CACAlE,MAAAxmB,OAAA,IAAA0a,EAAA,YAAAP,EAAA,KACAre,KAAA,YACA2qB,SAAA,SAAArc,EAAAmH,EAAAD,GACA,OAAA3W,KAAAguB,KAAArX,EAAA2J,EAAA1J,GAAA,KAIAoZ,aAAA,CACAnE,MAAAxmB,OAAA,IAAAma,EAAA,YAAAO,EAAA,KACA5e,KAAA,eACA2qB,SAAA,SAAArc,EAAAkH,EAAAC,GACA,OAAA5W,KAAAguB,KAAArX,EAAA2J,EAAA1J,GAAA,KAIAqZ,YAAA,CACApE,MAAAxmB,OAAA,KAAAya,EAAA,KAAAF,EAAA,IAAAL,EAAA,KACApe,KAAA,cACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAF,GACA,OAAA3W,KAAAguB,IAAA5N,EAAAzJ,GAAA2J,EAAA1J,IAAAC,KAIAqZ,WAAA,CACArE,MAAAxmB,OAAA,IAAA4a,EAAA,KACA9e,KAAA,aACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,GACA,OAAA7W,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,KAIAsZ,cAAA,CACAtE,MAAAxmB,OAAA,IAAAsa,EAAA,YAAAI,EAAA,KACA5e,KAAA,gBACA2qB,SAAA,SAAArc,EAAAoH,EAAAD,GACA,OAAA5W,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,KAIAuZ,WAAA,CACAvE,MAAAxmB,OAAA,IAAAma,EAAA,+CACAre,KAAA,uBACA2qB,SAAA,SAAArc,EAAAkH,EAAA0Z,EAAAxZ,GAGA,GAFAA,EAAAA,GAAAA,EAAA,GAEA7W,KAAAguB,KAAArX,EAAA,EAAA,GACA,OAAA,EAIA,IAGA2Z,EAAA,GAAA,GAHAA,EAAA,IAAA3e,KAAA3R,KAAA0Z,EAAA1Z,KAAA6N,EAAA7N,KAAA0Y,GAAAO,UAGAqX,EAAA,EAAAA,GAEAtwB,KAAA+rB,IAAAuE,EAAA,GAAAD,EAAA,GAAAxZ,IAIA0Z,aAAA,CACA1E,MAAAxmB,OAAA,KAAA+Z,EAAA,IAAAC,EAAA,IAAAf,EAAA,IAAAgB,EAAA,IAAA,KACAne,KAAA,eACA2qB,SAAA,SAAArc,EAAA+gB,EAAAC,GAGA,IA/lBAC,EA+lBAC,EA7lBA,CACAC,OA3BA,CACAC,MAAA,EACApe,UAAA,EACAzS,KAAA,EACA8wB,MAAA,EACAC,KAAA,EACA/D,OAAA,EACAgE,MAAA,EACAC,OAAA,EACAC,MAAA,EACAC,MAAA,EACAC,QAAA,EACAC,MAAA,EACAC,OAAA,EACAC,MAAA,EACAC,MAAA,GACAC,SAAA,GACAC,QAAA,IAOAhB,EA+lBAF,EA/lBA7iB,eAIAgkB,SARA,CACA3xB,KAAA,GAOA0wB,IAAA,GA4lBAE,EAAAD,EAAAC,OAGA,OAAAH,EAAA9iB,eACA,IAAA,MACA,IAAA,OACA,IAAA,SACA,IAAA,UACA3N,KAAA6J,IAAA+mB,EACA,MACA,IAAA,MACA,IAAA,OACA,IAAA,SACA,IAAA,UACA5wB,KAAA4xB,IAAAhB,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA6xB,IAAAjB,EACA,MACA,IAAA,MACA,IAAA,OACA5wB,KAAA+rB,IAAA6E,EACA,MACA,IAAA,YACA,IAAA,aACA,IAAA,aACA,IAAA,cACA5wB,KAAA+rB,IAAA,GAAA6E,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA+rB,IAAA,EAAA6E,EACA,MACA,IAAA,QACA,IAAA,SACA5wB,KAAA8xB,IAAAlB,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA+xB,IAAAnB,EACA,MACA,IAAA,MACA,IAAA,SACA,IAAA,MACA,IAAA,UACA,IAAA,MACA,IAAA,YACA,IAAA,MACA,IAAA,WACA,IAAA,MACA,IAAA,SACA,IAAA,MACA,IAAA,WACA,IAAA,MACA,IAAA,SACA5wB,KAAAgsB,YACAhsB,KAAAgyB,QAAAtP,EAAA+N,EAAA,GACAzwB,KAAAiyB,gBAAA,EACAjyB,KAAA+rB,IAAA,GAAA,EAAA6E,EAAAA,EAAA,EAAAA,MAUAsB,SAAA,CACArG,MAAAxmB,OAAA,wBAAAkZ,EAAA,IAAAe,EAAA,SAAA,KACAne,KAAA,WACA2qB,SAAA,SAAArc,EAAA0iB,EAAA3B,EAAAC,GACA,IAAA2B,EAAAD,EAAAtuB,QAAA,QAAA,IAAA5D,OAEA2wB,EAAAJ,EAAAxe,KAAAE,KAAA,EAAAkgB,GAEA,OAAA3B,EAAA9iB,eACA,IAAA,MACA,IAAA,OACA,IAAA,SACA,IAAA,UACA3N,KAAA6J,IAAA+mB,EACA,MACA,IAAA,MACA,IAAA,OACA,IAAA,SACA,IAAA,UACA5wB,KAAA4xB,IAAAhB,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA6xB,IAAAjB,EACA,MACA,IAAA,MACA,IAAA,OACA5wB,KAAA+rB,IAAA6E,EACA,MACA,IAAA,YACA,IAAA,aACA,IAAA,aACA,IAAA,cACA5wB,KAAA+rB,IAAA,GAAA6E,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA+rB,IAAA,EAAA6E,EACA,MACA,IAAA,QACA,IAAA,SACA5wB,KAAA8xB,IAAAlB,EACA,MACA,IAAA,OACA,IAAA,QACA5wB,KAAA+xB,IAAAnB,EACA,MACA,IAAA,MACA,IAAA,SACA,IAAA,MACA,IAAA,UACA,IAAA,MACA,IAAA,YACA,IAAA,MACA,IAAA,WACA,IAAA,MACA,IAAA,SACA,IAAA,MACA,IAAA,WACA,IAAA,MACA,IAAA,SACA5wB,KAAAgsB,YACAhsB,KAAAgyB,QAAAtP,EAAA+N,EAAA,GACAzwB,KAAAiyB,gBAAA,EACAjyB,KAAA+rB,IAAA,GAAA,EAAA6E,EAAAA,EAAA,EAAAA,MAUAyB,QAAA,CACAxG,MAAAxmB,OAAA,KAAA8Z,EAAA,IAAA,KACAhe,KAAA,UACA2qB,SAAA,SAAArc,EAAA4iB,GACAryB,KAAAgsB,YACAhsB,KAAAgyB,QAAAtP,EAAA2P,EAAA,GAEA,IAAAryB,KAAAiyB,kBACAjyB,KAAAiyB,gBAAA,KAKAK,iBAAA,CACAzG,MAAAxmB,OAAA,KAAAga,EAAA,IAAAf,EAAA,OAAA,KACAnd,KAAA,mBACA2qB,SAAA,SAAArc,EAAA8iB,GAGA,OAFAvyB,KAAAiyB,gBAAA,EAEAM,EAAA5kB,eACA,IAAA,OACA3N,KAAA+rB,IAAA,EACA,MACA,IAAA,OACA/rB,KAAA+rB,IAAA,EACA,MACA,IAAA,OACA,IAAA,WACA/rB,KAAA+rB,IAAA,EAIA7a,MAAAlR,KAAAgyB,WACAhyB,KAAAgyB,QAAA,KAKAQ,qBAAA,CACA3G,MAAAxmB,OAAA,KAAAwa,EAAA,IAAAC,EAAA,IAAA,KACA3e,KAAA,wBACA2qB,SAAA,SAAArc,EAAAmH,GACA,OAAA5W,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,GAAA5W,KAAA0Y,KAIA2V,aAAA,CACAxC,MAAAxmB,OAAA,IAAA2a,EAAA,KACA7e,KAAA,eACA2qB,SAAA,SAAAuC,GACA,OAAAruB,KAAAwsB,KAAA7I,EAAA0K,MAIAoE,OAAA,CACA5G,MAAAxmB,OAAA,4BACAlE,KAAA,SACA2qB,SAAA,SAAArc,EAAAijB,GACA,IAAAC,EAAA1O,EAAAyO,EAAA/kB,eAEA,OAAAuD,MAAAyhB,IAIA3yB,KAAAwsB,KAAAmG,KAIAC,IAAA,CACA/G,MAAA,QACA1qB,KAAA,MACA2qB,SAAA,WACA9rB,KAAA+xB,IAAA/xB,KAAA+xB,GACA/xB,KAAA8xB,IAAA9xB,KAAA8xB,GACA9xB,KAAA+rB,IAAA/rB,KAAA+rB,GACA/rB,KAAA6xB,IAAA7xB,KAAA6xB,GACA7xB,KAAA4xB,IAAA5xB,KAAA4xB,GACA5xB,KAAA6J,IAAA7J,KAAA6J,GACA7J,KAAA6yB,IAAA7yB,KAAA6yB,KAIAC,MAAA,CACAjH,MAAAxmB,OAAA,IAAAma,GACAre,KAAA,QACA2qB,SAAA,SAAArc,EAAAkH,GAEA,OADA3W,KAAA0Z,GAAA/C,GACA,IAIAoc,WAAA,CACAlH,MAAA,YACA1qB,KAAA,cAIA6xB,sBAAA,CACAnH,MAAAxmB,OAAA,IAAA4a,EAAA,KAAAxB,EAAA,OAAAG,EAAA,OAAAE,EAAA,KACA3d,KAAA,wBACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAC,EAAA+V,EAAAG,GACA,OAAAhtB,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,GAAAG,EAAA,KAIAiG,wBAAA,CACApH,MAAAxmB,OAAA,IAAA4a,EAAAtB,EAAA,OAAAC,EAAA,OAAAG,EAAAR,EAAAC,EAAA,KACArd,KAAA,0BACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAC,EAAA+V,EAAAG,EAAA7M,GACA,OAAAngB,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,IAAA7W,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,IAAA0M,GAAAG,EAAA,KAIAkG,uBAAA,CACArH,MAAAxmB,OAAA,IAAA4a,EAAA,KAAAxB,EAAA,OAAAG,EAAA,KACAzd,KAAA,yBACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAC,EAAA+V,GACA,OAAA7sB,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,IAAA7W,KAAAmsB,MAAArV,GAAA+V,EAAA,EAAA,KAIAsG,yBAAA,CACAtH,MAAAxmB,OAAA,IAAA4a,EAAAtB,EAAA,OAAAE,EAAAN,EAAAC,EAAA,KACArd,KAAA,2BACA2qB,SAAA,SAAArc,EAAAmH,EAAAC,EAAAC,EAAA+V,EAAA1M,GACA,OAAAngB,KAAAguB,IAAAhuB,KAAA0Z,EAAA4G,EAAA1J,IAAAC,IAAA7W,KAAAmsB,KAAAjM,GAAApJ,EAAAqJ,IAAA0M,EAAA,EAAA,MAKAuG,EAAA,CAEA1Z,EAAA2Z,IACAxlB,EAAAwlB,IACA3a,EAAA2a,IAEAjZ,EAAAiZ,IACAluB,EAAAkuB,IACA9a,EAAA8a,IACA7H,EAAA6H,IAGAtB,GAAA,EACAD,GAAA,EACA/F,GAAA,EACA8F,GAAA,EACAD,GAAA,EACA/nB,GAAA,EACAgpB,GAAA,EAGAb,QAAAqB,IACApB,gBAAA,EAIAvF,sBAAA,EAGAxT,EAAAma,IAGA9G,MAAA,EACAoD,MAAA,EACA2D,MAAA,EAGAtF,IAAA,SAAAtU,EAAA7L,EAAA6K,GACA,QAAA,EAAA1Y,KAAAusB,SAIAvsB,KAAAusB,QACAvsB,KAAA0Z,EAAAA,EACA1Z,KAAA6N,EAAAA,EACA7N,KAAA0Y,EAAAA,GACA,IAEAyT,KAAA,SAAA/R,EAAAjV,EAAAoT,EAAAiT,GACA,QAAA,EAAAxrB,KAAA2vB,SAIA3vB,KAAA2vB,QACA3vB,KAAAoa,EAAAA,EACApa,KAAAmF,EAAAA,EACAnF,KAAAuY,EAAAA,EACAvY,KAAAwrB,EAAAA,GAEA,IAEAQ,UAAA,WAOA,OANAhsB,KAAAoa,EAAA,EACApa,KAAAmF,EAAA,EACAnF,KAAAuY,EAAA,EACAvY,KAAAwrB,EAAA,IACAxrB,KAAA2vB,MAAA,IAIAnD,KAAA,SAAAxI,GACA,OAAAhkB,KAAAszB,OAAA,IACAtzB,KAAAszB,QACAtzB,KAAAkZ,EAAA8K,GACA,IAKAuP,OAAA,SAAAC,GAmCA,OAlCAxzB,KAAAusB,QAAAvsB,KAAA2vB,QACA3vB,KAAAoa,EAAApa,KAAAmF,EAAAnF,KAAAuY,EAAAvY,KAAAwrB,EAAA,GAIAta,MAAAlR,KAAA0Z,KACA1Z,KAAA0Z,EAAA8Z,EAAA1b,eAGA5G,MAAAlR,KAAA6N,KACA7N,KAAA6N,EAAA2lB,EAAA7d,YAGAzE,MAAAlR,KAAA0Y,KACA1Y,KAAA0Y,EAAA8a,EAAA1a,WAGA5H,MAAAlR,KAAAoa,KACApa,KAAAoa,EAAAoZ,EAAArZ,YAGAjJ,MAAAlR,KAAAmF,KACAnF,KAAAmF,EAAAquB,EAAAnZ,cAGAnJ,MAAAlR,KAAAuY,KACAvY,KAAAuY,EAAAib,EAAAlZ,cAGApJ,MAAAlR,KAAAwrB,KACAxrB,KAAAwrB,EAAAgI,EAAAhZ,mBAIAxa,KAAA0sB,uBACA,KAAA,EACA1sB,KAAA0Y,EAAA,EACA,MACA,KAAA,EACA1Y,KAAA0Y,EAAA,EACA1Y,KAAA6N,GAAA,EAIA,IACAnG,EAIA+rB,EAgBAC,EArBAxiB,MAAAlR,KAAAgyB,YACAtqB,EAAA,IAAAiK,KAAA6hB,EAAAjY,YACAxD,YAAA/X,KAAA0Z,EAAA1Z,KAAA6N,EAAA7N,KAAA0Y,GACAhR,EAAAsQ,SAAAhY,KAAAoa,EAAApa,KAAAmF,EAAAnF,KAAAuY,EAAAvY,KAAAwrB,GAEAiI,EAAA/rB,EAAAuR,SAEA,IAAAjZ,KAAAiyB,iBAEA,IAAAwB,GAAA,IAAAzzB,KAAAgyB,UACAhyB,KAAAgyB,SAAA,GAIA,IAAAhyB,KAAAgyB,SAAA,IAAAyB,IACAzzB,KAAAgyB,QAAA,GAGAhyB,KAAA0Y,GAAA+a,EACAzzB,KAAA0Y,GAAA1Y,KAAAgyB,UAEA0B,EAAA1zB,KAAAgyB,QAAAyB,GAGAzzB,KAAA+rB,GAAA,GAAA2H,EAAA,GAAA,GAAA1zB,KAAA+rB,IAAA2H,IAAA1zB,KAAAiyB,mBACAyB,GAAA,GAGA,GAAA1zB,KAAAgyB,QACAhyB,KAAA0Y,GAAAgb,EAEA1zB,KAAA0Y,GAAA,GAAA1G,KAAA+I,IAAA/a,KAAAgyB,SAAAyB,GAGAzzB,KAAAgyB,QAAAqB,MAKArzB,KAAA0Z,GAAA1Z,KAAA+xB,GACA/xB,KAAA6N,GAAA7N,KAAA8xB,GACA9xB,KAAA0Y,GAAA1Y,KAAA+rB,GAEA/rB,KAAAoa,GAAApa,KAAA6xB,GACA7xB,KAAAmF,GAAAnF,KAAA4xB,GACA5xB,KAAAuY,GAAAvY,KAAA6J,GACA7J,KAAAwrB,GAAAxrB,KAAA6yB,GAEA7yB,KAAA+xB,GAAA/xB,KAAA8xB,GAAA9xB,KAAA+rB,GAAA,EACA/rB,KAAA6xB,GAAA7xB,KAAA4xB,GAAA5xB,KAAA6J,GAAA7J,KAAA6yB,GAAA,EAEA,IAAA7xB,EAAA,IAAA2Q,KAAA6hB,EAAAjY,WAWA,OARAva,EAAA+W,YAAA/X,KAAA0Z,EAAA1Z,KAAA6N,EAAA7N,KAAA0Y,GACA1X,EAAAgX,SAAAhY,KAAAoa,EAAApa,KAAAmF,EAAAnF,KAAAuY,EAAAvY,KAAAwrB,GAOAxrB,KAAA0sB,uBACA,KAAA,EACA1rB,EAAA6W,QAAA,GACA,MACA,KAAA,EACA7W,EAAA4W,SAAA5W,EAAA2U,WAAA,EAAA,GAWA,OANAzE,MAAAlR,KAAAkZ,IAAAlY,EAAA8Z,sBAAA9a,KAAAkZ,IACAlY,EAAA2yB,eAAA3yB,EAAA8W,cAAA9W,EAAA2U,WAAA3U,EAAA8X,WAEA9X,EAAA4yB,YAAA5yB,EAAAmZ,WAAAnZ,EAAAqZ,aAAArZ,EAAAsZ,aAAAta,KAAAkZ,EAAAlY,EAAAwZ,oBAGAxZ,IAIAzB,EAAAC,QAAA,SAAAoE,EAAAqoB,GA+BA,MAAAA,IACAA,EAAAja,KAAAuC,MAAA5C,KAAAsa,MAAA,MAYA,IANA,IAAA1pB,EAAA,CAAAopB,EAAAC,UAAAD,EAAAM,IAAAN,EAAAO,KAAAP,EAAAS,gBAAAT,EAAAU,SAAAV,EAAAW,UAAAX,EAAAc,eAAAd,EAAAgB,cAEAhB,EAAAwC,WAAAxC,EAAAuC,YAAAvC,EAAAsC,WAAAtC,EAAAoB,UAAApB,EAAAuB,WAAAvB,EAAAuD,YAAAvD,EAAAoD,WAAApD,EAAAgD,YAAAhD,EAAA+D,WAAA/D,EAAAwD,eAAAxD,EAAA4D,cAAA5D,EAAA2D,SAAA3D,EAAA8D,aAAA9D,EAAAyD,iBAAAzD,EAAA0D,UAAA1D,EAAA6D,2BAAA7D,EAAAiE,eAAAjE,EAAAmE,SAAAnE,EAAAkD,aAAAlD,EAAAmD,aAAAnD,EAAAoE,UAAApE,EAAAqE,aAAArE,EAAAiD,YAAAjD,EAAAuE,WAAAvE,EAAAwE,cAAAxE,EAAAqD,YAAArD,EAAA6C,OAAA7C,EAAA8C,cAAA9C,EAAAyC,KAAAzC,EAAA2C,KAAA3C,EAAA4C,KAAA5C,EAAAsD,QAAAtD,EAAAyE,WAAAzE,EAAAsE,YAAAtE,EAAAkE,cAAAlE,EAAA+C,IAAA/C,EAAAmH,MAAAnH,EAAAiH,IAAAjH,EAAA0G,QAAA1G,EAAA2G,iBAAA3G,EAAA4E,aAAA5E,EAAA6G,qBAAA7G,EAAA0C,aAAA1C,EAAA8G,OAAA9G,EAAAwH,yBAAAxH,EAAAsH,wBAAAtH,EAAAuH,uBAAAvH,EAAAqH,sBAAArH,EAAAuG,SAAAvG,EAAAoH,YAEA/xB,EAAA6yB,OAAAC,OAAAV,GAEAxvB,EAAA3D,QAAA,CAIA,IAHA,IAAA8zB,EAAA,KACAC,EAAA,KAEA7uB,EAAA,EAAA4T,EAAAxW,EAAAtC,OAAAkF,EAAA4T,EAAA5T,IAAA,CACA,IAAAX,EAAAjC,EAAA4C,GAEAsK,EAAA7L,EAAA6L,MAAAjL,EAAAqnB,OAEApc,KACAskB,GAAAtkB,EAAA,GAAAxP,OAAA8zB,EAAA,GAAA9zB,UACA8zB,EAAAtkB,EACAukB,EAAAxvB,GAKA,IAAAwvB,GAAAA,EAAAlI,WAAA,IAAAkI,EAAAlI,SAAA/mB,MAAA/D,EAAA+yB,GACA,OAAA,EAGAnwB,EAAAA,EAAA8I,OAAAqnB,EAAA,GAAA9zB,QAEA8zB,EADAC,EAAA,KAIA,OAAAhiB,KAAAuC,MAAAvT,EAAAuyB,OAAA,IAAA5hB,KAAA,IAAAsa,IAAA,OAMAgI,6CAIA,SAAA10B,EAAA20B,EAAAC,GAIA50B,EAAAC,QAAA,SAAA40B,GAQA,IAAAC,EAAA,oBAAAvyB,OAAAA,OAAAqyB,EAAAja,EACAma,EAAAC,SAAAD,EAAAC,UAAA,GACA,IAAAA,EAAAD,EAAAC,SAIA,OAHAA,EAAAC,IAAAD,EAAAC,KAAA,GACAD,EAAAC,IAAAC,IAAAF,EAAAC,IAAAC,KAAA,IAEAF,EAAAC,IAAAC,IAAAJ,SAAAvzB,IAAAyzB,EAAAC,IAAAC,IAAAJ,GAAAK,aACA,OAAAH,EAAAC,IAAAC,IAAAJ,GAAAK,YAMA,GAHAH,EAAAC,IAAAC,IAAAJ,GAAAK,cASAC,+CAIA,SAAAn1B,EAAA20B,EAAAC,GAIA50B,EAAAC,QAAA,SAAAmQ,GAiBA,IAAA/L,EAAA+L,EAAA,GAGA,GAAA,SADAwkB,EAAA,6CAAAA,CAAA,sBAAA,OAEA,OAAAvwB,EAAA3D,OAuCA,IApCA,IAAAkF,EAAA,EACAwvB,EAAA,EAmCAxvB,EAAA,EAAAwvB,EAAA,EAAAxvB,EAAAvB,EAAA3D,OAAAkF,KACA,IAlCA,SAAAvB,EAAAuB,GACA,IACA4rB,EACA6D,EAFAC,EAAAjxB,EAAAkxB,WAAA3vB,GAGA,GAAA,OAAA0vB,GAAAA,GAAA,MAAA,CAGA,GAAAjxB,EAAA3D,QAAAkF,EAAA,EACA,MAAA,IAAAmN,MAAA,kDAGA,IADAye,EAAAntB,EAAAkxB,WAAA3vB,EAAA,IACA,OAAA,MAAA4rB,EACA,MAAA,IAAAze,MAAA,kDAEA,OAAA1O,EAAA4J,OAAArI,GAAAvB,EAAA4J,OAAArI,EAAA,GACA,GAAA,OAAA0vB,GAAAA,GAAA,MAAA,CAEA,GAAA,IAAA1vB,EACA,MAAA,IAAAmN,MAAA,kDAGA,IADAsiB,EAAAhxB,EAAAkxB,WAAA3vB,EAAA,IACA,OAAA,MAAAyvB,EAGA,MAAA,IAAAtiB,MAAA,kDAIA,OAAA,EAEA,OAAA1O,EAAA4J,OAAArI,GAIA4vB,CAAAnxB,EAAAuB,IAOAwvB,IAGA,OAAAA,IAMAK,+CAIA,SAAAz1B,GAIAA,EAAAC,QAAA,SAAAy1B,GAsBA,IAAAlC,EAAA,CAAA,IAAA,KAAA,KAAA,KAAA,KAAA,KAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,SAAA,SAAA,KAAA5pB,KAAA,IAGA,OAAA,iBAAA8rB,GAAA,iBAAAA,IAAA,IAAAlC,EAAA9b,QAAAge,EAAA/vB,OAAA,MAAA,KAAA+vB,IAAA/jB,MAAA+jB,MASAC,EAAA,GAGA,SAAAf,EAAAgB,GAEA,IAAAC,EAAAF,EAAAC,GACA,QAAAt0B,IAAAu0B,EACA,OAAAA,EAAA51B,QAGA,IAAAD,EAAA21B,EAAAC,GAAA,CAGA31B,QAAA,IAOA,OAHAqe,EAAAsX,GAAA51B,EAAAA,EAAAC,QAAA20B,GAGA50B,EAAAC,QAOA20B,EAAA/uB,EAAA,SAAA7F,GACA,IAAA81B,EAAA91B,GAAAA,EAAA+1B,WACA,WAAA,OAAA/1B,EAAA,SACA,WAAA,OAAAA,GAEA,OADA40B,EAAAzb,EAAA2c,EAAA,CAAAnxB,EAAAmxB,IACAA,GAOAlB,EAAAzb,EAAA,SAAAlZ,EAAA+1B,GACA,IAAA,IAAAtsB,KAAAssB,EACApB,EAAA1a,EAAA8b,EAAAtsB,KAAAkrB,EAAA1a,EAAAja,EAAAyJ,IACA4qB,OAAA2B,eAAAh2B,EAAAyJ,EAAA,CAAAwsB,YAAA,EAAAC,IAAAH,EAAAtsB,MAQAkrB,EAAAja,EAAA,WACA,GAAA,iBAAAyb,WAAA,OAAAA,WACA,IACA,OAAA31B,MAAA,IAAA41B,SAAA,cAAA,GACA,MAAA7pB,GACA,GAAA,iBAAAjK,OAAA,OAAAA,QALA,GAYAqyB,EAAA1a,EAAA,SAAApO,EAAAhH,GAAA,OAAAwvB,OAAAtrB,UAAAgN,eAAA/T,KAAA6J,EAAAhH,KAMA8vB,EAAA9Y,EAAA,SAAA7b,GACA,oBAAAq2B,QAAAA,OAAAC,aACAjC,OAAA2B,eAAAh2B,EAAAq2B,OAAAC,YAAA,CAAA5sB,MAAA,WAEA2qB,OAAA2B,eAAAh2B,EAAA,aAAA,CAAA0J,OAAA,MAKA,IAKA,IAAA6sB,EAAA5B,EAAA,gDACA6B,EAAA7B,EAAA/uB,EAAA2wB,GACAE,EAAA9B,EAAA,kDACA+B,EAAA/B,EAAA/uB,EAAA6wB,GACAE,EAAAhC,EAAA,oDACAiC,EAAAjC,EAAA/uB,EAAA+wB,GACAE,EAAAlC,EAAA,gDACAmC,EAAAnC,EAAA/uB,EAAAixB,GAgBA12B,EAAAC,QAAA,EAAAT,kBAAA,CACA8c,QAAA,CAIAsa,aAAA,CAAA,UAAA,WAQAC,SAAA,SAAAC,EAAAtzB,GACA,IAAAuzB,EAAAD,EAAAvtB,MAEA,OADA/F,OAAA,IAAAA,EAAAA,EAAA,EACA,OAAAszB,EAAAE,YACA,IAAAF,EAAAE,MAAAxzB,IACA,CACAyzB,KAAAF,EACAG,UAAAH,EAAAhqB,OAAAgqB,EAAA/pB,YAAA,KAAA,GACAhB,KAAA8qB,EAAAE,MAAAxzB,GAAAwI,KAAA,KACAxE,KAAAsvB,EAAAE,MAAAxzB,GAAAgE,OAYAgF,SAAA,SAAA2qB,GACA,IAAA3qB,EAAA,GACAnM,KAAAwR,QAAAslB,KACAA,EAAA,CAAAA,IAEA,IAAA,IAAA3xB,EAAA,EAAAA,EAAA2xB,EAAA72B,OAAAkF,IACAgH,EAAA7B,KAAA,UAAAwsB,EAAA3xB,GAAA,MAEA,OAAAgH,EAAAhD,QAQA4tB,gBAAA,SAAA10B,GACA,OAAArC,KAAAg3B,SAAA30B,EAAArC,KAAAu2B,eASAS,SAAA,SAAA30B,EAAAE,GACA,IAAA00B,GAAA,EACA,iBAAA10B,IACAA,EAAA,CAAAA,IAEA,IAAArC,EAAAP,EAAAQ,KAAAkC,EAAAV,KAAA,aACAu1B,EAAA,GACArb,EAAA3b,EAAAyb,gBAoBA,OAnBAtZ,EAAAlB,QAAA0a,GACAlc,EAAAyC,KAAAyZ,EAAAxZ,EAAAlB,MAAA,SAAAgC,EAAAg0B,GACAD,EAAA5sB,KAAA6sB,KAGA90B,EAAAlB,QAAAjB,EAAAG,SAAAkC,OACA20B,EAAA5sB,KAAApK,EAAAG,SAAAkC,MAAAF,EAAAlB,OAEAxB,EAAAyC,KAAA80B,EAAA,SAAA/zB,EAAAi0B,GACA,GAAA,sBAAAA,EAEA,IADA,IAAAC,EAAAD,EAAAj4B,kBACAgG,EAAA,EAAAA,EAAAkyB,EAAAp3B,OAAAkF,IACA,IAAA,IAAAxF,EAAAkH,QAAAwwB,EAAAlyB,GAAA,GAAA5C,GAEA,QADA00B,GAAA,KAMAA,GASAK,OAAA,SAAA3nB,GACA,OAAAqmB,GAAAA,CAAArmB,IAUA4nB,QAAA,SAAAlsB,EAAAhJ,EAAA6G,GACA,OAAAlJ,KAAA+2B,gBAAA10B,IAAArC,KAAAw3B,WAAAtuB,GACAuuB,WAAAvuB,GACAlJ,KAAAwR,QAAAtI,GACAuuB,WAAAvuB,EAAAjJ,QACA,SAAAoC,EAAA8E,KACAswB,WAAAzlB,KAAAuC,MAAAvU,KAAAw2B,SAAAn0B,GAAAsJ,OAEA8rB,WAAAz3B,KAAAs3B,OAAApuB,KASAwuB,qBAAA,SAAA9qB,EAAAvK,GACA,IAAA40B,OAAAp2B,EAUA,OATAlB,EAAAyC,KAAAzC,EAAAO,UAAAwC,YAAAL,GAAA,SAAA4G,EAAA1G,GACA,sBAAA0G,GACAtJ,EAAAyC,KAAAG,EAAA,SAAA4C,EAAA+D,GACAA,EAAA,KAAA0D,IACAqqB,EAAA/tB,OAKA+tB,GASAU,UAAA,SAAAzuB,EAAA1E,GACA,IAMAozB,EANAC,GAAA,EACArf,EAAA,IAAAjF,cACA,MAAA,iBAAArK,QAAA,IAAA1E,EACA0E,GAEA,iBAAA1E,IAGAA,OADA3D,KADA+2B,EAAA53B,KAAA03B,qBAAA,aAAAlzB,IAEAozB,EAAA,GAAA,GAEA,MAIAC,EADA,MAAArzB,EACAxE,KAAA83B,UAAA5uB,IAEA2uB,EAAArf,EAAA5C,UAAA1M,EAAA1E,cACAmN,MAAA6G,EAAAgD,WAAAqc,EAAArzB,KAAA0E,GACA8I,KAAAC,MAAA4lB,EAAAtc,UAAA,OAiBAwc,aAAA,SAAA73B,EAAAgJ,EAAA7G,EAAAqC,EAAAszB,GACA,IAAAC,EAAAj4B,KAAA23B,UAAAjzB,GACA,IAAAuzB,EAAA,CACA,IAAA1lB,EAAAvS,KAAAk4B,iBAAAh4B,EAAAmC,EAAAqC,GACA,QAAA7D,IAAA0R,EACA,OAAA,EAEA0lB,EAAAj4B,KAAA23B,UAAAz3B,EAAA0G,aAAA2L,GAAAA,GAEA,IAAAslB,EAAA73B,KAAA23B,UAAAzuB,EAAA7G,GACA,IAAA,IAAAw1B,EACA,OAAA,EAEA,OAAAG,GACA,IAAA,IACA,OAAAH,EAAAI,EACA,IAAA,KACA,OAAAJ,GAAAI,EACA,IAAA,KACA,IAAA,MACA,OAAAJ,IAAAI,EACA,IAAA,IACA,OAAAA,EAAAJ,EACA,IAAA,KACA,OAAAI,GAAAJ,EACA,QACA,MAAA,IAAAvlB,MAAA,2BAUA+E,UAAA,SAAAnO,EAAA1E,GAEA,OADA,IAAA+O,eACA8D,UAAAnO,EAAA1E,IAWAszB,UAAA,SAAAtsB,EAAAygB,GACA,OAAAmK,GAAAA,CAAA5qB,EAAAygB,IAUAuL,WAAA,SAAAW,GACA,OAAA7B,GAAAA,CAAA6B,IASA3mB,QAAA,SAAA4mB,GACA,MAAA,mBAAAvE,OAAAtrB,UAAAqJ,SAAApQ,KAAA42B,IAWAC,UAAA,SAAAta,EAAAua,GACA,OAAApC,GAAAA,CAAAnY,EAAAua,IASApb,YAAA,SAAAa,EAAAua,GACA,SAAAt4B,KAAAwR,QAAAuM,KAAA/d,KAAAwR,QAAA8mB,MAGAva,EAAA9d,SAAAq4B,EAAAr4B,QAGAN,EAAAie,cAAA5d,KAAAq4B,UAAAta,EAAAua,MAUAJ,iBAAA,SAAAh4B,EAAAmC,EAAAlB,GACA,IAEAX,EAIA+3B,EANAC,EAAAt4B,EAAAkH,WAAAjG,GAWA,YAVAN,IAAA23B,EAAA,IAAAt4B,EAAAG,SAAAiG,aACA9F,EAAA,OACA,WAAAg4B,EAAA,GAAAC,SAAA,WAAAD,EAAA,GAAAC,SAAA,aAAAD,EAAA,GAAArxB,MAAA,UAAAqxB,EAAA,GAAArxB,OACA3G,EAAA,SAEA+3B,EAAA,8BACAC,EAAA9nB,IAAA6nB,GAAA7nB,IAAAlQ,EAAA+3B,EAAA,IAAAl2B,EAAAlB,MAAAZ,GAAAC,EAAA+3B,EAAA,IAAAl2B,EAAAlB,KAAA,WACAxB,EAAA0C,GAAAJ,WAGAu2B,EAAA,IAQAlb,mBAAA,SAAAvK,GACA,IAEA2lB,EAFAC,EAAA,CAAA,4CAOA,MANA,iBAAA5lB,IACA2lB,EAAA3lB,EAAA6lB,aAAAnpB,MAAA,yBACAzP,KAAAwR,QAAAknB,KACAC,EAAA,CAAAD,EAAA,MAGAC,GAQAE,aAAA,SAAAj1B,GACA,OAAAA,EAAAC,QAAA,sCAAA,SAQAsY,kBAAA,SAAAhb,GACA,IAAA23B,EAAA33B,EAAA+B,MAAA,OAEA,OADA,IAAA41B,EAAA74B,QAAA64B,EAAAxuB,KAAA,IACA,IAAAjF,OAAA,IAAAyzB,EAAApuB,IAAA,SAAAyX,GACA,OAAAhjB,kBAAA8c,QAAA4c,aAAA1W,KACAhZ,KAAA,iBAAA,MASA+S,WAAA,SAAA3Z,EAAAyZ,GACA,IAAA+c,EAAA,CACA55B,kBAAA6c,EAAA7c,mBAAA,GACA0d,wBAAAb,EAAAa,yBAAA,IAEA,IAAA,IAAA5T,KAAA8vB,EACA,IAAAA,EAAA9vB,GAAAhJ,cAGA,IAAAsC,EAAA0G,KACA1G,EAAA0G,GAAA,IAEA1G,EAAA0G,GAAA1G,EAAA0G,GAAA3G,OAAAy2B,EAAA9vB,KAEA,OAAA1G,GAQAob,OAAA,SAAAhO,GACA,OAAAhQ,EAAA,UAAA6L,KAAAmE,GAAAT,QASA8pB,gBAAA,SAAA94B,EAAAiB,GASA,IARA,IAAA83B,EAAA93B,EAAA0C,QAAA,cAAA,QACAq1B,EAAA,CAEAD,EAEAA,EAAA,KAEAA,EAAAp1B,QAAA,iBAAA,SACAsB,EAAA,EAAAA,EAAA+zB,EAAAj5B,OAAAkF,IAAA,CACA,IAAAg0B,EAAAj5B,EAAAkH,WAAA8xB,EAAA/zB,IACA,GAAA,EAAAg0B,EAAAl5B,OACA,OAAAk5B,EAGA,OAAAx5B,EAAA,OAYAyH,WAAA,SAAAlH,EAAAiB,GAEA,IAAAg4B,EAAAj5B,EAAAkH,WAAAjG,GACA,GAAA,EAAAg4B,EAAAl5B,OACA,OAAAk5B,EAMA,IAFA,IACA5nB,EAAApQ,EAAA+B,MADA,KAEAiC,EAAAoM,EAAAtR,OAAA,EAAAkF,EAAAA,IAAA,CAEA,IADA,IAAAi0B,EAAA,GACAhe,EAAA,EAAAA,EAAAjW,EAAAiW,IACAge,EAAA9uB,KAAAiH,EAAA6J,IAGA,GAAA,GADA+d,EAAAn5B,KAAAg5B,gBAAA94B,EAAAk5B,EAAAjwB,KAPA,OAQAlJ,OACA,OAAAk5B,EAGA,OAAAx5B,EAAA,OASA05B,iBAAA,SAAAn5B,EAAAmC,GACA,OAAA,IAAAA,EAAAlB,KAAA8V,QAAA,MACA/W,EAAAkH,WAAA/E,EAAAlB,MAAAuJ,IAAA,SAAAvF,EAAA4G,GACA,OAAA7L,EAAA0G,aAAAmF,KACA2pB,MAEAx1B,EAAA0G,aAAAvE,OAjiEA,GCWA1C,EAAAC,QAAA,EAAAT,kBAAA,CAEA8c,QAAA,CAQAqd,WAAA,SAAApwB,GAEA,IAAAqwB,EAAA,CACAC,OAAA,CACA,UACA,QACA,cACA,UACA,SACA,SACA,SACA,SACA,SACA,WACA,cACA,YACA,QACA,aACA,QACA,UACA,QACA,gBACA,WACA,SACA,WACA,WACA,WACA,SACA,eACA,OACA,UACA,WACA,SACA,WACA,QACA,aACA,OACA,SACA,aACA,SACA,SACA,SACA,SACA,UACA,YACA,WACA,UACA,WACA,SACA,aACA,cACA,aACA,WACA,UACA,QACA,YAEAC,QAAA,CACA,OACA,YACA,WACA,UACA,YACA,yBACA,sBACA,oBACA,kBACA,qBACA,oBACA,yBACA,kBACA,qBACA,qBACA,oBACA,oBACA,QACA,WACA,WACA,QACA,iBACA,WACA,QACA,SACA,eACA,YACA,SACA,QACA,gBACA,eACA,SACA,UACA,UACA,SACA,UACA,YACA,aACA,UACA,SACA,UACA,eACA,SACA,eACA,SACA,UACA,WACA,WACA,WACA,cACA,YACA,YACA,UACA,YACA,aACA,UACA,aACA,YACA,YACA,SACA,UACA,SACA,aACA,uBACA,eACA,kBACA,qBACA,oBACA,gBACA,oBACA,kBACA,SACA,UACA,UACA,SACA,sBACA,sBACA,aACA,SACA,OACA,cACA,gBACA,SACA,UACA,SACA,UACA,aACA,YACA,WACA,YACA,SACA,aACA,cACA,WACA,UACA,YACA,aACA,WACA,aACA,SACA,WACA,UACA,OACA,UACA,sBACA,sBACA,yBACA,UACA,SACA,cACA,aACA,UACA,iBACA,gBACA,cACA,cACA,cACA,eACA,SACA,SACA,WACA,aACA,eACA,WACA,WACA,gBACA,YACA,eACA,WACA,QACA,gBACA,WACA,WACA,WACA,YACA,aACA,gBACA,cACA,QACA,cACA,UACA,UACA,UACA,YACA,aACA,WACA,UACA,eAEAC,WAAA,CACA,QACA,QACA,iBACA,YACA,SACA,UACA,SACA,UACA,aACA,QACA,UAEAC,OAAA,CACA,gBAEAC,KAAA,CACA,OACA,SACA,QACA,SACA,QACA,SACA,WACA,UACA,UACA,OACA,UACA,SACA,UACA,SACA,aACA,YACA,UACA,WACA,QACA,OACA,QACA,WACA,OACA,SACA,SACA,cACA,YACA,OACA,UACA,UACA,WACA,YACA,QACA,YACA,UACA,UACA,YACA,WACA,UACA,cACA,eACA,UACA,SACA,QACA,UACA,WACA,SACA,SACA,UACA,eACA,cACA,OACA,OACA,aACA,YACA,YACA,QACA,YACA,UACA,SACA,WACA,YACA,QACA,WACA,YACA,SACA,WACA,UACA,SACA,UACA,QACA,cACA,SACA,WACA,YACA,cACA,UACA,gBACA,WAEAC,SAAA,CACA,SACA,UACA,SACA,aACA,QACA,UACA,YACA,gBACA,YACA,WAEAC,UAAA,CACA,WACA,WACA,cACA,SACA,SACA,QACA,SACA,WACA,YACA,YACA,QACA,UAEAC,OAAA,CACA,YACA,UACA,SACA,WACA,SACA,aACA,WACA,YACA,WACA,WACA,WACA,aACA,SACA,YACA,WACA,WACA,cACA,WACA,SACA,cACA,OACA,SACA,YACA,SACA,aACA,SACA,QACA,YACA,QACA,SACA,SACA,OACA,QACA,YACA,SACA,OACA,OACA,SACA,aACA,WACA,aACA,SACA,QACA,YACA,UACA,SACA,WACA,QACA,UACA,SACA,UACA,YACA,SACA,SACA,aACA,UAEAC,OAAA,CACA,eACA,SACA,YACA,QACA,SACA,YACA,OACA,WACA,YACA,UACA,WAEAC,QAAA,CACA,OACA,WACA,UACA,QACA,SACA,QACA,YACA,UACA,OACA,WACA,YACA,UACA,cACA,OACA,WACA,WACA,aACA,SACA,YACA,SACA,YACA,SACA,QACA,OACA,UACA,SACA,YACA,QACA,WACA,UACA,eACA,YACA,SACA,SACA,SACA,YACA,OACA,UAEAxP,IAAA,CACA,KAIAyP,EAAAhxB,EAAAhG,MAAA,IAAA,GACAi3B,EAAAD,EAAA,GAAAvsB,cACAysB,EAAA,GAKA,OAJAF,EAAA,KACAE,EAAAF,EAAA,GAAAvsB,eAGAwsB,KAAAZ,IAAA,IAAAA,EAAAY,GAAAl6B,SAAA,IAAAs5B,EAAAY,GAAAljB,QAAAmjB,QC/cAz6B,EAAAC,QAAA,EAAAT,kBAAA,CAEA+N,QAAA,CAEA+O,QAAA9c,kBAAA8c,QAEAoe,cAAA,EAQAC,UAAA,WACA,OAAA,GASAC,KAAA,WACA,OAAA,GASAC,SAAA,WACA,OAAA,GAMAC,OAAA,SAAAvxB,EAAA7G,GACA,OAAA1C,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAOAq4B,SAAA,SAAAxxB,EAAA7G,GACA,OAAA1C,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,IAQAs4B,aAAA,SAAAzxB,EAAA7G,EAAAqC,GACA,IAAAxE,EAAAF,KACAyD,GAAA,EACAm3B,EAAA56B,KAeA,OAbAL,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAvC,GACA,IAAA2P,EAAApT,kBAAA8c,QAAAic,iBACA0C,EAAAv4B,EAAAO,GAEAa,EAAAA,QACA5C,IAAA0R,GACA5S,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAtB,EACA06B,EAAAh0B,aAAA2L,GACAA,GAAA,MAIA9O,GACA9D,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAUAw4B,gBAAA,SAAA3xB,EAAA7G,EAAAqC,GACA,IAAAxE,EAAAF,KACAyD,GAAA,EACAm3B,EAAA56B,KAeA,OAbAL,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAvC,GACA,IAAA2P,EAAApT,kBAAA8c,QAAAic,iBACA0C,EAAAv4B,EAAAO,GAEAa,EAAAA,QACA5C,IAAA0R,GACA5S,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAtB,EACA06B,EAAAh0B,aAAA2L,GACAA,GAAA,MAIA9O,GACA9D,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAUAy4B,gBAAA,SAAA5xB,EAAA7G,EAAAqC,GACA,IAAAxE,EAAAF,KACAyD,GAAA,EACAm3B,EAAA56B,KAeA,OAbAL,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAvC,GACA,IAAA2P,EAAApT,kBAAA8c,QAAAic,iBACA0C,EAAAv4B,EAAAO,GAEAa,EAAAA,QACA5C,IAAA0R,IACA5S,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAtB,EACA06B,EAAAh0B,aAAA2L,GACAA,GAAA,MAIA9O,GACA9D,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAUA04B,mBAAA,SAAA7xB,EAAA7G,EAAAqC,GACA,IAAAxE,EAAAF,KACAyD,GAAA,EACAm3B,EAAA56B,KAeA,OAbAL,EAAAyC,KAAAsC,EAAA,SAAAS,EAAAvC,GACA,IAAA2P,EAAApT,kBAAA8c,QAAAic,iBACA0C,EAAAv4B,EAAAO,GAEAa,EAAAA,SACA5C,IAAA0R,IACA5S,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAtB,EACA06B,EAAAh0B,aAAA2L,GACAA,GAAA,OAIA9O,GACA9D,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAUA24B,WAAA,SAAA9xB,EAAA7G,EAAAqC,GAEA,IAAA6N,EAAApT,kBAAA8c,QAAAic,iBACAl4B,KAAAqC,EAAAqC,EAAA,IAGA,QAAA7D,IAAA0R,EAAA,CACA,IAAAnR,EAAA0M,OAAA9N,KAAA4G,aAAA2L,IACA,QAAA,IAAAnR,EAAA,CACA,IAAAjB,EAAAuE,EAAAQ,MAAA,GACA,IAAA,IAAAvF,EAAAkH,QAAAzF,EAAAjB,GACA,OAAAR,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAxB,KAAAkJ,EAAA7G,GAAA,IAMA,OAAA,GASA44B,eAAA,SAAA/xB,EAAA7G,EAAAqC,GAEA,IAAA6N,EAAApT,kBAAA8c,QAAAic,iBACAl4B,KAAAqC,EAAAqC,EAAA,IAGA,QAAA7D,IAAA0R,EAAA,CACA,IAAAnR,EAAA0M,OAAA9N,KAAA4G,aAAA2L,IACA,QAAA,IAAAnR,EAAA,CACA,IAAAjB,EAAAuE,EAAAQ,MAAA,GACA,IAAA,IAAAvF,EAAAkH,QAAAzF,EAAAjB,GACA,OAAA,GAKA,OAAAR,EAAAO,UAAAgN,QAAAzJ,SAAAjC,KACAxB,KAAAkJ,EAAA7G,GAAA,IAUA64B,UAAA,SAAAhyB,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA+N,QAAAiuB,KAAA35B,KAAAxB,KAAAkJ,EAAA7G,EAAAqC,IAQAy2B,KAAA,SAAAjyB,EAAA7G,EAAAqC,GAEA,IAAA6N,EAAApT,kBAAA8c,QAAAic,iBACAl4B,KAAAqC,EAAAqC,EAAA,IAGA,YAAA7D,IAAA0R,GACAzE,OAAA5E,KAAA4E,OAAA9N,KAAA4G,aAAA2L,KAcA6oB,QAAA,SAAAlyB,EAAA7G,EAAAqC,GACA,QAAA,IAAAA,EAAA,GACA,OAAA,EAMA,IAJA,IAAAgF,EAAA1J,KAAA0J,WACAutB,GAAA,EACAlb,EAAA5c,kBAAA8c,QAAAE,kBAAAzX,EAAA,IAEAS,EAAA,EAAAA,EAAAuE,EAAAzJ,OAAAkF,IAAA,CACA,IAEAk2B,EAFAC,EAAA5xB,EAAAvE,GAAAhE,KACAm6B,EAAA7rB,MAAAsM,KACAsf,EAAAl8B,kBAAA+N,QAAAiuB,KAAA35B,KAAAxB,KAAAkJ,EAAA7G,EAAA,CAAAi5B,IACArE,EAAAA,GAAAoE,GAIA,OAAApE,GAWAsE,SAAA,SAAAryB,EAAA7G,EAAAqC,GACA,QAAA,IAAAA,EAAA,GACA,OAAA,EAOA,IAJA,IAAAgF,EAAA1J,KAAA0J,WACAutB,GAAA,EACAlb,EAAA5c,kBAAA8c,QAAAE,kBAAAzX,EAAA,IAEAS,EAAA,EAAAA,EAAAuE,EAAAzJ,OAAAkF,IAAA,CACA,IAEAk2B,EAFAC,EAAA5xB,EAAAvE,GAAAhE,KACAm6B,IAAAj5B,EAAAlB,MAAAm6B,EAAA7rB,MAAAsM,KACAsf,EAAAl8B,kBAAA+N,QAAAiuB,KAAA35B,KAAAxB,KAAAkJ,EAAA7G,EAAA,CAAAi5B,IACArE,EAAAA,GAAAoE,GAIA,OAAApE,GASAuE,UAAA,SAAAtyB,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA+N,QAAAiuB,KAAA35B,KAAAxB,KAAAkJ,EAAA7G,EAAAqC,IASA+2B,SAAA,SAAAvyB,GAEA,OADA,IAAA7D,OAAA,wBAAA,KACA8I,KAAAjF,IASAjE,MAAA,SAAAiE,EAAA7G,GACA,OAAA,IAAAA,EAAAlB,KAAA8V,QAAA,OAAA,IAAA5U,EAAAlB,KAAA8V,QAAA,MAIA9X,kBAAA8c,QAAAzK,QAAAtI,IAQAwyB,QAAA,SAAAxyB,GAEA,OADA,IAAA7D,OAAA,yBAAA,KACA8I,KAAAjF,IAQAyyB,QAAA,SAAAzyB,GAEA,OADA,IAAA7D,OAAA,eAAA,KACA8I,KAAAjF,IAMA0yB,QAAA,SAAA1yB,EAAA7G,GACA,OAAA1C,EAAAO,UAAAgN,QAAAtF,OAAApG,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAQAyL,OAAA,SAAA5E,GACA,MAAA,iBAAAA,GAMA2yB,OAAA,SAAA3yB,EAAA7G,EAAAqC,GACA,OACA/E,EAAAO,UAAAgN,QAAAtF,OAAApG,KAAAxB,KAAAkJ,EAAA7G,GAAA,IACA6G,EAAAjJ,SAAA8U,SAAArQ,EAAA,KAOAo3B,cAAA,SAAA5yB,EAAA7G,EAAAqC,GACA,OAAA/E,EAAAO,UAAAgN,QAAAtF,OAAApG,KAAAxB,KAAAkJ,EAAA7G,GAAA,IACA6G,EAAAjJ,QAAAw3B,WAAA/yB,EAAA,KAAAwE,EAAAjJ,QAAAw3B,WAAA/yB,EAAA,KAQAq3B,KAAA,SAAA7yB,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAAsb,QAAAv3B,KAAAqC,EAAA6G,KAAAuuB,WAAA/yB,EAAA,KAQAs3B,QAAA,SAAA9yB,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAAsb,QAAAv3B,KAAAqC,EAAA6G,IAAAuuB,WAAA/yB,EAAA,KACAvF,kBAAA8c,QAAAsb,QAAAv3B,KAAAqC,EAAA6G,IAAAuuB,WAAA/yB,EAAA,KAQAu3B,IAAA,SAAA/yB,EAAA7G,EAAAqC,GAGA,OAFAwE,EAAA/J,kBAAA8c,QAAAod,iBAAAr5B,KAAAqC,GAEAlD,kBAAA8c,QAAAsb,QAAAv3B,KAAAqC,EAAA6G,IAAAuuB,WAAA/yB,EAAA,KAQAw3B,IAAA,SAAAhzB,EAAA7G,EAAAqC,GAGA,OAFAwE,EAAA/J,kBAAA8c,QAAAod,iBAAAr5B,KAAAqC,GAEAlD,kBAAA8c,QAAAsb,QAAAv3B,KAAAqC,EAAA6G,IAAAuuB,WAAA/yB,EAAA,KAQAy3B,GAAA,SAAAjzB,EAAA7G,EAAAqC,GACA,GAAAvF,kBAAA8c,QAAAzK,QAAAtI,IACA/J,kBAAA8c,QAAA+a,SAAA30B,EAAA,SACA,CACA,IAAAqxB,EAAAv0B,kBAAA8c,QAAAoc,UAAAnvB,EAAAxE,GAEA,OAAA,IAAAmvB,OAAAuI,KAAA1I,GAAAzzB,OAGA,OAAA,IAAAyE,EAAAuS,QAAA/N,EAAA0I,aAQAyqB,MAAA,SAAAnzB,EAAA7G,EAAAqC,GACA,OAAA,IAAAA,EAAAuS,QAAA/N,EAAA0I,aAQA0qB,GAAA,SAAApzB,GACA,MAAA,gIAAAiF,KAAAjF,IACA,q2BAAAiF,KAAAjF,IAMAqzB,MAAA,SAAArzB,EAAA7G,GACA,OAAA1C,EAAAO,UAAAgN,QAAA1F,MAAAhG,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAMAm6B,IAAA,SAAAtzB,EAAA7G,GACA,OAAA1C,EAAAO,UAAAgN,QAAAzF,IAAAjG,KAAAxB,KAAAkJ,EAAA7G,GAAA,IAQAo6B,KAAA,SAAAvzB,EAAA7G,GACA,QAAAP,OAAA26B,MAAA36B,OAAA46B,YAAA56B,OAAA66B,UAAA76B,OAAA86B,OAGA,UAAAv6B,GACA,EAAAA,EAAAs0B,MAAA12B,QAUA48B,MAAA,SAAA3zB,EAAA7G,EAAAqC,GACA,KAAA5C,OAAA26B,MAAA36B,OAAA46B,YAAA56B,OAAA66B,UAAA76B,OAAA86B,MACA,OAAA,EAEA,IAAAE,EAAAn9B,EAAA+K,IAAAhG,EAAA,SAAAq4B,GACA,OAAAA,EAAApvB,gBAGA6oB,EAAAr3B,kBAAA8c,QAAAua,SAAAn0B,GACA,OAAA,IAAAm0B,IAAA,IAAAsG,EAAA7lB,QAAAuf,EAAAK,UAAAlpB,gBAQAqvB,UAAA,SAAA9zB,EAAA7G,EAAAqC,GACA,KAAA5C,OAAA26B,MAAA36B,OAAA46B,YAAA56B,OAAA66B,UAAA76B,OAAA86B,MACA,OAAA,EAEA,IAAAE,EAAAn9B,EAAA+K,IAAAhG,EAAA,SAAAq4B,GACA,OAAAA,EAAApvB,gBAGA6oB,EAAAr3B,kBAAA8c,QAAAua,SAAAn0B,GAEA,OAAA,IAAAm0B,IAGA,IAAAsG,EAAA7lB,QAAAuf,EAAArvB,KAAAwG,gBAMAsvB,MAAA,SAAA/zB,EAAA7G,GACA,OAAAlD,kBAAA+N,QAAA2vB,MAAAr7B,KAAAxB,KAAAkJ,EAAA7G,EAAA,CACA,MAAA,MAAA,MAAA,MAAA,MAAA,UASA66B,WAAA,SAAAh0B,MAAA7G,QAAAqC,OAAAonB,UACA,KAAAhqB,OAAA26B,MAAA36B,OAAA46B,YAAA56B,OAAA66B,UAAA76B,OAAA86B,MACA,OAAA,EAEA,GAAA,OAAAv6B,QAAAs0B,YAAA,IAAAt0B,QAAAs0B,MAAA,GACA,OAAA,EAGA,IAAAwG,GAAA,IAAAT,WAwBA,OAvBAS,GAAAC,OAAA,WACA,IAAAC,IAAA,IAAAJ,MACAI,IAAAD,OAAA,WACA,IAAAE,OAAA7F,WAAA4F,IAAAE,eACAC,MAAA/F,WAAA4F,IAAAI,cACAC,MAAAF,MAAAF,OACAK,SAAAj5B,OAAA,OAAA+yB,WAAA/yB,OAAA,QAAA84B,QACA94B,OAAA,WAAA+yB,WAAA/yB,OAAA,WAAA84B,OACA94B,OAAA,WAAA+yB,WAAA/yB,OAAA,WAAA84B,OACA94B,OAAA,QAAA+yB,WAAA/yB,OAAA,UAAA44B,QACA54B,OAAA,YAAA+yB,WAAA/yB,OAAA,YAAA44B,QACA54B,OAAA,YAAA+yB,WAAA/yB,OAAA,YAAA44B,QACA54B,OAAA,OAAAg5B,QAAAjG,WAAAmG,KAAAl5B,OAAA,QAEAonB,UAAA6R,WAEAN,IAAAQ,QAAA,WACA/R,UAAA,IAEAuR,IAAAS,IAAAX,GAAAn8B,QAEAm8B,GAAAY,cAAA17B,QAAAs0B,MAAA,IAEA,WAQAqH,MAAA,SAAA90B,GACA,MAAA,iBAAAA,GAIA,IAAA7D,OAAA,oBAAA,KACA8I,KAAAjF,IASA+0B,SAAA,SAAA/0B,GACA,MAAA,iBAAAA,GAGA,IAAA7D,OAAA,uBAAA,KACA8I,KAAAjF,IAQAg1B,UAAA,SAAAh1B,GACA,MAAA,iBAAAA,GAGA,IAAA7D,OAAA,yBAAA,KACA8I,KAAAjF,IAQAi1B,MAAA,SAAAj1B,EAAA7G,EAAAqC,GACA,IAAA05B,EAAA,CAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAEAC,EAAA,IAAAh5B,OAAA,mDACAi5B,EAAA55B,EAAA,GAAA+K,MAAA4uB,GACA,GAAA,OAAAC,EACA,OAAA,EAGA,IAAAC,EAAA,GACA,QAAA19B,IAAAy9B,EAAA,GAAA,CACAC,EAAAD,EAAA,GAAAp7B,MAAA,IACA,IAAA,IAAAiC,EAAA,EAAAA,EAAAo5B,EAAAt+B,OAAAkF,EAAAA,IACA,IAAA,IAAAi5B,EAAAnnB,QAAAsnB,EAAAp5B,IACA,OAAA,EAKA,OADA,IAAAE,OAAA,OAAAi5B,EAAA,GAAA,KAAAC,EAAAp1B,QACAgF,KAAAjF,IAQAyI,KAAA,SAAAzI,GACA,OAAA,IAAA/J,kBAAA8c,QAAA6b,UAAA5uB,IAQAs1B,WAAA,SAAAt1B,EAAA7G,EAAAqC,GACA,OAAA,IAAAvF,kBAAA8c,QAAA0b,UAAAzuB,EAAAxE,EAAA,KAQA+5B,OAAA,SAAAv1B,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAA8b,aAAA/3B,KAAAkJ,EAAA7G,EAAAqC,EAAA,GAAA,MAQAg6B,cAAA,SAAAx1B,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAA8b,aAAA/3B,KAAAkJ,EAAA7G,EAAAqC,EAAA,GAAA,OAQAi6B,MAAA,SAAAz1B,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAA8b,aAAA/3B,KAAAkJ,EAAA7G,EAAAqC,EAAA,GAAA,MAQAk6B,aAAA,SAAA11B,EAAA7G,EAAAqC,GACA,OAAAvF,kBAAA8c,QAAA8b,aAAA/3B,KAAAkJ,EAAA7G,EAAAqC,EAAA,GAAA,OAOAm6B,SAAA,SAAA31B,GACA,OAAA/J,kBAAA8c,QAAAqd,WAAApwB,IAUA41B,KAAA,SAAA51B,GACA,IAAAlI,GAAA,EACA,IACAuc,KAAAwhB,MAAA71B,GACA,MAAA6C,GACA/K,GAAA,EAEA,OAAAA,GASAg+B,eAAA,SAAA91B,GACA,OAAA", "file": "jsvalidation.min.js", "sourcesContent": ["/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\nvar laravelValidation;\nlaravelValidation = {\n\n    implicitRules: ['Required','Confirmed'],\n\n    /**\n     * Initialize laravel validations.\n     */\n    init: function () {\n\n        // jquery-validation requires the field under validation to be present. We're adding a dummy hidden\n        // field so that any errors are not visible.\n        var constructor = $.fn.validate;\n        $.fn.validate = function( options ) {\n            var name = 'proengsoft_jsvalidation'; // must match the name defined in JsValidatorFactory.newFormRequestValidator\n            var $elm = $(this).find('input[name=\"' + name + '\"]');\n            if ($elm.length === 0) {\n                $('<input>').attr({type: 'hidden', name: name}).appendTo(this);\n            }\n\n            return constructor.apply(this, [options]);\n        };\n\n        // Disable class rules and attribute rules\n        $.validator.classRuleSettings = {};\n        $.validator.attributeRules = function () {};\n\n        $.validator.dataRules = this.arrayRules;\n        $.validator.prototype.arrayRulesCache = {};\n\n        // Register validations methods\n        this.setupValidations();\n    },\n\n    arrayRules: function(element) {\n\n        var rules = {},\n            validator = $.data( element.form, \"validator\"),\n            cache = validator.arrayRulesCache;\n\n        // Is not an Array\n        if (element.name.indexOf('[') === -1) {\n            return rules;\n        }\n\n        if (! (element.name in cache)) {\n            cache[element.name] = {};\n        }\n\n        $.each(validator.settings.rules, function(name, tmpRules) {\n            if (name in cache[element.name]) {\n                rules = laravelValidation.helpers.mergeRules(rules, cache[element.name][name]);\n            } else {\n                cache[element.name][name] = {};\n\n                var nameRegExp = laravelValidation.helpers.regexFromWildcard(name);\n                if (element.name.match(nameRegExp)) {\n                    var newRules = $.validator.normalizeRule(tmpRules) || {};\n                    cache[element.name][name] = newRules;\n\n                    rules = laravelValidation.helpers.mergeRules(rules, newRules);\n                }\n            }\n        });\n\n        return rules;\n    },\n\n    setupValidations: function () {\n\n        /**\n         * Get CSRF token.\n         *\n         * @param params\n         * @returns {string}\n         */\n        var getCsrfToken = function (params) {\n            return params[0][1][1];\n        };\n\n        /**\n         * Whether to validate all attributes.\n         *\n         * @param params\n         * @returns {boolean}\n         */\n        var isValidateAll = function (params) {\n            return params[0][1][2];\n        };\n\n        /**\n         * Determine whether the rule is implicit.\n         *\n         * @param params\n         * @returns {boolean}\n         */\n        var isImplicit = function (params) {\n            var implicit = false;\n            $.each(params, function (i, parameters) {\n                implicit = implicit || parameters[3];\n            });\n\n            return implicit;\n        };\n\n        /**\n         * Get form method from a validator instance.\n         *\n         * @param validator\n         * @returns {string}\n         */\n        var formMethod = function (validator) {\n            var formMethod = $(validator.currentForm).attr('method');\n            if ($(validator.currentForm).find('input[name=\"_method\"]').length) {\n                formMethod = $(validator.currentForm).find('input[name=\"_method\"]').val();\n            }\n\n            return formMethod;\n        };\n\n        /**\n         * Get AJAX parameters for remote requests.\n         *\n         * @param validator\n         * @param element\n         * @param params\n         * @param data\n         * @returns {object}\n         */\n        var ajaxOpts = function (validator, element, params, data) {\n            return {\n                mode: 'abort',\n                port: 'validate' + element.name,\n                dataType: 'json',\n                data: data,\n                context: validator.currentForm,\n                url: $(validator.currentForm).attr('action'),\n                type: formMethod(validator),\n                beforeSend: function (xhr) {\n                    var token = getCsrfToken(params);\n                    if (formMethod(validator) !== 'get' && token) {\n                        return xhr.setRequestHeader('X-XSRF-TOKEN', token);\n                    }\n                },\n            };\n        };\n\n        /**\n         * Validate a set of local JS based rules against an element.\n         *\n         * @param validator\n         * @param values\n         * @param element\n         * @param rules\n         * @returns {boolean}\n         */\n        var validateLocalRules = function (validator, values, element, rules) {\n            var validated = true,\n                previous = validator.previousValue(element);\n\n            $.each(rules, function (i, param) {\n                var implicit = param[3] || laravelValidation.implicitRules.indexOf(param[0]) !== -1;\n                var rule = param[0];\n                var message = param[2];\n\n                if (! implicit && validator.optional(element)) {\n                    validated = \"dependency-mismatch\";\n                    return false;\n                }\n\n                if (laravelValidation.methods[rule] !== undefined) {\n                    $.each(values, function(index, value) {\n                        validated = laravelValidation.methods[rule].call(validator, value, element, param[1], function(valid) {\n                            validator.settings.messages[element.name].laravelValidationRemote = previous.originalMessage;\n                            if (valid) {\n                                var submitted = validator.formSubmitted;\n                                validator.prepareElement(element);\n                                validator.formSubmitted = submitted;\n                                validator.successList.push(element);\n                                delete validator.invalid[element.name];\n                                validator.showErrors();\n                            } else {\n                                var errors = {};\n                                errors[ element.name ]\n                                    = previous.message\n                                    = typeof message === \"function\" ? message( value ) : message;\n                                validator.invalid[element.name] = true;\n                                validator.showErrors(errors);\n                            }\n                            validator.showErrors(validator.errorMap);\n                            previous.valid = valid;\n                        });\n\n                        // Break loop.\n                        if (validated === false) {\n                            return false;\n                        }\n                    });\n                } else {\n                    validated = false;\n                }\n\n                if (validated !== true) {\n                    if (!validator.settings.messages[element.name] ) {\n                        validator.settings.messages[element.name] = {};\n                    }\n\n                    validator.settings.messages[element.name].laravelValidation= message;\n\n                    return false;\n                }\n\n            });\n\n            return validated;\n        };\n\n        /**\n         * Create JQueryValidation check to validate Laravel rules.\n         */\n\n        $.validator.addMethod(\"laravelValidation\", function (value, element, params) {\n            var rules = [],\n                arrayRules = [];\n            $.each(params, function (i, param) {\n                // put Implicit rules in front\n                var isArrayRule = param[4].indexOf('[') !== -1;\n                if (param[3] || laravelValidation.implicitRules.indexOf(param[0]) !== -1) {\n                    isArrayRule ? arrayRules.unshift(param) : rules.unshift(param);\n                } else {\n                    isArrayRule ? arrayRules.push(param) : rules.push(param);\n                }\n            });\n\n            // Validate normal rules.\n            var localRulesResult = validateLocalRules(this, [value], element, rules);\n\n            // Validate items of the array using array rules.\n            var arrayValue = ! Array.isArray(value) ? [value] : value;\n            var arrayRulesResult = validateLocalRules(this, arrayValue, element, arrayRules);\n\n            return localRulesResult && arrayRulesResult;\n        }, '');\n\n\n        /**\n         * Create JQueryValidation check to validate Remote Laravel rules.\n         */\n        $.validator.addMethod(\"laravelValidationRemote\", function (value, element, params) {\n\n            if (! isImplicit(params) && this.optional( element )) {\n                return \"dependency-mismatch\";\n            }\n\n            var previous = this.previousValue( element ),\n                validator, data;\n\n            if (! this.settings.messages[ element.name ]) {\n                this.settings.messages[ element.name ] = {};\n            }\n            previous.originalMessage = this.settings.messages[ element.name ].laravelValidationRemote;\n            this.settings.messages[ element.name ].laravelValidationRemote = previous.message;\n\n            if (laravelValidation.helpers.arrayEquals(previous.old, value) || previous.old === value) {\n                return previous.valid;\n            }\n\n            previous.old = value;\n            validator = this;\n            this.startRequest( element );\n\n            data = $(validator.currentForm).serializeArray();\n            data.push({'name': '_jsvalidation', 'value': element.name});\n            data.push({'name': '_jsvalidation_validate_all', 'value': isValidateAll(params)});\n\n            $.ajax( ajaxOpts(validator, element, params, data) )\n                .always(function( response, textStatus ) {\n                    var errors, message, submitted, valid;\n\n                    if (textStatus === 'error') {\n                        valid = false;\n                        response = laravelValidation.helpers.parseErrorResponse(response);\n                    } else if (textStatus === 'success') {\n                        valid = response === true || response === \"true\";\n                    } else {\n                        return;\n                    }\n\n                    validator.settings.messages[ element.name ].laravelValidationRemote = previous.originalMessage;\n\n                    if ( valid ) {\n                        submitted = validator.formSubmitted;\n                        validator.prepareElement( element );\n                        validator.formSubmitted = submitted;\n                        validator.successList.push( element );\n                        delete validator.invalid[ element.name ];\n                        validator.showErrors();\n                    } else {\n                        errors = {};\n                        message = response || validator.defaultMessage( element, \"remote\" );\n                        errors[ element.name ]\n                            = previous.message\n                            = typeof message === \"function\" ? message( value ) : message[0];\n                        validator.invalid[ element.name ] = true;\n                        validator.showErrors( errors );\n                    }\n                    validator.showErrors(validator.errorMap);\n                    previous.valid = valid;\n                    validator.stopRequest( element, valid );\n                }\n            );\n            return \"pending\";\n        }, '');\n\n        /**\n         * Create JQueryValidation check to form requests.\n         */\n        $.validator.addMethod(\"laravelValidationFormRequest\", function (value, element, params) {\n\n            var validator = this,\n                previous = validator.previousValue(element);\n\n            var data = $(validator.currentForm).serializeArray();\n            data.push({name: '__proengsoft_form_request', value: 1}); // must match FormRequest.JS_VALIDATION_FIELD\n\n            // Skip AJAX if the value remains the same as a prior request.\n            if (JSON.stringify(previous.old) === JSON.stringify(data)) {\n                if (! previous.valid) {\n                    validator.showErrors(previous.errors || {});\n                }\n\n                return previous.valid;\n            }\n\n            previous.old = data;\n            this.startRequest( element );\n\n            $.ajax(ajaxOpts(validator, element, params, data))\n                .always(function( response, textStatus ) {\n                    var errors = {},\n                        valid = textStatus === 'success' && (response === true || response === 'true');\n\n                    if (valid) {\n                        validator.resetInternals();\n                        validator.toHide = validator.errorsFor( element );\n                    } else {\n                        $.each( response, function( fieldName, errorMessages ) {\n                            var errorElement = laravelValidation.helpers.findByName(validator, fieldName)[0];\n                            if (errorElement) {\n                                errors[errorElement.name] = laravelValidation.helpers.encode(errorMessages[0] || '');\n                            }\n                        });\n\n                        // Failed to find the error fields so mark the form as valid otherwise user\n                        // will be left in limbo with no visible error messages.\n                        if ($.isEmptyObject(errors)) {\n                            valid = true;\n                        }\n                    }\n\n                    previous.valid = valid;\n                    previous.errors = errors;\n                    validator.showErrors(errors);\n                    validator.stopRequest(element, valid);\n                });\n\n            return 'pending';\n        }, '');\n    }\n};\n\n$(function() {\n    laravelValidation.init();\n});\n", "/*!\n * jQuery Validation Plugin v1.21.0\n *\n * https://jqueryvalidation.org/\n *\n * Copyright (c) 2024 <PERSON><PERSON><PERSON>\n * Released under the MIT license\n */\n(function( factory ) {\n\tif ( typeof define === \"function\" && define.amd ) {\n\t\tdefine( [\"jquery\"], factory );\n\t} else if (typeof module === \"object\" && module.exports) {\n\t\tmodule.exports = factory( require( \"jquery\" ) );\n\t} else {\n\t\tfactory( jQuery );\n\t}\n}(function( $ ) {\n\n$.extend( $.fn, {\n\n\t// https://jqueryvalidation.org/validate/\n\tvalidate: function( options ) {\n\n\t\t// If nothing is selected, return nothing; can't chain anyway\n\t\tif ( !this.length ) {\n\t\t\tif ( options && options.debug && window.console ) {\n\t\t\t\tconsole.warn( \"Nothing selected, can't validate, returning nothing.\" );\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// Check if a validator for this form was already created\n\t\tvar validator = $.data( this[ 0 ], \"validator\" );\n\t\tif ( validator ) {\n\t\t\treturn validator;\n\t\t}\n\n\t\t// Add novalidate tag if HTML5.\n\t\tthis.attr( \"novalidate\", \"novalidate\" );\n\n\t\tvalidator = new $.validator( options, this[ 0 ] );\n\t\t$.data( this[ 0 ], \"validator\", validator );\n\n\t\tif ( validator.settings.onsubmit ) {\n\n\t\t\tthis.on( \"click.validate\", \":submit\", function( event ) {\n\n\t\t\t\t// Track the used submit button to properly handle scripted\n\t\t\t\t// submits later.\n\t\t\t\tvalidator.submitButton = event.currentTarget;\n\n\t\t\t\t// Allow suppressing validation by adding a cancel class to the submit button\n\t\t\t\tif ( $( this ).hasClass( \"cancel\" ) ) {\n\t\t\t\t\tvalidator.cancelSubmit = true;\n\t\t\t\t}\n\n\t\t\t\t// Allow suppressing validation by adding the html5 formnovalidate attribute to the submit button\n\t\t\t\tif ( $( this ).attr( \"formnovalidate\" ) !== undefined ) {\n\t\t\t\t\tvalidator.cancelSubmit = true;\n\t\t\t\t}\n\t\t\t} );\n\n\t\t\t// Validate the form on submit\n\t\t\tthis.on( \"submit.validate\", function( event ) {\n\t\t\t\tif ( validator.settings.debug ) {\n\n\t\t\t\t\t// Prevent form submit to be able to see console output\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t}\n\n\t\t\t\tfunction handle() {\n\t\t\t\t\tvar hidden, result;\n\n\t\t\t\t\t// Insert a hidden input as a replacement for the missing submit button\n\t\t\t\t\t// The hidden input is inserted in two cases:\n\t\t\t\t\t//   - A user defined a `submitHandler`\n\t\t\t\t\t//   - There was a pending request due to `remote` method and `stopRequest()`\n\t\t\t\t\t//     was called to submit the form in case it's valid\n\t\t\t\t\tif ( validator.submitButton && ( validator.settings.submitHandler || validator.formSubmitted ) ) {\n\t\t\t\t\t\thidden = $( \"<input type='hidden'/>\" )\n\t\t\t\t\t\t\t.attr( \"name\", validator.submitButton.name )\n\t\t\t\t\t\t\t.val( $( validator.submitButton ).val() )\n\t\t\t\t\t\t\t.appendTo( validator.currentForm );\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( validator.settings.submitHandler && !validator.settings.debug ) {\n\t\t\t\t\t\tresult = validator.settings.submitHandler.call( validator, validator.currentForm, event );\n\t\t\t\t\t\tif ( hidden ) {\n\n\t\t\t\t\t\t\t// And clean up afterwards; thanks to no-block-scope, hidden can be referenced\n\t\t\t\t\t\t\thidden.remove();\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ( result !== undefined ) {\n\t\t\t\t\t\t\treturn result;\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\t// Prevent submit for invalid forms or custom submit handlers\n\t\t\t\tif ( validator.cancelSubmit ) {\n\t\t\t\t\tvalidator.cancelSubmit = false;\n\t\t\t\t\treturn handle();\n\t\t\t\t}\n\t\t\t\tif ( validator.form() ) {\n\t\t\t\t\tif ( validator.pendingRequest ) {\n\t\t\t\t\t\tvalidator.formSubmitted = true;\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\treturn handle();\n\t\t\t\t} else {\n\t\t\t\t\tvalidator.focusInvalid();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\n\t\treturn validator;\n\t},\n\n\t// https://jqueryvalidation.org/valid/\n\tvalid: function() {\n\t\tvar valid, validator, errorList;\n\n\t\tif ( $( this[ 0 ] ).is( \"form\" ) ) {\n\t\t\tvalid = this.validate().form();\n\t\t} else {\n\t\t\terrorList = [];\n\t\t\tvalid = true;\n\t\t\tvalidator = $( this[ 0 ].form ).validate();\n\t\t\tthis.each( function() {\n\t\t\t\tvalid = validator.element( this ) && valid;\n\t\t\t\tif ( !valid ) {\n\t\t\t\t\terrorList = errorList.concat( validator.errorList );\n\t\t\t\t}\n\t\t\t} );\n\t\t\tvalidator.errorList = errorList;\n\t\t}\n\t\treturn valid;\n\t},\n\n\t// https://jqueryvalidation.org/rules/\n\trules: function( command, argument ) {\n\t\tvar element = this[ 0 ],\n\t\t\tisContentEditable = typeof this.attr( \"contenteditable\" ) !== \"undefined\" && this.attr( \"contenteditable\" ) !== \"false\",\n\t\t\tsettings, staticRules, existingRules, data, param, filtered;\n\n\t\t// If nothing is selected, return empty object; can't chain anyway\n\t\tif ( element == null ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( !element.form && isContentEditable ) {\n\t\t\telement.form = this.closest( \"form\" )[ 0 ];\n\t\t\telement.name = this.attr( \"name\" );\n\t\t}\n\n\t\tif ( element.form == null ) {\n\t\t\treturn;\n\t\t}\n\n\t\tif ( command ) {\n\t\t\tsettings = $.data( element.form, \"validator\" ).settings;\n\t\t\tstaticRules = settings.rules;\n\t\t\texistingRules = $.validator.staticRules( element );\n\t\t\tswitch ( command ) {\n\t\t\tcase \"add\":\n\t\t\t\t$.extend( existingRules, $.validator.normalizeRule( argument ) );\n\n\t\t\t\t// Remove messages from rules, but allow them to be set separately\n\t\t\t\tdelete existingRules.messages;\n\t\t\t\tstaticRules[ element.name ] = existingRules;\n\t\t\t\tif ( argument.messages ) {\n\t\t\t\t\tsettings.messages[ element.name ] = $.extend( settings.messages[ element.name ], argument.messages );\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase \"remove\":\n\t\t\t\tif ( !argument ) {\n\t\t\t\t\tdelete staticRules[ element.name ];\n\t\t\t\t\treturn existingRules;\n\t\t\t\t}\n\t\t\t\tfiltered = {};\n\t\t\t\t$.each( argument.split( /\\s/ ), function( index, method ) {\n\t\t\t\t\tfiltered[ method ] = existingRules[ method ];\n\t\t\t\t\tdelete existingRules[ method ];\n\t\t\t\t} );\n\t\t\t\treturn filtered;\n\t\t\t}\n\t\t}\n\n\t\tdata = $.validator.normalizeRules(\n\t\t$.extend(\n\t\t\t{},\n\t\t\t$.validator.classRules( element ),\n\t\t\t$.validator.attributeRules( element ),\n\t\t\t$.validator.dataRules( element ),\n\t\t\t$.validator.staticRules( element )\n\t\t), element );\n\n\t\t// Make sure required is at front\n\t\tif ( data.required ) {\n\t\t\tparam = data.required;\n\t\t\tdelete data.required;\n\t\t\tdata = $.extend( { required: param }, data );\n\t\t}\n\n\t\t// Make sure remote is at back\n\t\tif ( data.remote ) {\n\t\t\tparam = data.remote;\n\t\t\tdelete data.remote;\n\t\t\tdata = $.extend( data, { remote: param } );\n\t\t}\n\n\t\treturn data;\n\t}\n} );\n\n// JQuery trim is deprecated, provide a trim method based on String.prototype.trim\nvar trim = function( str ) {\n\n\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/trim#Polyfill\n\treturn str.replace( /^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, \"\" );\n};\n\n// Custom selectors\n$.extend( $.expr.pseudos || $.expr[ \":\" ], {\t\t// '|| $.expr[ \":\" ]' here enables backwards compatibility to jQuery 1.7. Can be removed when dropping jQ 1.7.x support\n\n\t// https://jqueryvalidation.org/blank-selector/\n\tblank: function( a ) {\n\t\treturn !trim( \"\" + $( a ).val() );\n\t},\n\n\t// https://jqueryvalidation.org/filled-selector/\n\tfilled: function( a ) {\n\t\tvar val = $( a ).val();\n\t\treturn val !== null && !!trim( \"\" + val );\n\t},\n\n\t// https://jqueryvalidation.org/unchecked-selector/\n\tunchecked: function( a ) {\n\t\treturn !$( a ).prop( \"checked\" );\n\t}\n} );\n\n// Constructor for validator\n$.validator = function( options, form ) {\n\tthis.settings = $.extend( true, {}, $.validator.defaults, options );\n\tthis.currentForm = form;\n\tthis.init();\n};\n\n// https://jqueryvalidation.org/jQuery.validator.format/\n$.validator.format = function( source, params ) {\n\tif ( arguments.length === 1 ) {\n\t\treturn function() {\n\t\t\tvar args = $.makeArray( arguments );\n\t\t\targs.unshift( source );\n\t\t\treturn $.validator.format.apply( this, args );\n\t\t};\n\t}\n\tif ( params === undefined ) {\n\t\treturn source;\n\t}\n\tif ( arguments.length > 2 && params.constructor !== Array  ) {\n\t\tparams = $.makeArray( arguments ).slice( 1 );\n\t}\n\tif ( params.constructor !== Array ) {\n\t\tparams = [ params ];\n\t}\n\t$.each( params, function( i, n ) {\n\t\tsource = source.replace( new RegExp( \"\\\\{\" + i + \"\\\\}\", \"g\" ), function() {\n\t\t\treturn n;\n\t\t} );\n\t} );\n\treturn source;\n};\n\n$.extend( $.validator, {\n\n\tdefaults: {\n\t\tmessages: {},\n\t\tgroups: {},\n\t\trules: {},\n\t\terrorClass: \"error\",\n\t\tpendingClass: \"pending\",\n\t\tvalidClass: \"valid\",\n\t\terrorElement: \"label\",\n\t\tfocusCleanup: false,\n\t\tfocusInvalid: true,\n\t\terrorContainer: $( [] ),\n\t\terrorLabelContainer: $( [] ),\n\t\tonsubmit: true,\n\t\tignore: \":hidden\",\n\t\tignoreTitle: false,\n\t\tcustomElements: [],\n\t\tonfocusin: function( element ) {\n\t\t\tthis.lastActive = element;\n\n\t\t\t// Hide error label and remove error class on focus if enabled\n\t\t\tif ( this.settings.focusCleanup ) {\n\t\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, element, this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t\tthis.hideThese( this.errorsFor( element ) );\n\t\t\t}\n\t\t},\n\t\tonfocusout: function( element ) {\n\t\t\tif ( !this.checkable( element ) && ( element.name in this.submitted || !this.optional( element ) ) ) {\n\t\t\t\tthis.element( element );\n\t\t\t}\n\t\t},\n\t\tonkeyup: function( element, event ) {\n\n\t\t\t// Avoid revalidate the field when pressing one of the following keys\n\t\t\t// Shift       => 16\n\t\t\t// Ctrl        => 17\n\t\t\t// Alt         => 18\n\t\t\t// Caps lock   => 20\n\t\t\t// End         => 35\n\t\t\t// Home        => 36\n\t\t\t// Left arrow  => 37\n\t\t\t// Up arrow    => 38\n\t\t\t// Right arrow => 39\n\t\t\t// Down arrow  => 40\n\t\t\t// Insert      => 45\n\t\t\t// Num lock    => 144\n\t\t\t// AltGr key   => 225\n\t\t\tvar excludedKeys = [\n\t\t\t\t16, 17, 18, 20, 35, 36, 37,\n\t\t\t\t38, 39, 40, 45, 144, 225\n\t\t\t];\n\n\t\t\tif ( event.which === 9 && this.elementValue( element ) === \"\" || $.inArray( event.keyCode, excludedKeys ) !== -1 ) {\n\t\t\t\treturn;\n\t\t\t} else if ( element.name in this.submitted || element.name in this.invalid ) {\n\t\t\t\tthis.element( element );\n\t\t\t}\n\t\t},\n\t\tonclick: function( element ) {\n\n\t\t\t// Click on selects, radiobuttons and checkboxes\n\t\t\tif ( element.name in this.submitted ) {\n\t\t\t\tthis.element( element );\n\n\t\t\t// Or option elements, check parent select in that case\n\t\t\t} else if ( element.parentNode.name in this.submitted ) {\n\t\t\t\tthis.element( element.parentNode );\n\t\t\t}\n\t\t},\n\t\thighlight: function( element, errorClass, validClass ) {\n\t\t\tif ( element.type === \"radio\" ) {\n\t\t\t\tthis.findByName( element.name ).addClass( errorClass ).removeClass( validClass );\n\t\t\t} else {\n\t\t\t\t$( element ).addClass( errorClass ).removeClass( validClass );\n\t\t\t}\n\t\t},\n\t\tunhighlight: function( element, errorClass, validClass ) {\n\t\t\tif ( element.type === \"radio\" ) {\n\t\t\t\tthis.findByName( element.name ).removeClass( errorClass ).addClass( validClass );\n\t\t\t} else {\n\t\t\t\t$( element ).removeClass( errorClass ).addClass( validClass );\n\t\t\t}\n\t\t}\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.setDefaults/\n\tsetDefaults: function( settings ) {\n\t\t$.extend( $.validator.defaults, settings );\n\t},\n\n\tmessages: {\n\t\trequired: \"This field is required.\",\n\t\tremote: \"Please fix this field.\",\n\t\temail: \"Please enter a valid email address.\",\n\t\turl: \"Please enter a valid URL.\",\n\t\tdate: \"Please enter a valid date.\",\n\t\tdateISO: \"Please enter a valid date (ISO).\",\n\t\tnumber: \"Please enter a valid number.\",\n\t\tdigits: \"Please enter only digits.\",\n\t\tequalTo: \"Please enter the same value again.\",\n\t\tmaxlength: $.validator.format( \"Please enter no more than {0} characters.\" ),\n\t\tminlength: $.validator.format( \"Please enter at least {0} characters.\" ),\n\t\trangelength: $.validator.format( \"Please enter a value between {0} and {1} characters long.\" ),\n\t\trange: $.validator.format( \"Please enter a value between {0} and {1}.\" ),\n\t\tmax: $.validator.format( \"Please enter a value less than or equal to {0}.\" ),\n\t\tmin: $.validator.format( \"Please enter a value greater than or equal to {0}.\" ),\n\t\tstep: $.validator.format( \"Please enter a multiple of {0}.\" )\n\t},\n\n\tautoCreateRanges: false,\n\n\tprototype: {\n\n\t\tinit: function() {\n\t\t\tthis.labelContainer = $( this.settings.errorLabelContainer );\n\t\t\tthis.errorContext = this.labelContainer.length && this.labelContainer || $( this.currentForm );\n\t\t\tthis.containers = $( this.settings.errorContainer ).add( this.settings.errorLabelContainer );\n\t\t\tthis.submitted = {};\n\t\t\tthis.valueCache = {};\n\t\t\tthis.pendingRequest = 0;\n\t\t\tthis.pending = {};\n\t\t\tthis.invalid = {};\n\t\t\tthis.reset();\n\n\t\t\tvar currentForm = this.currentForm,\n\t\t\t\tgroups = ( this.groups = {} ),\n\t\t\t\trules;\n\t\t\t$.each( this.settings.groups, function( key, value ) {\n\t\t\t\tif ( typeof value === \"string\" ) {\n\t\t\t\t\tvalue = value.split( /\\s/ );\n\t\t\t\t}\n\t\t\t\t$.each( value, function( index, name ) {\n\t\t\t\t\tgroups[ name ] = key;\n\t\t\t\t} );\n\t\t\t} );\n\t\t\trules = this.settings.rules;\n\t\t\t$.each( rules, function( key, value ) {\n\t\t\t\trules[ key ] = $.validator.normalizeRule( value );\n\t\t\t} );\n\n\t\t\tfunction delegate( event ) {\n\t\t\t\tvar isContentEditable = typeof $( this ).attr( \"contenteditable\" ) !== \"undefined\" && $( this ).attr( \"contenteditable\" ) !== \"false\";\n\n\t\t\t\t// Set form expando on contenteditable\n\t\t\t\tif ( !this.form && isContentEditable ) {\n\t\t\t\t\tthis.form = $( this ).closest( \"form\" )[ 0 ];\n\t\t\t\t\tthis.name = $( this ).attr( \"name\" );\n\t\t\t\t}\n\n\t\t\t\t// Ignore the element if it belongs to another form. This will happen mainly\n\t\t\t\t// when setting the `form` attribute of an input to the id of another form.\n\t\t\t\tif ( currentForm !== this.form ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar validator = $.data( this.form, \"validator\" ),\n\t\t\t\t\teventType = \"on\" + event.type.replace( /^validate/, \"\" ),\n\t\t\t\t\tsettings = validator.settings;\n\t\t\t\tif ( settings[ eventType ] && !$( this ).is( settings.ignore ) ) {\n\t\t\t\t\tsettings[ eventType ].call( validator, this, event );\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar focusListeners = [ \":text\", \"[type='password']\", \"[type='file']\", \"select\", \"textarea\", \"[type='number']\", \"[type='search']\",\n\t\t\t\t\t\t\t\t\"[type='tel']\", \"[type='url']\", \"[type='email']\", \"[type='datetime']\", \"[type='date']\", \"[type='month']\",\n\t\t\t\t\t\t\t\t\"[type='week']\", \"[type='time']\", \"[type='datetime-local']\", \"[type='range']\", \"[type='color']\",\n\t\t\t\t\t\t\t\t\"[type='radio']\", \"[type='checkbox']\", \"[contenteditable]\", \"[type='button']\" ];\n\t\t\tvar clickListeners = [ \"select\", \"option\", \"[type='radio']\", \"[type='checkbox']\" ];\n\t\t\t$( this.currentForm )\n\t\t\t\t.on( \"focusin.validate focusout.validate keyup.validate\", focusListeners.concat( this.settings.customElements ).join( \", \" ), delegate )\n\n\t\t\t\t// Support: Chrome, oldIE\n\t\t\t\t// \"select\" is provided as event.target when clicking a option\n\t\t\t\t.on( \"click.validate\", clickListeners.concat( this.settings.customElements ).join( \", \" ), delegate );\n\n\t\t\tif ( this.settings.invalidHandler ) {\n\t\t\t\t$( this.currentForm ).on( \"invalid-form.validate\", this.settings.invalidHandler );\n\t\t\t}\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.form/\n\t\tform: function() {\n\t\t\tthis.checkForm();\n\t\t\t$.extend( this.submitted, this.errorMap );\n\t\t\tthis.invalid = $.extend( {}, this.errorMap );\n\t\t\tif ( !this.valid() ) {\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\n\t\t\t}\n\t\t\tthis.showErrors();\n\t\t\treturn this.valid();\n\t\t},\n\n\t\tcheckForm: function() {\n\t\t\tthis.prepareForm();\n\t\t\tfor ( var i = 0, elements = ( this.currentElements = this.elements() ); elements[ i ]; i++ ) {\n\t\t\t\tthis.check( elements[ i ] );\n\t\t\t}\n\t\t\treturn this.valid();\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.element/\n\t\telement: function( element ) {\n\t\t\tvar cleanElement = this.clean( element ),\n\t\t\t\tcheckElement = this.validationTargetFor( cleanElement ),\n\t\t\t\tv = this,\n\t\t\t\tresult = true,\n\t\t\t\trs, group;\n\n\t\t\tif ( checkElement === undefined ) {\n\t\t\t\tdelete this.invalid[ cleanElement.name ];\n\t\t\t} else {\n\t\t\t\tthis.prepareElement( checkElement );\n\t\t\t\tthis.currentElements = $( checkElement );\n\n\t\t\t\t// If this element is grouped, then validate all group elements already\n\t\t\t\t// containing a value\n\t\t\t\tgroup = this.groups[ checkElement.name ];\n\t\t\t\tif ( group ) {\n\t\t\t\t\t$.each( this.groups, function( name, testgroup ) {\n\t\t\t\t\t\tif ( testgroup === group && name !== checkElement.name ) {\n\t\t\t\t\t\t\tcleanElement = v.validationTargetFor( v.clean( v.findByName( name ) ) );\n\t\t\t\t\t\t\tif ( cleanElement && cleanElement.name in v.invalid ) {\n\t\t\t\t\t\t\t\tv.currentElements.push( cleanElement );\n\t\t\t\t\t\t\t\tresult = v.check( cleanElement ) && result;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} );\n\t\t\t\t}\n\n\t\t\t\trs = this.check( checkElement ) !== false;\n\t\t\t\tresult = result && rs;\n\t\t\t\tif ( rs ) {\n\t\t\t\t\tthis.invalid[ checkElement.name ] = false;\n\t\t\t\t} else {\n\t\t\t\t\tthis.invalid[ checkElement.name ] = true;\n\t\t\t\t}\n\n\t\t\t\tif ( !this.numberOfInvalids() ) {\n\n\t\t\t\t\t// Hide error containers on last error\n\t\t\t\t\tthis.toHide = this.toHide.add( this.containers );\n\t\t\t\t}\n\t\t\t\tthis.showErrors();\n\n\t\t\t\t// Add aria-invalid status for screen readers\n\t\t\t\t$( element ).attr( \"aria-invalid\", !rs );\n\t\t\t}\n\n\t\t\treturn result;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.showErrors/\n\t\tshowErrors: function( errors ) {\n\t\t\tif ( errors ) {\n\t\t\t\tvar validator = this;\n\n\t\t\t\t// Add items to error list and map\n\t\t\t\t$.extend( this.errorMap, errors );\n\t\t\t\tthis.errorList = $.map( this.errorMap, function( message, name ) {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tmessage: message,\n\t\t\t\t\t\telement: validator.findByName( name )[ 0 ]\n\t\t\t\t\t};\n\t\t\t\t} );\n\n\t\t\t\t// Remove items from success list\n\t\t\t\tthis.successList = $.grep( this.successList, function( element ) {\n\t\t\t\t\treturn !( element.name in errors );\n\t\t\t\t} );\n\t\t\t}\n\t\t\tif ( this.settings.showErrors ) {\n\t\t\t\tthis.settings.showErrors.call( this, this.errorMap, this.errorList );\n\t\t\t} else {\n\t\t\t\tthis.defaultShowErrors();\n\t\t\t}\n\t\t},\n\n\t\t// https://jqueryvalidation.org/Validator.resetForm/\n\t\tresetForm: function() {\n\t\t\tif ( $.fn.resetForm ) {\n\t\t\t\t$( this.currentForm ).resetForm();\n\t\t\t}\n\t\t\tthis.invalid = {};\n\t\t\tthis.submitted = {};\n\t\t\tthis.prepareForm();\n\t\t\tthis.hideErrors();\n\t\t\tvar elements = this.elements()\n\t\t\t\t.removeData( \"previousValue\" )\n\t\t\t\t.removeAttr( \"aria-invalid\" );\n\n\t\t\tthis.resetElements( elements );\n\t\t},\n\n\t\tresetElements: function( elements ) {\n\t\t\tvar i;\n\n\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\tfor ( i = 0; elements[ i ]; i++ ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ],\n\t\t\t\t\t\tthis.settings.errorClass, \"\" );\n\t\t\t\t\tthis.findByName( elements[ i ].name ).removeClass( this.settings.validClass );\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\telements\n\t\t\t\t\t.removeClass( this.settings.errorClass )\n\t\t\t\t\t.removeClass( this.settings.validClass );\n\t\t\t}\n\t\t},\n\n\t\tnumberOfInvalids: function() {\n\t\t\treturn this.objectLength( this.invalid );\n\t\t},\n\n\t\tobjectLength: function( obj ) {\n\t\t\t/* jshint unused: false */\n\t\t\tvar count = 0,\n\t\t\t\ti;\n\t\t\tfor ( i in obj ) {\n\n\t\t\t\t// This check allows counting elements with empty error\n\t\t\t\t// message as invalid elements\n\t\t\t\tif ( obj[ i ] !== undefined && obj[ i ] !== null && obj[ i ] !== false ) {\n\t\t\t\t\tcount++;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn count;\n\t\t},\n\n\t\thideErrors: function() {\n\t\t\tthis.hideThese( this.toHide );\n\t\t},\n\n\t\thideThese: function( errors ) {\n\t\t\terrors.not( this.containers ).text( \"\" );\n\t\t\tthis.addWrapper( errors ).hide();\n\t\t},\n\n\t\tvalid: function() {\n\t\t\treturn this.size() === 0;\n\t\t},\n\n\t\tsize: function() {\n\t\t\treturn this.errorList.length;\n\t\t},\n\n\t\tfocusInvalid: function() {\n\t\t\tif ( this.settings.focusInvalid ) {\n\t\t\t\ttry {\n\t\t\t\t\t$( this.findLastActive() || this.errorList.length && this.errorList[ 0 ].element || [] )\n\t\t\t\t\t.filter( \":visible\" )\n\t\t\t\t\t.trigger( \"focus\" )\n\n\t\t\t\t\t// Manually trigger focusin event; without it, focusin handler isn't called, findLastActive won't have anything to find\n\t\t\t\t\t.trigger( \"focusin\" );\n\t\t\t\t} catch ( e ) {\n\n\t\t\t\t\t// Ignore IE throwing errors when focusing hidden elements\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tfindLastActive: function() {\n\t\t\tvar lastActive = this.lastActive;\n\t\t\treturn lastActive && $.grep( this.errorList, function( n ) {\n\t\t\t\treturn n.element.name === lastActive.name;\n\t\t\t} ).length === 1 && lastActive;\n\t\t},\n\n\t\telements: function() {\n\t\t\tvar validator = this,\n\t\t\t\trulesCache = {},\n\t\t\t\tselectors = [ \"input\", \"select\", \"textarea\", \"[contenteditable]\" ];\n\n\t\t\t// Select all valid inputs inside the form (no submit or reset buttons)\n\t\t\treturn $( this.currentForm )\n\t\t\t.find( selectors.concat( this.settings.customElements ).join( \", \" ) )\n\t\t\t.not( \":submit, :reset, :image, :disabled\" )\n\t\t\t.not( this.settings.ignore )\n\t\t\t.filter( function() {\n\t\t\t\tvar name = this.name || $( this ).attr( \"name\" ); // For contenteditable\n\t\t\t\tvar isContentEditable = typeof $( this ).attr( \"contenteditable\" ) !== \"undefined\" && $( this ).attr( \"contenteditable\" ) !== \"false\";\n\n\t\t\t\tif ( !name && validator.settings.debug && window.console ) {\n\t\t\t\t\tconsole.error( \"%o has no name assigned\", this );\n\t\t\t\t}\n\n\t\t\t\t// Set form expando on contenteditable\n\t\t\t\tif ( isContentEditable ) {\n\t\t\t\t\tthis.form = $( this ).closest( \"form\" )[ 0 ];\n\t\t\t\t\tthis.name = name;\n\t\t\t\t}\n\n\t\t\t\t// Ignore elements that belong to other/nested forms\n\t\t\t\tif ( this.form !== validator.currentForm ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\t// Select only the first element for each name, and only those with rules specified\n\t\t\t\tif ( name in rulesCache || !validator.objectLength( $( this ).rules() ) ) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\n\t\t\t\trulesCache[ name ] = true;\n\t\t\t\treturn true;\n\t\t\t} );\n\t\t},\n\n\t\tclean: function( selector ) {\n\t\t\treturn $( selector )[ 0 ];\n\t\t},\n\n\t\terrors: function() {\n\t\t\tvar errorClass = this.settings.errorClass.split( \" \" ).join( \".\" );\n\t\t\treturn $( this.settings.errorElement + \".\" + errorClass, this.errorContext );\n\t\t},\n\n\t\tresetInternals: function() {\n\t\t\tthis.successList = [];\n\t\t\tthis.errorList = [];\n\t\t\tthis.errorMap = {};\n\t\t\tthis.toShow = $( [] );\n\t\t\tthis.toHide = $( [] );\n\t\t},\n\n\t\treset: function() {\n\t\t\tthis.resetInternals();\n\t\t\tthis.currentElements = $( [] );\n\t\t},\n\n\t\tprepareForm: function() {\n\t\t\tthis.reset();\n\t\t\tthis.toHide = this.errors().add( this.containers );\n\t\t},\n\n\t\tprepareElement: function( element ) {\n\t\t\tthis.reset();\n\t\t\tthis.toHide = this.errorsFor( element );\n\t\t},\n\n\t\telementValue: function( element ) {\n\t\t\tvar $element = $( element ),\n\t\t\t\ttype = element.type,\n\t\t\t\tisContentEditable = typeof $element.attr( \"contenteditable\" ) !== \"undefined\" && $element.attr( \"contenteditable\" ) !== \"false\",\n\t\t\t\tval, idx;\n\n\t\t\tif ( type === \"radio\" || type === \"checkbox\" ) {\n\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).val();\n\t\t\t} else if ( type === \"number\" && typeof element.validity !== \"undefined\" ) {\n\t\t\t\treturn element.validity.badInput ? \"NaN\" : $element.val();\n\t\t\t}\n\n\t\t\tif ( isContentEditable ) {\n\t\t\t\tval = $element.text();\n\t\t\t} else {\n\t\t\t\tval = $element.val();\n\t\t\t}\n\n\t\t\tif ( type === \"file\" ) {\n\n\t\t\t\t// Modern browser (chrome & safari)\n\t\t\t\tif ( val.substr( 0, 12 ) === \"C:\\\\fakepath\\\\\" ) {\n\t\t\t\t\treturn val.substr( 12 );\n\t\t\t\t}\n\n\t\t\t\t// Legacy browsers\n\t\t\t\t// Unix-based path\n\t\t\t\tidx = val.lastIndexOf( \"/\" );\n\t\t\t\tif ( idx >= 0 ) {\n\t\t\t\t\treturn val.substr( idx + 1 );\n\t\t\t\t}\n\n\t\t\t\t// Windows-based path\n\t\t\t\tidx = val.lastIndexOf( \"\\\\\" );\n\t\t\t\tif ( idx >= 0 ) {\n\t\t\t\t\treturn val.substr( idx + 1 );\n\t\t\t\t}\n\n\t\t\t\t// Just the file name\n\t\t\t\treturn val;\n\t\t\t}\n\n\t\t\tif ( typeof val === \"string\" ) {\n\t\t\t\treturn val.replace( /\\r/g, \"\" );\n\t\t\t}\n\t\t\treturn val;\n\t\t},\n\n\t\tcheck: function( element ) {\n\t\t\telement = this.validationTargetFor( this.clean( element ) );\n\n\t\t\tvar rules = $( element ).rules(),\n\t\t\t\trulesCount = $.map( rules, function( n, i ) {\n\t\t\t\t\treturn i;\n\t\t\t\t} ).length,\n\t\t\t\tdependencyMismatch = false,\n\t\t\t\tval = this.elementValue( element ),\n\t\t\t\tresult, method, rule, normalizer;\n\n\t\t\t// Abort any pending Ajax request from a previous call to this method.\n\t\t\tthis.abortRequest( element );\n\n\t\t\t// Prioritize the local normalizer defined for this element over the global one\n\t\t\t// if the former exists, otherwise user the global one in case it exists.\n\t\t\tif ( typeof rules.normalizer === \"function\" ) {\n\t\t\t\tnormalizer = rules.normalizer;\n\t\t\t} else if (\ttypeof this.settings.normalizer === \"function\" ) {\n\t\t\t\tnormalizer = this.settings.normalizer;\n\t\t\t}\n\n\t\t\t// If normalizer is defined, then call it to retreive the changed value instead\n\t\t\t// of using the real one.\n\t\t\t// Note that `this` in the normalizer is `element`.\n\t\t\tif ( normalizer ) {\n\t\t\t\tval = normalizer.call( element, val );\n\n\t\t\t\t// Delete the normalizer from rules to avoid treating it as a pre-defined method.\n\t\t\t\tdelete rules.normalizer;\n\t\t\t}\n\n\t\t\tfor ( method in rules ) {\n\t\t\t\trule = { method: method, parameters: rules[ method ] };\n\t\t\t\ttry {\n\t\t\t\t\tresult = $.validator.methods[ method ].call( this, val, element, rule.parameters );\n\n\t\t\t\t\t// If a method indicates that the field is optional and therefore valid,\n\t\t\t\t\t// don't mark it as valid when there are no other rules\n\t\t\t\t\tif ( result === \"dependency-mismatch\" && rulesCount === 1 ) {\n\t\t\t\t\t\tdependencyMismatch = true;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tdependencyMismatch = false;\n\n\t\t\t\t\tif ( result === \"pending\" ) {\n\t\t\t\t\t\tthis.toHide = this.toHide.not( this.errorsFor( element ) );\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( !result ) {\n\t\t\t\t\t\tthis.formatAndAdd( element, rule );\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t} catch ( e ) {\n\t\t\t\t\tif ( this.settings.debug && window.console ) {\n\t\t\t\t\t\tconsole.log( \"Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\", e );\n\t\t\t\t\t}\n\t\t\t\t\tif ( e instanceof TypeError ) {\n\t\t\t\t\t\te.message += \".  Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\";\n\t\t\t\t\t}\n\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( dependencyMismatch ) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( this.objectLength( rules ) ) {\n\t\t\t\tthis.successList.push( element );\n\t\t\t}\n\t\t\treturn true;\n\t\t},\n\n\t\t// Return the custom message for the given element and validation method\n\t\t// specified in the element's HTML5 data attribute\n\t\t// return the generic message if present and no method specific message is present\n\t\tcustomDataMessage: function( element, method ) {\n\t\t\treturn $( element ).data( \"msg\" + method.charAt( 0 ).toUpperCase() +\n\t\t\t\tmethod.substring( 1 ).toLowerCase() ) || $( element ).data( \"msg\" );\n\t\t},\n\n\t\t// Return the custom message for the given element name and validation method\n\t\tcustomMessage: function( name, method ) {\n\t\t\tvar m = this.settings.messages[ name ];\n\t\t\treturn m && ( m.constructor === String ? m : m[ method ] );\n\t\t},\n\n\t\t// Return the first defined argument, allowing empty strings\n\t\tfindDefined: function() {\n\t\t\tfor ( var i = 0; i < arguments.length; i++ ) {\n\t\t\t\tif ( arguments[ i ] !== undefined ) {\n\t\t\t\t\treturn arguments[ i ];\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn undefined;\n\t\t},\n\n\t\t// The second parameter 'rule' used to be a string, and extended to an object literal\n\t\t// of the following form:\n\t\t// rule = {\n\t\t//     method: \"method name\",\n\t\t//     parameters: \"the given method parameters\"\n\t\t// }\n\t\t//\n\t\t// The old behavior still supported, kept to maintain backward compatibility with\n\t\t// old code, and will be removed in the next major release.\n\t\tdefaultMessage: function( element, rule ) {\n\t\t\tif ( typeof rule === \"string\" ) {\n\t\t\t\trule = { method: rule };\n\t\t\t}\n\n\t\t\tvar message = this.findDefined(\n\t\t\t\t\tthis.customMessage( element.name, rule.method ),\n\t\t\t\t\tthis.customDataMessage( element, rule.method ),\n\n\t\t\t\t\t// 'title' is never undefined, so handle empty string as undefined\n\t\t\t\t\t!this.settings.ignoreTitle && element.title || undefined,\n\t\t\t\t\t$.validator.messages[ rule.method ],\n\t\t\t\t\t\"<strong>Warning: No message defined for \" + element.name + \"</strong>\"\n\t\t\t\t),\n\t\t\t\ttheregex = /\\$?\\{(\\d+)\\}/g;\n\t\t\tif ( typeof message === \"function\" ) {\n\t\t\t\tmessage = message.call( this, rule.parameters, element );\n\t\t\t} else if ( theregex.test( message ) ) {\n\t\t\t\tmessage = $.validator.format( message.replace( theregex, \"{$1}\" ), rule.parameters );\n\t\t\t}\n\n\t\t\treturn message;\n\t\t},\n\n\t\tformatAndAdd: function( element, rule ) {\n\t\t\tvar message = this.defaultMessage( element, rule );\n\n\t\t\tthis.errorList.push( {\n\t\t\t\tmessage: message,\n\t\t\t\telement: element,\n\t\t\t\tmethod: rule.method\n\t\t\t} );\n\n\t\t\tthis.errorMap[ element.name ] = message;\n\t\t\tthis.submitted[ element.name ] = message;\n\t\t},\n\n\t\taddWrapper: function( toToggle ) {\n\t\t\tif ( this.settings.wrapper ) {\n\t\t\t\ttoToggle = toToggle.add( toToggle.parent( this.settings.wrapper ) );\n\t\t\t}\n\t\t\treturn toToggle;\n\t\t},\n\n\t\tdefaultShowErrors: function() {\n\t\t\tvar i, elements, error;\n\t\t\tfor ( i = 0; this.errorList[ i ]; i++ ) {\n\t\t\t\terror = this.errorList[ i ];\n\t\t\t\tif ( this.settings.highlight ) {\n\t\t\t\t\tthis.settings.highlight.call( this, error.element, this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t\tthis.showLabel( error.element, error.message );\n\t\t\t}\n\t\t\tif ( this.errorList.length ) {\n\t\t\t\tthis.toShow = this.toShow.add( this.containers );\n\t\t\t}\n\t\t\tif ( this.settings.success ) {\n\t\t\t\tfor ( i = 0; this.successList[ i ]; i++ ) {\n\t\t\t\t\tthis.showLabel( this.successList[ i ] );\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( this.settings.unhighlight ) {\n\t\t\t\tfor ( i = 0, elements = this.validElements(); elements[ i ]; i++ ) {\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ], this.settings.errorClass, this.settings.validClass );\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.toHide = this.toHide.not( this.toShow );\n\t\t\tthis.hideErrors();\n\t\t\tthis.addWrapper( this.toShow ).show();\n\t\t},\n\n\t\tvalidElements: function() {\n\t\t\treturn this.currentElements.not( this.invalidElements() );\n\t\t},\n\n\t\tinvalidElements: function() {\n\t\t\treturn $( this.errorList ).map( function() {\n\t\t\t\treturn this.element;\n\t\t\t} );\n\t\t},\n\n\t\tshowLabel: function( element, message ) {\n\t\t\tvar place, group, errorID, v,\n\t\t\t\terror = this.errorsFor( element ),\n\t\t\t\telementID = this.idOrName( element ),\n\t\t\t\tdescribedBy = $( element ).attr( \"aria-describedby\" );\n\n\t\t\tif ( error.length ) {\n\n\t\t\t\t// Refresh error/success class\n\t\t\t\terror.removeClass( this.settings.validClass ).addClass( this.settings.errorClass );\n\n\t\t\t\t// Replace message on existing label\n\t\t\t\tif ( this.settings && this.settings.escapeHtml ) {\n\t\t\t\t\terror.text( message || \"\" );\n\t\t\t\t} else {\n\t\t\t\t\terror.html( message || \"\" );\n\t\t\t\t}\n\t\t\t} else {\n\n\t\t\t\t// Create error element\n\t\t\t\terror = $( \"<\" + this.settings.errorElement + \">\" )\n\t\t\t\t\t.attr( \"id\", elementID + \"-error\" )\n\t\t\t\t\t.addClass( this.settings.errorClass );\n\n\t\t\t\tif ( this.settings && this.settings.escapeHtml ) {\n\t\t\t\t\terror.text( message || \"\" );\n\t\t\t\t} else {\n\t\t\t\t\terror.html( message || \"\" );\n\t\t\t\t}\n\n\t\t\t\t// Maintain reference to the element to be placed into the DOM\n\t\t\t\tplace = error;\n\t\t\t\tif ( this.settings.wrapper ) {\n\n\t\t\t\t\t// Make sure the element is visible, even in IE\n\t\t\t\t\t// actually showing the wrapped element is handled elsewhere\n\t\t\t\t\tplace = error.hide().show().wrap( \"<\" + this.settings.wrapper + \"/>\" ).parent();\n\t\t\t\t}\n\t\t\t\tif ( this.labelContainer.length ) {\n\t\t\t\t\tthis.labelContainer.append( place );\n\t\t\t\t} else if ( this.settings.errorPlacement ) {\n\t\t\t\t\tthis.settings.errorPlacement.call( this, place, $( element ) );\n\t\t\t\t} else {\n\t\t\t\t\tplace.insertAfter( element );\n\t\t\t\t}\n\n\t\t\t\t// Link error back to the element\n\t\t\t\tif ( error.is( \"label\" ) ) {\n\n\t\t\t\t\t// If the error is a label, then associate using 'for'\n\t\t\t\t\terror.attr( \"for\", elementID );\n\n\t\t\t\t\t// If the element is not a child of an associated label, then it's necessary\n\t\t\t\t\t// to explicitly apply aria-describedby\n\t\t\t\t} else if ( error.parents( \"label[for='\" + this.escapeCssMeta( elementID ) + \"']\" ).length === 0 ) {\n\t\t\t\t\terrorID = error.attr( \"id\" );\n\n\t\t\t\t\t// Respect existing non-error aria-describedby\n\t\t\t\t\tif ( !describedBy ) {\n\t\t\t\t\t\tdescribedBy = errorID;\n\t\t\t\t\t} else if ( !describedBy.match( new RegExp( \"\\\\b\" + this.escapeCssMeta( errorID ) + \"\\\\b\" ) ) ) {\n\n\t\t\t\t\t\t// Add to end of list if not already present\n\t\t\t\t\t\tdescribedBy += \" \" + errorID;\n\t\t\t\t\t}\n\t\t\t\t\t$( element ).attr( \"aria-describedby\", describedBy );\n\n\t\t\t\t\t// If this element is grouped, then assign to all elements in the same group\n\t\t\t\t\tgroup = this.groups[ element.name ];\n\t\t\t\t\tif ( group ) {\n\t\t\t\t\t\tv = this;\n\t\t\t\t\t\t$.each( v.groups, function( name, testgroup ) {\n\t\t\t\t\t\t\tif ( testgroup === group ) {\n\t\t\t\t\t\t\t\t$( \"[name='\" + v.escapeCssMeta( name ) + \"']\", v.currentForm )\n\t\t\t\t\t\t\t\t\t.attr( \"aria-describedby\", error.attr( \"id\" ) );\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} );\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif ( !message && this.settings.success ) {\n\t\t\t\terror.text( \"\" );\n\t\t\t\tif ( typeof this.settings.success === \"string\" ) {\n\t\t\t\t\terror.addClass( this.settings.success );\n\t\t\t\t} else {\n\t\t\t\t\tthis.settings.success( error, element );\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.toShow = this.toShow.add( error );\n\t\t},\n\n\t\terrorsFor: function( element ) {\n\t\t\tvar name = this.escapeCssMeta( this.idOrName( element ) ),\n\t\t\t\tdescriber = $( element ).attr( \"aria-describedby\" ),\n\t\t\t\tselector = \"label[for='\" + name + \"'], label[for='\" + name + \"'] *\";\n\n\t\t\t// 'aria-describedby' should directly reference the error element\n\t\t\tif ( describer ) {\n\t\t\t\tselector = selector + \", #\" + this.escapeCssMeta( describer )\n\t\t\t\t\t.replace( /\\s+/g, \", #\" );\n\t\t\t}\n\n\t\t\treturn this\n\t\t\t\t.errors()\n\t\t\t\t.filter( selector );\n\t\t},\n\n\t\t// See https://api.jquery.com/category/selectors/, for CSS\n\t\t// meta-characters that should be escaped in order to be used with JQuery\n\t\t// as a literal part of a name/id or any selector.\n\t\tescapeCssMeta: function( string ) {\n\t\t\tif ( string === undefined ) {\n\t\t\t\treturn \"\";\n\t\t\t}\n\n\t\t\treturn string.replace( /([\\\\!\"#$%&'()*+,./:;<=>?@\\[\\]^`{|}~])/g, \"\\\\$1\" );\n\t\t},\n\n\t\tidOrName: function( element ) {\n\t\t\treturn this.groups[ element.name ] || ( this.checkable( element ) ? element.name : element.id || element.name );\n\t\t},\n\n\t\tvalidationTargetFor: function( element ) {\n\n\t\t\t// If radio/checkbox, validate first element in group instead\n\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\telement = this.findByName( element.name );\n\t\t\t}\n\n\t\t\t// Always apply ignore filter\n\t\t\treturn $( element ).not( this.settings.ignore )[ 0 ];\n\t\t},\n\n\t\tcheckable: function( element ) {\n\t\t\treturn ( /radio|checkbox/i ).test( element.type );\n\t\t},\n\n\t\tfindByName: function( name ) {\n\t\t\treturn $( this.currentForm ).find( \"[name='\" + this.escapeCssMeta( name ) + \"']\" );\n\t\t},\n\n\t\tgetLength: function( value, element ) {\n\t\t\tswitch ( element.nodeName.toLowerCase() ) {\n\t\t\tcase \"select\":\n\t\t\t\treturn $( \"option:selected\", element ).length;\n\t\t\tcase \"input\":\n\t\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).length;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn value.length;\n\t\t},\n\n\t\tdepend: function( param, element ) {\n\t\t\treturn this.dependTypes[ typeof param ] ? this.dependTypes[ typeof param ]( param, element ) : true;\n\t\t},\n\n\t\tdependTypes: {\n\t\t\t\"boolean\": function( param ) {\n\t\t\t\treturn param;\n\t\t\t},\n\t\t\t\"string\": function( param, element ) {\n\t\t\t\treturn !!$( param, element.form ).length;\n\t\t\t},\n\t\t\t\"function\": function( param, element ) {\n\t\t\t\treturn param( element );\n\t\t\t}\n\t\t},\n\n\t\toptional: function( element ) {\n\t\t\tvar val = this.elementValue( element );\n\t\t\treturn !$.validator.methods.required.call( this, val, element ) && \"dependency-mismatch\";\n\t\t},\n\n\t\telementAjaxPort: function( element ) {\n\t\t\treturn \"validate\" + element.name;\n\t\t},\n\n\t\tstartRequest: function( element ) {\n\t\t\tif ( !this.pending[ element.name ] ) {\n\t\t\t\tthis.pendingRequest++;\n\t\t\t\t$( element ).addClass( this.settings.pendingClass );\n\t\t\t\tthis.pending[ element.name ] = true;\n\t\t\t}\n\t\t},\n\n\t\tstopRequest: function( element, valid ) {\n\t\t\tthis.pendingRequest--;\n\n\t\t\t// Sometimes synchronization fails, make sure pendingRequest is never < 0\n\t\t\tif ( this.pendingRequest < 0 ) {\n\t\t\t\tthis.pendingRequest = 0;\n\t\t\t}\n\t\t\tdelete this.pending[ element.name ];\n\t\t\t$( element ).removeClass( this.settings.pendingClass );\n\t\t\tif ( valid && this.pendingRequest === 0 && this.formSubmitted && this.form() && this.pendingRequest === 0 ) {\n\t\t\t\t$( this.currentForm ).trigger( \"submit\" );\n\n\t\t\t\t// Remove the hidden input that was used as a replacement for the\n\t\t\t\t// missing submit button. The hidden input is added by `handle()`\n\t\t\t\t// to ensure that the value of the used submit button is passed on\n\t\t\t\t// for scripted submits triggered by this method\n\t\t\t\tif ( this.submitButton ) {\n\t\t\t\t\t$( \"input:hidden[name='\" + this.submitButton.name + \"']\", this.currentForm ).remove();\n\t\t\t\t}\n\n\t\t\t\tthis.formSubmitted = false;\n\t\t\t} else if ( !valid && this.pendingRequest === 0 && this.formSubmitted ) {\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\n\t\t\t\tthis.formSubmitted = false;\n\t\t\t}\n\t\t},\n\n\t\tabortRequest: function( element ) {\n\t\t\tvar port;\n\n\t\t\tif ( this.pending[ element.name ] ) {\n\t\t\t\tport = this.elementAjaxPort( element );\n\t\t\t\t$.ajaxAbort( port );\n\n\t\t\t\tthis.pendingRequest--;\n\n\t\t\t\t// Sometimes synchronization fails, make sure pendingRequest is never < 0\n\t\t\t\tif ( this.pendingRequest < 0 ) {\n\t\t\t\t\tthis.pendingRequest = 0;\n\t\t\t\t}\n\n\t\t\t\tdelete this.pending[ element.name ];\n\t\t\t\t$( element ).removeClass( this.settings.pendingClass );\n\t\t\t}\n\t\t},\n\n\t\tpreviousValue: function( element, method ) {\n\t\t\tmethod = typeof method === \"string\" && method || \"remote\";\n\n\t\t\treturn $.data( element, \"previousValue\" ) || $.data( element, \"previousValue\", {\n\t\t\t\told: null,\n\t\t\t\tvalid: true,\n\t\t\t\tmessage: this.defaultMessage( element, { method: method } )\n\t\t\t} );\n\t\t},\n\n\t\t// Cleans up all forms and elements, removes validator-specific events\n\t\tdestroy: function() {\n\t\t\tthis.resetForm();\n\n\t\t\t$( this.currentForm )\n\t\t\t\t.off( \".validate\" )\n\t\t\t\t.removeData( \"validator\" )\n\t\t\t\t.find( \".validate-equalTo-blur\" )\n\t\t\t\t\t.off( \".validate-equalTo\" )\n\t\t\t\t\t.removeClass( \"validate-equalTo-blur\" )\n\t\t\t\t.find( \".validate-lessThan-blur\" )\n\t\t\t\t\t.off( \".validate-lessThan\" )\n\t\t\t\t\t.removeClass( \"validate-lessThan-blur\" )\n\t\t\t\t.find( \".validate-lessThanEqual-blur\" )\n\t\t\t\t\t.off( \".validate-lessThanEqual\" )\n\t\t\t\t\t.removeClass( \"validate-lessThanEqual-blur\" )\n\t\t\t\t.find( \".validate-greaterThanEqual-blur\" )\n\t\t\t\t\t.off( \".validate-greaterThanEqual\" )\n\t\t\t\t\t.removeClass( \"validate-greaterThanEqual-blur\" )\n\t\t\t\t.find( \".validate-greaterThan-blur\" )\n\t\t\t\t\t.off( \".validate-greaterThan\" )\n\t\t\t\t\t.removeClass( \"validate-greaterThan-blur\" );\n\t\t}\n\n\t},\n\n\tclassRuleSettings: {\n\t\trequired: { required: true },\n\t\temail: { email: true },\n\t\turl: { url: true },\n\t\tdate: { date: true },\n\t\tdateISO: { dateISO: true },\n\t\tnumber: { number: true },\n\t\tdigits: { digits: true },\n\t\tcreditcard: { creditcard: true }\n\t},\n\n\taddClassRules: function( className, rules ) {\n\t\tif ( className.constructor === String ) {\n\t\t\tthis.classRuleSettings[ className ] = rules;\n\t\t} else {\n\t\t\t$.extend( this.classRuleSettings, className );\n\t\t}\n\t},\n\n\tclassRules: function( element ) {\n\t\tvar rules = {},\n\t\t\tclasses = $( element ).attr( \"class\" );\n\n\t\tif ( classes ) {\n\t\t\t$.each( classes.split( \" \" ), function() {\n\t\t\t\tif ( this in $.validator.classRuleSettings ) {\n\t\t\t\t\t$.extend( rules, $.validator.classRuleSettings[ this ] );\n\t\t\t\t}\n\t\t\t} );\n\t\t}\n\t\treturn rules;\n\t},\n\n\tnormalizeAttributeRule: function( rules, type, method, value ) {\n\n\t\t// Convert the value to a number for number inputs, and for text for backwards compability\n\t\t// allows type=\"date\" and others to be compared as strings\n\t\tif ( /min|max|step/.test( method ) && ( type === null || /number|range|text/.test( type ) ) ) {\n\t\t\tvalue = Number( value );\n\n\t\t\t// Support Opera Mini, which returns NaN for undefined minlength\n\t\t\tif ( isNaN( value ) ) {\n\t\t\t\tvalue = undefined;\n\t\t\t}\n\t\t}\n\n\t\tif ( value || value === 0 ) {\n\t\t\trules[ method ] = value;\n\t\t} else if ( type === method && type !== \"range\" ) {\n\n\t\t\t// Exception: the jquery validate 'range' method\n\t\t\t// does not test for the html5 'range' type\n\t\t\trules[ type === \"date\" ? \"dateISO\" : method ] = true;\n\t\t}\n\t},\n\n\tattributeRules: function( element ) {\n\t\tvar rules = {},\n\t\t\t$element = $( element ),\n\t\t\ttype = element.getAttribute( \"type\" ),\n\t\t\tmethod, value;\n\n\t\tfor ( method in $.validator.methods ) {\n\n\t\t\t// Support for <input required> in both html5 and older browsers\n\t\t\tif ( method === \"required\" ) {\n\t\t\t\tvalue = element.getAttribute( method );\n\n\t\t\t\t// Some browsers return an empty string for the required attribute\n\t\t\t\t// and non-HTML5 browsers might have required=\"\" markup\n\t\t\t\tif ( value === \"\" ) {\n\t\t\t\t\tvalue = true;\n\t\t\t\t}\n\n\t\t\t\t// Force non-HTML5 browsers to return bool\n\t\t\t\tvalue = !!value;\n\t\t\t} else {\n\t\t\t\tvalue = $element.attr( method );\n\t\t\t}\n\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\n\t\t}\n\n\t\t// 'maxlength' may be returned as -1, 2147483647 ( IE ) and 524288 ( safari ) for text inputs\n\t\tif ( rules.maxlength && /-1|2147483647|524288/.test( rules.maxlength ) ) {\n\t\t\tdelete rules.maxlength;\n\t\t}\n\n\t\treturn rules;\n\t},\n\n\tdataRules: function( element ) {\n\t\tvar rules = {},\n\t\t\t$element = $( element ),\n\t\t\ttype = element.getAttribute( \"type\" ),\n\t\t\tmethod, value;\n\n\t\tfor ( method in $.validator.methods ) {\n\t\t\tvalue = $element.data( \"rule\" + method.charAt( 0 ).toUpperCase() + method.substring( 1 ).toLowerCase() );\n\n\t\t\t// Cast empty attributes like `data-rule-required` to `true`\n\t\t\tif ( value === \"\" ) {\n\t\t\t\tvalue = true;\n\t\t\t}\n\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\n\t\t}\n\t\treturn rules;\n\t},\n\n\tstaticRules: function( element ) {\n\t\tvar rules = {},\n\t\t\tvalidator = $.data( element.form, \"validator\" );\n\n\t\tif ( validator.settings.rules ) {\n\t\t\trules = $.validator.normalizeRule( validator.settings.rules[ element.name ] ) || {};\n\t\t}\n\t\treturn rules;\n\t},\n\n\tnormalizeRules: function( rules, element ) {\n\n\t\t// Handle dependency check\n\t\t$.each( rules, function( prop, val ) {\n\n\t\t\t// Ignore rule when param is explicitly false, eg. required:false\n\t\t\tif ( val === false ) {\n\t\t\t\tdelete rules[ prop ];\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif ( val.param || val.depends ) {\n\t\t\t\tvar keepRule = true;\n\t\t\t\tswitch ( typeof val.depends ) {\n\t\t\t\tcase \"string\":\n\t\t\t\t\tkeepRule = !!$( val.depends, element.form ).length;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"function\":\n\t\t\t\t\tkeepRule = val.depends.call( element, element );\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tif ( keepRule ) {\n\t\t\t\t\trules[ prop ] = val.param !== undefined ? val.param : true;\n\t\t\t\t} else {\n\t\t\t\t\t$.data( element.form, \"validator\" ).resetElements( $( element ) );\n\t\t\t\t\tdelete rules[ prop ];\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\n\t\t// Evaluate parameters\n\t\t$.each( rules, function( rule, parameter ) {\n\t\t\trules[ rule ] = typeof parameter === \"function\" && rule !== \"normalizer\" ? parameter( element ) : parameter;\n\t\t} );\n\n\t\t// Clean number parameters\n\t\t$.each( [ \"minlength\", \"maxlength\" ], function() {\n\t\t\tif ( rules[ this ] ) {\n\t\t\t\trules[ this ] = Number( rules[ this ] );\n\t\t\t}\n\t\t} );\n\t\t$.each( [ \"rangelength\", \"range\" ], function() {\n\t\t\tvar parts;\n\t\t\tif ( rules[ this ] ) {\n\t\t\t\tif ( Array.isArray( rules[ this ] ) ) {\n\t\t\t\t\trules[ this ] = [ Number( rules[ this ][ 0 ] ), Number( rules[ this ][ 1 ] ) ];\n\t\t\t\t} else if ( typeof rules[ this ] === \"string\" ) {\n\t\t\t\t\tparts = rules[ this ].replace( /[\\[\\]]/g, \"\" ).split( /[\\s,]+/ );\n\t\t\t\t\trules[ this ] = [ Number( parts[ 0 ] ), Number( parts[ 1 ] ) ];\n\t\t\t\t}\n\t\t\t}\n\t\t} );\n\n\t\tif ( $.validator.autoCreateRanges ) {\n\n\t\t\t// Auto-create ranges\n\t\t\tif ( rules.min != null && rules.max != null ) {\n\t\t\t\trules.range = [ rules.min, rules.max ];\n\t\t\t\tdelete rules.min;\n\t\t\t\tdelete rules.max;\n\t\t\t}\n\t\t\tif ( rules.minlength != null && rules.maxlength != null ) {\n\t\t\t\trules.rangelength = [ rules.minlength, rules.maxlength ];\n\t\t\t\tdelete rules.minlength;\n\t\t\t\tdelete rules.maxlength;\n\t\t\t}\n\t\t}\n\n\t\treturn rules;\n\t},\n\n\t// Converts a simple string to a {string: true} rule, e.g., \"required\" to {required:true}\n\tnormalizeRule: function( data ) {\n\t\tif ( typeof data === \"string\" ) {\n\t\t\tvar transformed = {};\n\t\t\t$.each( data.split( /\\s/ ), function() {\n\t\t\t\ttransformed[ this ] = true;\n\t\t\t} );\n\t\t\tdata = transformed;\n\t\t}\n\t\treturn data;\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.addMethod/\n\taddMethod: function( name, method, message ) {\n\t\t$.validator.methods[ name ] = method;\n\t\t$.validator.messages[ name ] = message !== undefined ? message : $.validator.messages[ name ];\n\t\tif ( method.length < 3 ) {\n\t\t\t$.validator.addClassRules( name, $.validator.normalizeRule( name ) );\n\t\t}\n\t},\n\n\t// https://jqueryvalidation.org/jQuery.validator.methods/\n\tmethods: {\n\n\t\t// https://jqueryvalidation.org/required-method/\n\t\trequired: function( value, element, param ) {\n\n\t\t\t// Check if dependency is met\n\t\t\tif ( !this.depend( param, element ) ) {\n\t\t\t\treturn \"dependency-mismatch\";\n\t\t\t}\n\t\t\tif ( element.nodeName.toLowerCase() === \"select\" ) {\n\n\t\t\t\t// Could be an array for select-multiple or a string, both are fine this way\n\t\t\t\tvar val = $( element ).val();\n\t\t\t\treturn val && val.length > 0;\n\t\t\t}\n\t\t\tif ( this.checkable( element ) ) {\n\t\t\t\treturn this.getLength( value, element ) > 0;\n\t\t\t}\n\t\t\treturn value !== undefined && value !== null && value.length > 0;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/email-method/\n\t\temail: function( value, element ) {\n\n\t\t\t// From https://html.spec.whatwg.org/multipage/forms.html#valid-e-mail-address\n\t\t\t// Retrieved 2014-01-14\n\t\t\t// If you have a problem with this implementation, report a bug against the above spec\n\t\t\t// Or use custom methods to implement your own email validation\n\t\t\treturn this.optional( element ) || /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/url-method/\n\t\turl: function( value, element ) {\n\n\t\t\t// Copyright (c) 2010-2013 Diego Perini, MIT licensed\n\t\t\t// https://gist.github.com/dperini/729294\n\t\t\t// see also https://mathiasbynens.be/demo/url-regex\n\t\t\t// modified to allow protocol-relative URLs\n\t\t\treturn this.optional( element ) || /^(?:(?:(?:https?|ftp):)?\\/\\/)(?:(?:[^\\]\\[?\\/<~#`!@$^&*()+=}|:\";',>{ ]|%[0-9A-Fa-f]{2})+(?::(?:[^\\]\\[?\\/<~#`!@$^&*()+=}|:\";',>{ ]|%[0-9A-Fa-f]{2})*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z0-9\\u00a1-\\uffff][a-z0-9\\u00a1-\\uffff_-]{0,62})?[a-z0-9\\u00a1-\\uffff]\\.)+(?:[a-z\\u00a1-\\uffff]{2,}\\.?))(?::\\d{2,5})?(?:[/?#]\\S*)?$/i.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/date-method/\n\t\tdate: ( function() {\n\t\t\tvar called = false;\n\n\t\t\treturn function( value, element ) {\n\t\t\t\tif ( !called ) {\n\t\t\t\t\tcalled = true;\n\t\t\t\t\tif ( this.settings.debug && window.console ) {\n\t\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t\t\"The `date` method is deprecated and will be removed in version '2.0.0'.\\n\" +\n\t\t\t\t\t\t\t\"Please don't use it, since it relies on the Date constructor, which\\n\" +\n\t\t\t\t\t\t\t\"behaves very differently across browsers and locales. Use `dateISO`\\n\" +\n\t\t\t\t\t\t\t\"instead or one of the locale specific methods in `localizations/`\\n\" +\n\t\t\t\t\t\t\t\"and `additional-methods.js`.\"\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn this.optional( element ) || !/Invalid|NaN/.test( new Date( value ).toString() );\n\t\t\t};\n\t\t}() ),\n\n\t\t// https://jqueryvalidation.org/dateISO-method/\n\t\tdateISO: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/number-method/\n\t\tnumber: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:-?\\.\\d+)?$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/digits-method/\n\t\tdigits: function( value, element ) {\n\t\t\treturn this.optional( element ) || /^\\d+$/.test( value );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/minlength-method/\n\t\tminlength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || length >= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/maxlength-method/\n\t\tmaxlength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || length <= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/rangelength-method/\n\t\trangelength: function( value, element, param ) {\n\t\t\tvar length = Array.isArray( value ) ? value.length : this.getLength( value, element );\n\t\t\treturn this.optional( element ) || ( length >= param[ 0 ] && length <= param[ 1 ] );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/min-method/\n\t\tmin: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || value >= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/max-method/\n\t\tmax: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || value <= param;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/range-method/\n\t\trange: function( value, element, param ) {\n\t\t\treturn this.optional( element ) || ( value >= param[ 0 ] && value <= param[ 1 ] );\n\t\t},\n\n\t\t// https://jqueryvalidation.org/step-method/\n\t\tstep: function( value, element, param ) {\n\t\t\tvar type = $( element ).attr( \"type\" ),\n\t\t\t\terrorMessage = \"Step attribute on input type \" + type + \" is not supported.\",\n\t\t\t\tsupportedTypes = [ \"text\", \"number\", \"range\" ],\n\t\t\t\tre = new RegExp( \"\\\\b\" + type + \"\\\\b\" ),\n\t\t\t\tnotSupported = type && !re.test( supportedTypes.join() ),\n\t\t\t\tdecimalPlaces = function( num ) {\n\t\t\t\t\tvar match = ( \"\" + num ).match( /(?:\\.(\\d+))?$/ );\n\t\t\t\t\tif ( !match ) {\n\t\t\t\t\t\treturn 0;\n\t\t\t\t\t}\n\n\t\t\t\t\t// Number of digits right of decimal point.\n\t\t\t\t\treturn match[ 1 ] ? match[ 1 ].length : 0;\n\t\t\t\t},\n\t\t\t\ttoInt = function( num ) {\n\t\t\t\t\treturn Math.round( num * Math.pow( 10, decimals ) );\n\t\t\t\t},\n\t\t\t\tvalid = true,\n\t\t\t\tdecimals;\n\n\t\t\t// Works only for text, number and range input types\n\t\t\t// TODO find a way to support input types date, datetime, datetime-local, month, time and week\n\t\t\tif ( notSupported ) {\n\t\t\t\tthrow new Error( errorMessage );\n\t\t\t}\n\n\t\t\tdecimals = decimalPlaces( param );\n\n\t\t\t// Value can't have too many decimals\n\t\t\tif ( decimalPlaces( value ) > decimals || toInt( value ) % toInt( param ) !== 0 ) {\n\t\t\t\tvalid = false;\n\t\t\t}\n\n\t\t\treturn this.optional( element ) || valid;\n\t\t},\n\n\t\t// https://jqueryvalidation.org/equalTo-method/\n\t\tequalTo: function( value, element, param ) {\n\n\t\t\t// Bind to the blur event of the target in order to revalidate whenever the target field is updated\n\t\t\tvar target = $( param );\n\t\t\tif ( this.settings.onfocusout && target.not( \".validate-equalTo-blur\" ).length ) {\n\t\t\t\ttarget.addClass( \"validate-equalTo-blur\" ).on( \"blur.validate-equalTo\", function() {\n\t\t\t\t\t$( element ).valid();\n\t\t\t\t} );\n\t\t\t}\n\t\t\treturn value === target.val();\n\t\t},\n\n\t\t// https://jqueryvalidation.org/remote-method/\n\t\tremote: function( value, element, param, method ) {\n\t\t\tif ( this.optional( element ) ) {\n\t\t\t\treturn \"dependency-mismatch\";\n\t\t\t}\n\n\t\t\tmethod = typeof method === \"string\" && method || \"remote\";\n\n\t\t\tvar previous = this.previousValue( element, method ),\n\t\t\t\tvalidator, data, optionDataString;\n\n\t\t\tif ( !this.settings.messages[ element.name ] ) {\n\t\t\t\tthis.settings.messages[ element.name ] = {};\n\t\t\t}\n\t\t\tprevious.originalMessage = previous.originalMessage || this.settings.messages[ element.name ][ method ];\n\t\t\tthis.settings.messages[ element.name ][ method ] = previous.message;\n\n\t\t\tparam = typeof param === \"string\" && { url: param } || param;\n\t\t\toptionDataString = $.param( $.extend( { data: value }, param.data ) );\n\t\t\tif ( previous.valid !== null && previous.old === optionDataString ) {\n\t\t\t\treturn previous.valid;\n\t\t\t}\n\n\t\t\tprevious.old = optionDataString;\n\t\t\tprevious.valid = null;\n\t\t\tvalidator = this;\n\t\t\tthis.startRequest( element );\n\t\t\tdata = {};\n\t\t\tdata[ element.name ] = value;\n\t\t\t$.ajax( $.extend( true, {\n\t\t\t\tmode: \"abort\",\n\t\t\t\tport: this.elementAjaxPort( element ),\n\t\t\t\tdataType: \"json\",\n\t\t\t\tdata: data,\n\t\t\t\tcontext: validator.currentForm,\n\t\t\t\tsuccess: function( response ) {\n\t\t\t\t\tvar valid = response === true || response === \"true\",\n\t\t\t\t\t\terrors, message, submitted;\n\n\t\t\t\t\tvalidator.settings.messages[ element.name ][ method ] = previous.originalMessage;\n\t\t\t\t\tif ( valid ) {\n\t\t\t\t\t\tsubmitted = validator.formSubmitted;\n\t\t\t\t\t\tvalidator.toHide = validator.errorsFor( element );\n\t\t\t\t\t\tvalidator.formSubmitted = submitted;\n\t\t\t\t\t\tvalidator.successList.push( element );\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = false;\n\t\t\t\t\t\tvalidator.showErrors();\n\t\t\t\t\t} else {\n\t\t\t\t\t\terrors = {};\n\t\t\t\t\t\tmessage = response || validator.defaultMessage( element, { method: method, parameters: value } );\n\t\t\t\t\t\terrors[ element.name ] = previous.message = message;\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = true;\n\t\t\t\t\t\tvalidator.showErrors( errors );\n\t\t\t\t\t}\n\t\t\t\t\tprevious.valid = valid;\n\t\t\t\t\tvalidator.stopRequest( element, valid );\n\t\t\t\t}\n\t\t\t}, param ) );\n\t\t\treturn \"pending\";\n\t\t}\n\t}\n\n} );\n\n// Ajax mode: abort\n// usage: $.ajax({ mode: \"abort\"[, port: \"uniqueport\"]});\n//        $.ajaxAbort( port );\n// if mode:\"abort\" is used, the previous request on that port (port can be undefined) is aborted via XMLHttpRequest.abort()\n\nvar pendingRequests = {},\n\tajax;\n\n// Use a prefilter if available (1.5+)\nif ( $.ajaxPrefilter ) {\n\t$.ajaxPrefilter( function( settings, _, xhr ) {\n\t\tvar port = settings.port;\n\t\tif ( settings.mode === \"abort\" ) {\n\t\t\t$.ajaxAbort( port );\n\t\t\tpendingRequests[ port ] = xhr;\n\t\t}\n\t} );\n} else {\n\n\t// Proxy ajax\n\tajax = $.ajax;\n\t$.ajax = function( settings ) {\n\t\tvar mode = ( \"mode\" in settings ? settings : $.ajaxSettings ).mode,\n\t\t\tport = ( \"port\" in settings ? settings : $.ajaxSettings ).port;\n\t\tif ( mode === \"abort\" ) {\n\t\t\t$.ajaxAbort( port );\n\t\t\tpendingRequests[ port ] = ajax.apply( this, arguments );\n\t\t\treturn pendingRequests[ port ];\n\t\t}\n\t\treturn ajax.apply( this, arguments );\n\t};\n}\n\n// Abort the previous request without sending a new one\n$.ajaxAbort = function( port ) {\n\tif ( pendingRequests[ port ] ) {\n\t\tpendingRequests[ port ].abort();\n\t\tdelete pendingRequests[ port ];\n\t}\n};\nreturn $;\n}));", "/*!\r\n * @copyright Copyright &copy; <PERSON><PERSON><PERSON>, Krajee.com, 2014 - 2020\r\n * @version 1.3.6\r\n *\r\n * Date formatter utility library that allows formatting date/time variables or Date objects using PHP DateTime format.\r\n * This library is a standalone javascript library and does not depend on other libraries or plugins like jQuery. The\r\n * library also adds support for Universal Module Definition (UMD).\r\n * \r\n * @see http://php.net/manual/en/function.date.php\r\n *\r\n * For more JQuery plugins visit http://plugins.krajee.com\r\n * For more Yii related demos visit http://demos.krajee.com\r\n */\r\n(function (root, factory) {\r\n    // noinspection JSUnresolvedVariable\r\n    if (typeof define === 'function' && define.amd) { // AMD\r\n        // noinspection JSUnresolvedFunction\r\n        define([], factory);\r\n    } else {\r\n        // noinspection JSUnresolvedVariable\r\n        if (typeof module === 'object' && module.exports) { // Node\r\n            // noinspection JSUnresolvedVariable\r\n            module.exports = factory();\r\n        } else { // Browser globals\r\n            root.DateFormatter = factory();\r\n        }\r\n    }\r\n}(typeof self !== 'undefined' ? self : this, function () {\r\n    var DateFormatter, $h;\r\n    /**\r\n     * Global helper object\r\n     */\r\n    $h = {\r\n        DAY: 1000 * 60 * 60 * 24,\r\n        HOUR: 3600,\r\n        defaults: {\r\n            dateSettings: {\r\n                days: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\r\n                daysShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],\r\n                months: [\r\n                    'January', 'February', 'March', 'April', 'May', 'June', 'July',\r\n                    'August', 'September', 'October', 'November', 'December'\r\n                ],\r\n                monthsShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\r\n                meridiem: ['AM', 'PM'],\r\n                ordinal: function (number) {\r\n                    var n = number % 10, suffixes = {1: 'st', 2: 'nd', 3: 'rd'};\r\n                    return Math.floor(number % 100 / 10) === 1 || !suffixes[n] ? 'th' : suffixes[n];\r\n                }\r\n            },\r\n            separators: /[ \\-+\\/.:@]/g,\r\n            validParts: /[dDjlNSwzWFmMntLoYyaABgGhHisueTIOPZcrU]/g,\r\n            intParts: /[djwNzmnyYhHgGis]/g,\r\n            tzParts: /\\b(?:[PMCEA][SDP]T|(?:Pacific|Mountain|Central|Eastern|Atlantic) (?:Standard|Daylight|Prevailing) Time|(?:GMT|UTC)(?:[-+]\\d{4})?)\\b/g,\r\n            tzClip: /[^-+\\dA-Z]/g\r\n        },\r\n        getInt: function (str, radix) {\r\n            return parseInt(str, (radix ? radix : 10));\r\n        },\r\n        compare: function (str1, str2) {\r\n            return typeof (str1) === 'string' && typeof (str2) === 'string' && str1.toLowerCase() === str2.toLowerCase();\r\n        },\r\n        lpad: function (value, length, chr) {\r\n            var val = value.toString();\r\n            chr = chr || '0';\r\n            return val.length < length ? $h.lpad(chr + val, length) : val;\r\n        },\r\n        merge: function (out) {\r\n            var i, obj;\r\n            out = out || {};\r\n            for (i = 1; i < arguments.length; i++) {\r\n                obj = arguments[i];\r\n                if (!obj) {\r\n                    continue;\r\n                }\r\n                for (var key in obj) {\r\n                    if (obj.hasOwnProperty(key)) {\r\n                        if (typeof obj[key] === 'object') {\r\n                            $h.merge(out[key], obj[key]);\r\n                        } else {\r\n                            out[key] = obj[key];\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return out;\r\n        },\r\n        getIndex: function (val, arr) {\r\n            for (var i = 0; i < arr.length; i++) {\r\n                if (arr[i].toLowerCase() === val.toLowerCase()) {\r\n                    return i;\r\n                }\r\n            }\r\n            return -1;\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Date Formatter Library Constructor\r\n     * @param options\r\n     * @constructor\r\n     */\r\n    DateFormatter = function (options) {\r\n        var self = this, config = $h.merge($h.defaults, options);\r\n        self.dateSettings = config.dateSettings;\r\n        self.separators = config.separators;\r\n        self.validParts = config.validParts;\r\n        self.intParts = config.intParts;\r\n        self.tzParts = config.tzParts;\r\n        self.tzClip = config.tzClip;\r\n    };\r\n\r\n    /**\r\n     * DateFormatter Library Prototype\r\n     */\r\n    DateFormatter.prototype = {\r\n        constructor: DateFormatter,\r\n        getMonth: function (val) {\r\n            var self = this, i;\r\n            i = $h.getIndex(val, self.dateSettings.monthsShort) + 1;\r\n            if (i === 0) {\r\n                i = $h.getIndex(val, self.dateSettings.months) + 1;\r\n            }\r\n            return i;\r\n        },\r\n        parseDate: function (vDate, vFormat) {\r\n            var self = this, vFormatParts, vDateParts, i, vDateFlag = false, vTimeFlag = false, vDatePart, iDatePart,\r\n                vSettings = self.dateSettings, vMonth, vMeriIndex, vMeriOffset, len, mer,\r\n                out = {date: null, year: null, month: null, day: null, hour: 0, min: 0, sec: 0};\r\n            if (!vDate) {\r\n                return null;\r\n            }\r\n            if (vDate instanceof Date) {\r\n                return vDate;\r\n            }\r\n            if (vFormat === 'U') {\r\n                i = $h.getInt(vDate);\r\n                return i ? new Date(i * 1000) : vDate;\r\n            }\r\n            switch (typeof vDate) {\r\n                case 'number':\r\n                    return new Date(vDate);\r\n                case 'string':\r\n                    break;\r\n                default:\r\n                    return null;\r\n            }\r\n            vFormatParts = vFormat.match(self.validParts);\r\n            if (!vFormatParts || vFormatParts.length === 0) {\r\n                throw new Error('Invalid date format definition.');\r\n            }\r\n            for (i = vFormatParts.length - 1; i >= 0; i--) {\r\n                if (vFormatParts[i] === 'S') {\r\n                    vFormatParts.splice(i, 1);\r\n                }\r\n            }\r\n            vDateParts = vDate.replace(self.separators, '\\0').split('\\0');\r\n            for (i = 0; i < vDateParts.length; i++) {\r\n                vDatePart = vDateParts[i];\r\n                iDatePart = $h.getInt(vDatePart);\r\n                switch (vFormatParts[i]) {\r\n                    case 'y':\r\n                    case 'Y':\r\n                        if (iDatePart) {\r\n                            len = vDatePart.length;\r\n                            out.year = len === 2 ? $h.getInt((iDatePart < 70 ? '20' : '19') + vDatePart) : iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'm':\r\n                    case 'n':\r\n                    case 'M':\r\n                    case 'F':\r\n                        if (isNaN(iDatePart)) {\r\n                            vMonth = self.getMonth(vDatePart);\r\n                            if (vMonth > 0) {\r\n                                out.month = vMonth;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        } else {\r\n                            if (iDatePart >= 1 && iDatePart <= 12) {\r\n                                out.month = iDatePart;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'd':\r\n                    case 'j':\r\n                        if (iDatePart >= 1 && iDatePart <= 31) {\r\n                            out.day = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vDateFlag = true;\r\n                        break;\r\n                    case 'g':\r\n                    case 'h':\r\n                        vMeriIndex = (vFormatParts.indexOf('a') > -1) ? vFormatParts.indexOf('a') :\r\n                            ((vFormatParts.indexOf('A') > -1) ? vFormatParts.indexOf('A') : -1);\r\n                        mer = vDateParts[vMeriIndex];\r\n                        if (vMeriIndex !== -1) {\r\n                            vMeriOffset = $h.compare(mer, vSettings.meridiem[0]) ? 0 :\r\n                                ($h.compare(mer, vSettings.meridiem[1]) ? 12 : -1);\r\n                            if (iDatePart >= 1 && iDatePart <= 12 && vMeriOffset !== -1) {\r\n                                out.hour = iDatePart % 12 === 0 ? vMeriOffset : iDatePart + vMeriOffset;\r\n                            } else {\r\n                                if (iDatePart >= 0 && iDatePart <= 23) {\r\n                                    out.hour = iDatePart;\r\n                                }\r\n                            }\r\n                        } else {\r\n                            if (iDatePart >= 0 && iDatePart <= 23) {\r\n                                out.hour = iDatePart;\r\n                            } else {\r\n                                return null;\r\n                            }\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 'G':\r\n                    case 'H':\r\n                        if (iDatePart >= 0 && iDatePart <= 23) {\r\n                            out.hour = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 'i':\r\n                        if (iDatePart >= 0 && iDatePart <= 59) {\r\n                            out.min = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                    case 's':\r\n                        if (iDatePart >= 0 && iDatePart <= 59) {\r\n                            out.sec = iDatePart;\r\n                        } else {\r\n                            return null;\r\n                        }\r\n                        vTimeFlag = true;\r\n                        break;\r\n                }\r\n            }\r\n            if (vDateFlag === true) {\r\n                var varY = out.year || 0, varM = out.month ? out.month - 1 : 0, varD = out.day || 1;\r\n                out.date = new Date(varY, varM, varD, out.hour, out.min, out.sec, 0);\r\n            } else {\r\n                if (vTimeFlag !== true) {\r\n                    return null;\r\n                }\r\n                out.date = new Date(0, 0, 0, out.hour, out.min, out.sec, 0);\r\n            }\r\n            return out.date;\r\n        },\r\n        guessDate: function (vDateStr, vFormat) {\r\n            if (typeof vDateStr !== 'string') {\r\n                return vDateStr;\r\n            }\r\n            var self = this, vParts = vDateStr.replace(self.separators, '\\0').split('\\0'), vPattern = /^[djmn]/g, len,\r\n                vFormatParts = vFormat.match(self.validParts), vDate = new Date(), vDigit = 0, vYear, i, n, iPart, iSec;\r\n\r\n            if (!vPattern.test(vFormatParts[0])) {\r\n                return vDateStr;\r\n            }\r\n\r\n            for (i = 0; i < vParts.length; i++) {\r\n                vDigit = 2;\r\n                iPart = vParts[i];\r\n                iSec = $h.getInt(iPart.substr(0, 2));\r\n                if (isNaN(iSec)) {\r\n                    return null;\r\n                }\r\n                switch (i) {\r\n                    case 0:\r\n                        if (vFormatParts[0] === 'm' || vFormatParts[0] === 'n') {\r\n                            vDate.setMonth(iSec - 1);\r\n                        } else {\r\n                            vDate.setDate(iSec);\r\n                        }\r\n                        break;\r\n                    case 1:\r\n                        if (vFormatParts[0] === 'm' || vFormatParts[0] === 'n') {\r\n                            vDate.setDate(iSec);\r\n                        } else {\r\n                            vDate.setMonth(iSec - 1);\r\n                        }\r\n                        break;\r\n                    case 2:\r\n                        vYear = vDate.getFullYear();\r\n                        len = iPart.length;\r\n                        vDigit = len < 4 ? len : 4;\r\n                        vYear = $h.getInt(len < 4 ? vYear.toString().substr(0, 4 - len) + iPart : iPart.substr(0, 4));\r\n                        if (!vYear) {\r\n                            return null;\r\n                        }\r\n                        vDate.setFullYear(vYear);\r\n                        break;\r\n                    case 3:\r\n                        vDate.setHours(iSec);\r\n                        break;\r\n                    case 4:\r\n                        vDate.setMinutes(iSec);\r\n                        break;\r\n                    case 5:\r\n                        vDate.setSeconds(iSec);\r\n                        break;\r\n                }\r\n                n = iPart.substr(vDigit);\r\n                if (n.length > 0) {\r\n                    vParts.splice(i + 1, 0, n);\r\n                }\r\n            }\r\n            return vDate;\r\n        },\r\n        parseFormat: function (vChar, vDate) {\r\n            var self = this, vSettings = self.dateSettings, fmt, backslash = /\\\\?(.?)/gi, doFormat = function (t, s) {\r\n                return fmt[t] ? fmt[t]() : s;\r\n            };\r\n            fmt = {\r\n                /////////\r\n                // DAY //\r\n                /////////\r\n                /**\r\n                 * Day of month with leading 0: `01..31`\r\n                 * @return {string}\r\n                 */\r\n                d: function () {\r\n                    return $h.lpad(fmt.j(), 2);\r\n                },\r\n                /**\r\n                 * Shorthand day name: `Mon...Sun`\r\n                 * @return {string}\r\n                 */\r\n                D: function () {\r\n                    return vSettings.daysShort[fmt.w()];\r\n                },\r\n                /**\r\n                 * Day of month: `1..31`\r\n                 * @return {number}\r\n                 */\r\n                j: function () {\r\n                    return vDate.getDate();\r\n                },\r\n                /**\r\n                 * Full day name: `Monday...Sunday`\r\n                 * @return {string}\r\n                 */\r\n                l: function () {\r\n                    return vSettings.days[fmt.w()];\r\n                },\r\n                /**\r\n                 * ISO-8601 day of week: `1[Mon]..7[Sun]`\r\n                 * @return {number}\r\n                 */\r\n                N: function () {\r\n                    return fmt.w() || 7;\r\n                },\r\n                /**\r\n                 * Day of week: `0[Sun]..6[Sat]`\r\n                 * @return {number}\r\n                 */\r\n                w: function () {\r\n                    return vDate.getDay();\r\n                },\r\n                /**\r\n                 * Day of year: `0..365`\r\n                 * @return {number}\r\n                 */\r\n                z: function () {\r\n                    var a = new Date(fmt.Y(), fmt.n() - 1, fmt.j()), b = new Date(fmt.Y(), 0, 1);\r\n                    return Math.round((a - b) / $h.DAY);\r\n                },\r\n\r\n                //////////\r\n                // WEEK //\r\n                //////////\r\n                /**\r\n                 * ISO-8601 week number\r\n                 * @return {number}\r\n                 */\r\n                W: function () {\r\n                    var a = new Date(fmt.Y(), fmt.n() - 1, fmt.j() - fmt.N() + 3), b = new Date(a.getFullYear(), 0, 4);\r\n                    return $h.lpad(1 + Math.round((a - b) / $h.DAY / 7), 2);\r\n                },\r\n\r\n                ///////////\r\n                // MONTH //\r\n                ///////////\r\n                /**\r\n                 * Full month name: `January...December`\r\n                 * @return {string}\r\n                 */\r\n                F: function () {\r\n                    return vSettings.months[vDate.getMonth()];\r\n                },\r\n                /**\r\n                 * Month w/leading 0: `01..12`\r\n                 * @return {string}\r\n                 */\r\n                m: function () {\r\n                    return $h.lpad(fmt.n(), 2);\r\n                },\r\n                /**\r\n                 * Shorthand month name; `Jan...Dec`\r\n                 * @return {string}\r\n                 */\r\n                M: function () {\r\n                    return vSettings.monthsShort[vDate.getMonth()];\r\n                },\r\n                /**\r\n                 * Month: `1...12`\r\n                 * @return {number}\r\n                 */\r\n                n: function () {\r\n                    return vDate.getMonth() + 1;\r\n                },\r\n                /**\r\n                 * Days in month: `28...31`\r\n                 * @return {number}\r\n                 */\r\n                t: function () {\r\n                    return (new Date(fmt.Y(), fmt.n(), 0)).getDate();\r\n                },\r\n\r\n                //////////\r\n                // YEAR //\r\n                //////////\r\n                /**\r\n                 * Is leap year? `0 or 1`\r\n                 * @return {number}\r\n                 */\r\n                L: function () {\r\n                    var Y = fmt.Y();\r\n                    return (Y % 4 === 0 && Y % 100 !== 0 || Y % 400 === 0) ? 1 : 0;\r\n                },\r\n                /**\r\n                 * ISO-8601 year\r\n                 * @return {number}\r\n                 */\r\n                o: function () {\r\n                    var n = fmt.n(), W = fmt.W(), Y = fmt.Y();\r\n                    return Y + (n === 12 && W < 9 ? 1 : n === 1 && W > 9 ? -1 : 0);\r\n                },\r\n                /**\r\n                 * Full year: `e.g. 1980...2010`\r\n                 * @return {number}\r\n                 */\r\n                Y: function () {\r\n                    return vDate.getFullYear();\r\n                },\r\n                /**\r\n                 * Last two digits of year: `00...99`\r\n                 * @return {string}\r\n                 */\r\n                y: function () {\r\n                    return fmt.Y().toString().slice(-2);\r\n                },\r\n\r\n                //////////\r\n                // TIME //\r\n                //////////\r\n                /**\r\n                 * Meridian lower: `am or pm`\r\n                 * @return {string}\r\n                 */\r\n                a: function () {\r\n                    return fmt.A().toLowerCase();\r\n                },\r\n                /**\r\n                 * Meridian upper: `AM or PM`\r\n                 * @return {string}\r\n                 */\r\n                A: function () {\r\n                    var n = fmt.G() < 12 ? 0 : 1;\r\n                    return vSettings.meridiem[n];\r\n                },\r\n                /**\r\n                 * Swatch Internet time: `000..999`\r\n                 * @return {string}\r\n                 */\r\n                B: function () {\r\n                    var H = vDate.getUTCHours() * $h.HOUR, i = vDate.getUTCMinutes() * 60, s = vDate.getUTCSeconds();\r\n                    return $h.lpad(Math.floor((H + i + s + $h.HOUR) / 86.4) % 1000, 3);\r\n                },\r\n                /**\r\n                 * 12-Hours: `1..12`\r\n                 * @return {number}\r\n                 */\r\n                g: function () {\r\n                    return fmt.G() % 12 || 12;\r\n                },\r\n                /**\r\n                 * 24-Hours: `0..23`\r\n                 * @return {number}\r\n                 */\r\n                G: function () {\r\n                    return vDate.getHours();\r\n                },\r\n                /**\r\n                 * 12-Hours with leading 0: `01..12`\r\n                 * @return {string}\r\n                 */\r\n                h: function () {\r\n                    return $h.lpad(fmt.g(), 2);\r\n                },\r\n                /**\r\n                 * 24-Hours w/leading 0: `00..23`\r\n                 * @return {string}\r\n                 */\r\n                H: function () {\r\n                    return $h.lpad(fmt.G(), 2);\r\n                },\r\n                /**\r\n                 * Minutes w/leading 0: `00..59`\r\n                 * @return {string}\r\n                 */\r\n                i: function () {\r\n                    return $h.lpad(vDate.getMinutes(), 2);\r\n                },\r\n                /**\r\n                 * Seconds w/leading 0: `00..59`\r\n                 * @return {string}\r\n                 */\r\n                s: function () {\r\n                    return $h.lpad(vDate.getSeconds(), 2);\r\n                },\r\n                /**\r\n                 * Microseconds: `000000-999000`\r\n                 * @return {string}\r\n                 */\r\n                u: function () {\r\n                    return $h.lpad(vDate.getMilliseconds() * 1000, 6);\r\n                },\r\n\r\n                //////////////\r\n                // TIMEZONE //\r\n                //////////////\r\n                /**\r\n                 * Timezone identifier: `e.g. Atlantic/Azores, ...`\r\n                 * @return {string}\r\n                 */\r\n                e: function () {\r\n                    var str = /\\((.*)\\)/.exec(String(vDate))[1];\r\n                    return str || 'Coordinated Universal Time';\r\n                },\r\n                /**\r\n                 * DST observed? `0 or 1`\r\n                 * @return {number}\r\n                 */\r\n                I: function () {\r\n                    var a = new Date(fmt.Y(), 0), c = Date.UTC(fmt.Y(), 0),\r\n                        b = new Date(fmt.Y(), 6), d = Date.UTC(fmt.Y(), 6);\r\n                    return ((a - c) !== (b - d)) ? 1 : 0;\r\n                },\r\n                /**\r\n                 * Difference to GMT in hour format: `e.g. +0200`\r\n                 * @return {string}\r\n                 */\r\n                O: function () {\r\n                    var tzo = vDate.getTimezoneOffset(), a = Math.abs(tzo);\r\n                    return (tzo > 0 ? '-' : '+') + $h.lpad(Math.floor(a / 60) * 100 + a % 60, 4);\r\n                },\r\n                /**\r\n                 * Difference to GMT with colon: `e.g. +02:00`\r\n                 * @return {string}\r\n                 */\r\n                P: function () {\r\n                    var O = fmt.O();\r\n                    return (O.substr(0, 3) + ':' + O.substr(3, 2));\r\n                },\r\n                /**\r\n                 * Timezone abbreviation: `e.g. EST, MDT, ...`\r\n                 * @return {string}\r\n                 */\r\n                T: function () {\r\n                    var str = (String(vDate).match(self.tzParts) || ['']).pop().replace(self.tzClip, '');\r\n                    return str || 'UTC';\r\n                },\r\n                /**\r\n                 * Timezone offset in seconds: `-43200...50400`\r\n                 * @return {number}\r\n                 */\r\n                Z: function () {\r\n                    return -vDate.getTimezoneOffset() * 60;\r\n                },\r\n\r\n                ////////////////////\r\n                // FULL DATE TIME //\r\n                ////////////////////\r\n                /**\r\n                 * ISO-8601 date\r\n                 * @return {string}\r\n                 */\r\n                c: function () {\r\n                    return 'Y-m-d\\\\TH:i:sP'.replace(backslash, doFormat);\r\n                },\r\n                /**\r\n                 * RFC 2822 date\r\n                 * @return {string}\r\n                 */\r\n                r: function () {\r\n                    return 'D, d M Y H:i:s O'.replace(backslash, doFormat);\r\n                },\r\n                /**\r\n                 * Seconds since UNIX epoch\r\n                 * @return {number}\r\n                 */\r\n                U: function () {\r\n                    return vDate.getTime() / 1000 || 0;\r\n                }\r\n            };\r\n            return doFormat(vChar, vChar);\r\n        },\r\n        formatDate: function (vDate, vFormat) {\r\n            var self = this, i, n, len, str, vChar, vDateStr = '', BACKSLASH = '\\\\';\r\n            if (typeof vDate === 'string') {\r\n                vDate = self.parseDate(vDate, vFormat);\r\n                if (!vDate) {\r\n                    return null;\r\n                }\r\n            }\r\n            if (vDate instanceof Date) {\r\n                len = vFormat.length;\r\n                for (i = 0; i < len; i++) {\r\n                    vChar = vFormat.charAt(i);\r\n                    if (vChar === 'S' || vChar === BACKSLASH) {\r\n                        continue;\r\n                    }\r\n                    if (i > 0 && vFormat.charAt(i - 1) === BACKSLASH) {\r\n                        vDateStr += vChar;\r\n                        continue;\r\n                    }\r\n                    str = self.parseFormat(vChar, vDate);\r\n                    if (i !== (len - 1) && self.intParts.test(vChar) && vFormat.charAt(i + 1) === 'S') {\r\n                        n = $h.getInt(str) || 0;\r\n                        str += self.dateSettings.ordinal(n);\r\n                    }\r\n                    vDateStr += str;\r\n                }\r\n                return vDateStr;\r\n            }\r\n            return '';\r\n        }\r\n    };\r\n    return DateFormatter;\r\n}));", "/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ \"./node_modules/locutus/php/array/array_diff.js\":\n/*!******************************************************!*\\\n  !*** ./node_modules/locutus/php/array/array_diff.js ***!\n  \\******************************************************/\n/***/ (function(module) {\n\n\n\nmodule.exports = function array_diff(arr1) {\n  //  discuss at: https://locutus.io/php/array_diff/\n  // original by: <PERSON> (https://kvz.io)\n  // improved by: <PERSON><PERSON>\n  //  revised by: <PERSON> (https://brett-zamir.me)\n  //   example 1: array_diff(['<PERSON>', 'van', '<PERSON><PERSON><PERSON><PERSON><PERSON>'], ['van', '<PERSON><PERSON><PERSON><PERSON><PERSON>'])\n  //   returns 1: {0:'<PERSON>'}\n\n  var retArr = {};\n  var argl = arguments.length;\n  var k1 = '';\n  var i = 1;\n  var k = '';\n  var arr = {};\n\n  arr1keys: for (k1 in arr1) {\n    for (i = 1; i < argl; i++) {\n      arr = arguments[i];\n      for (k in arr) {\n        if (arr[k] === arr1[k1]) {\n          // If it reaches here, it was found in at least one array, so try next value\n          continue arr1keys; // eslint-disable-line no-labels\n        }\n      }\n      retArr[k1] = arr1[k1];\n    }\n  }\n\n  return retArr;\n};\n//# sourceMappingURL=array_diff.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/datetime/strtotime.js\":\n/*!********************************************************!*\\\n  !*** ./node_modules/locutus/php/datetime/strtotime.js ***!\n  \\********************************************************/\n/***/ (function(module) {\n\n\n\nvar reSpace = '[ \\\\t]+';\nvar reSpaceOpt = '[ \\\\t]*';\nvar reMeridian = '(?:([ap])\\\\.?m\\\\.?([\\\\t ]|$))';\nvar reHour24 = '(2[0-4]|[01]?[0-9])';\nvar reHour24lz = '([01][0-9]|2[0-4])';\nvar reHour12 = '(0?[1-9]|1[0-2])';\nvar reMinute = '([0-5]?[0-9])';\nvar reMinutelz = '([0-5][0-9])';\nvar reSecond = '(60|[0-5]?[0-9])';\nvar reSecondlz = '(60|[0-5][0-9])';\nvar reFrac = '(?:\\\\.([0-9]+))';\n\nvar reDayfull = 'sunday|monday|tuesday|wednesday|thursday|friday|saturday';\nvar reDayabbr = 'sun|mon|tue|wed|thu|fri|sat';\nvar reDaytext = reDayfull + '|' + reDayabbr + '|weekdays?';\n\nvar reReltextnumber = 'first|second|third|fourth|fifth|sixth|seventh|eighth?|ninth|tenth|eleventh|twelfth';\nvar reReltexttext = 'next|last|previous|this';\nvar reReltextunit = '(?:second|sec|minute|min|hour|day|fortnight|forthnight|month|year)s?|weeks|' + reDaytext;\n\nvar reYear = '([0-9]{1,4})';\nvar reYear2 = '([0-9]{2})';\nvar reYear4 = '([0-9]{4})';\nvar reYear4withSign = '([+-]?[0-9]{4})';\nvar reMonth = '(1[0-2]|0?[0-9])';\nvar reMonthlz = '(0[0-9]|1[0-2])';\nvar reDay = '(?:(3[01]|[0-2]?[0-9])(?:st|nd|rd|th)?)';\nvar reDaylz = '(0[0-9]|[1-2][0-9]|3[01])';\n\nvar reMonthFull = 'january|february|march|april|may|june|july|august|september|october|november|december';\nvar reMonthAbbr = 'jan|feb|mar|apr|may|jun|jul|aug|sept?|oct|nov|dec';\nvar reMonthroman = 'i[vx]|vi{0,3}|xi{0,2}|i{1,3}';\nvar reMonthText = '(' + reMonthFull + '|' + reMonthAbbr + '|' + reMonthroman + ')';\n\nvar reTzCorrection = '((?:GMT)?([+-])' + reHour24 + ':?' + reMinute + '?)';\nvar reTzAbbr = '\\\\(?([a-zA-Z]{1,6})\\\\)?';\nvar reDayOfYear = '(00[1-9]|0[1-9][0-9]|[12][0-9][0-9]|3[0-5][0-9]|36[0-6])';\nvar reWeekOfYear = '(0[1-9]|[1-4][0-9]|5[0-3])';\n\nvar reDateNoYear = reMonthText + '[ .\\\\t-]*' + reDay + '[,.stndrh\\\\t ]*';\n\nfunction processMeridian(hour, meridian) {\n  meridian = meridian && meridian.toLowerCase();\n\n  switch (meridian) {\n    case 'a':\n      hour += hour === 12 ? -12 : 0;\n      break;\n    case 'p':\n      hour += hour !== 12 ? 12 : 0;\n      break;\n  }\n\n  return hour;\n}\n\nfunction processYear(yearStr) {\n  var year = +yearStr;\n\n  if (yearStr.length < 4 && year < 100) {\n    year += year < 70 ? 2000 : 1900;\n  }\n\n  return year;\n}\n\nfunction lookupMonth(monthStr) {\n  return {\n    jan: 0,\n    january: 0,\n    i: 0,\n    feb: 1,\n    february: 1,\n    ii: 1,\n    mar: 2,\n    march: 2,\n    iii: 2,\n    apr: 3,\n    april: 3,\n    iv: 3,\n    may: 4,\n    v: 4,\n    jun: 5,\n    june: 5,\n    vi: 5,\n    jul: 6,\n    july: 6,\n    vii: 6,\n    aug: 7,\n    august: 7,\n    viii: 7,\n    sep: 8,\n    sept: 8,\n    september: 8,\n    ix: 8,\n    oct: 9,\n    october: 9,\n    x: 9,\n    nov: 10,\n    november: 10,\n    xi: 10,\n    dec: 11,\n    december: 11,\n    xii: 11\n  }[monthStr.toLowerCase()];\n}\n\nfunction lookupWeekday(dayStr) {\n  var desiredSundayNumber = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n  var dayNumbers = {\n    mon: 1,\n    monday: 1,\n    tue: 2,\n    tuesday: 2,\n    wed: 3,\n    wednesday: 3,\n    thu: 4,\n    thursday: 4,\n    fri: 5,\n    friday: 5,\n    sat: 6,\n    saturday: 6,\n    sun: 0,\n    sunday: 0\n  };\n\n  return dayNumbers[dayStr.toLowerCase()] || desiredSundayNumber;\n}\n\nfunction lookupRelative(relText) {\n  var relativeNumbers = {\n    last: -1,\n    previous: -1,\n    this: 0,\n    first: 1,\n    next: 1,\n    second: 2,\n    third: 3,\n    fourth: 4,\n    fifth: 5,\n    sixth: 6,\n    seventh: 7,\n    eight: 8,\n    eighth: 8,\n    ninth: 9,\n    tenth: 10,\n    eleventh: 11,\n    twelfth: 12\n  };\n\n  var relativeBehavior = {\n    this: 1\n  };\n\n  var relTextLower = relText.toLowerCase();\n\n  return {\n    amount: relativeNumbers[relTextLower],\n    behavior: relativeBehavior[relTextLower] || 0\n  };\n}\n\nfunction processTzCorrection(tzOffset, oldValue) {\n  var reTzCorrectionLoose = /(?:GMT)?([+-])(\\d+)(:?)(\\d{0,2})/i;\n  tzOffset = tzOffset && tzOffset.match(reTzCorrectionLoose);\n\n  if (!tzOffset) {\n    return oldValue;\n  }\n\n  var sign = tzOffset[1] === '-' ? -1 : 1;\n  var hours = +tzOffset[2];\n  var minutes = +tzOffset[4];\n\n  if (!tzOffset[4] && !tzOffset[3]) {\n    minutes = Math.floor(hours % 100);\n    hours = Math.floor(hours / 100);\n  }\n\n  // timezone offset in seconds\n  return sign * (hours * 60 + minutes) * 60;\n}\n\n// tz abbrevation : tz offset in seconds\nvar tzAbbrOffsets = {\n  acdt: 37800,\n  acst: 34200,\n  addt: -7200,\n  adt: -10800,\n  aedt: 39600,\n  aest: 36000,\n  ahdt: -32400,\n  ahst: -36000,\n  akdt: -28800,\n  akst: -32400,\n  amt: -13840,\n  apt: -10800,\n  ast: -14400,\n  awdt: 32400,\n  awst: 28800,\n  awt: -10800,\n  bdst: 7200,\n  bdt: -36000,\n  bmt: -14309,\n  bst: 3600,\n  cast: 34200,\n  cat: 7200,\n  cddt: -14400,\n  cdt: -18000,\n  cemt: 10800,\n  cest: 7200,\n  cet: 3600,\n  cmt: -15408,\n  cpt: -18000,\n  cst: -21600,\n  cwt: -18000,\n  chst: 36000,\n  dmt: -1521,\n  eat: 10800,\n  eddt: -10800,\n  edt: -14400,\n  eest: 10800,\n  eet: 7200,\n  emt: -26248,\n  ept: -14400,\n  est: -18000,\n  ewt: -14400,\n  ffmt: -14660,\n  fmt: -4056,\n  gdt: 39600,\n  gmt: 0,\n  gst: 36000,\n  hdt: -34200,\n  hkst: 32400,\n  hkt: 28800,\n  hmt: -19776,\n  hpt: -34200,\n  hst: -36000,\n  hwt: -34200,\n  iddt: 14400,\n  idt: 10800,\n  imt: 25025,\n  ist: 7200,\n  jdt: 36000,\n  jmt: 8440,\n  jst: 32400,\n  kdt: 36000,\n  kmt: 5736,\n  kst: 30600,\n  lst: 9394,\n  mddt: -18000,\n  mdst: 16279,\n  mdt: -21600,\n  mest: 7200,\n  met: 3600,\n  mmt: 9017,\n  mpt: -21600,\n  msd: 14400,\n  msk: 10800,\n  mst: -25200,\n  mwt: -21600,\n  nddt: -5400,\n  ndt: -9052,\n  npt: -9000,\n  nst: -12600,\n  nwt: -9000,\n  nzdt: 46800,\n  nzmt: 41400,\n  nzst: 43200,\n  pddt: -21600,\n  pdt: -25200,\n  pkst: 21600,\n  pkt: 18000,\n  plmt: 25590,\n  pmt: -13236,\n  ppmt: -17340,\n  ppt: -25200,\n  pst: -28800,\n  pwt: -25200,\n  qmt: -18840,\n  rmt: 5794,\n  sast: 7200,\n  sdmt: -16800,\n  sjmt: -20173,\n  smt: -13884,\n  sst: -39600,\n  tbmt: 10751,\n  tmt: 12344,\n  uct: 0,\n  utc: 0,\n  wast: 7200,\n  wat: 3600,\n  wemt: 7200,\n  west: 3600,\n  wet: 0,\n  wib: 25200,\n  wita: 28800,\n  wit: 32400,\n  wmt: 5040,\n  yddt: -25200,\n  ydt: -28800,\n  ypt: -28800,\n  yst: -32400,\n  ywt: -28800,\n  a: 3600,\n  b: 7200,\n  c: 10800,\n  d: 14400,\n  e: 18000,\n  f: 21600,\n  g: 25200,\n  h: 28800,\n  i: 32400,\n  k: 36000,\n  l: 39600,\n  m: 43200,\n  n: -3600,\n  o: -7200,\n  p: -10800,\n  q: -14400,\n  r: -18000,\n  s: -21600,\n  t: -25200,\n  u: -28800,\n  v: -32400,\n  w: -36000,\n  x: -39600,\n  y: -43200,\n  z: 0\n};\n\nvar formats = {\n  yesterday: {\n    regex: /^yesterday/i,\n    name: 'yesterday',\n    callback: function callback() {\n      this.rd -= 1;\n      return this.resetTime();\n    }\n  },\n\n  now: {\n    regex: /^now/i,\n    name: 'now'\n    // do nothing\n  },\n\n  noon: {\n    regex: /^noon/i,\n    name: 'noon',\n    callback: function callback() {\n      return this.resetTime() && this.time(12, 0, 0, 0);\n    }\n  },\n\n  midnightOrToday: {\n    regex: /^(midnight|today)/i,\n    name: 'midnight | today',\n    callback: function callback() {\n      return this.resetTime();\n    }\n  },\n\n  tomorrow: {\n    regex: /^tomorrow/i,\n    name: 'tomorrow',\n    callback: function callback() {\n      this.rd += 1;\n      return this.resetTime();\n    }\n  },\n\n  timestamp: {\n    regex: /^@(-?\\d+)/i,\n    name: 'timestamp',\n    callback: function callback(match, timestamp) {\n      this.rs += +timestamp;\n      this.y = 1970;\n      this.m = 0;\n      this.d = 1;\n      this.dates = 0;\n\n      return this.resetTime() && this.zone(0);\n    }\n  },\n\n  firstOrLastDay: {\n    regex: /^(first|last) day of/i,\n    name: 'firstdayof | lastdayof',\n    callback: function callback(match, day) {\n      if (day.toLowerCase() === 'first') {\n        this.firstOrLastDayOfMonth = 1;\n      } else {\n        this.firstOrLastDayOfMonth = -1;\n      }\n    }\n  },\n\n  backOrFrontOf: {\n    regex: RegExp('^(back|front) of ' + reHour24 + reSpaceOpt + reMeridian + '?', 'i'),\n    name: 'backof | frontof',\n    callback: function callback(match, side, hours, meridian) {\n      var back = side.toLowerCase() === 'back';\n      var hour = +hours;\n      var minute = 15;\n\n      if (!back) {\n        hour -= 1;\n        minute = 45;\n      }\n\n      hour = processMeridian(hour, meridian);\n\n      return this.resetTime() && this.time(hour, minute, 0, 0);\n    }\n  },\n\n  weekdayOf: {\n    regex: RegExp('^(' + reReltextnumber + '|' + reReltexttext + ')' + reSpace + '(' + reDayfull + '|' + reDayabbr + ')' + reSpace + 'of', 'i'),\n    name: 'weekdayof'\n    // todo\n  },\n\n  mssqltime: {\n    regex: RegExp('^' + reHour12 + ':' + reMinutelz + ':' + reSecondlz + '[:.]([0-9]+)' + reMeridian, 'i'),\n    name: 'mssqltime',\n    callback: function callback(match, hour, minute, second, frac, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, +second, +frac.substr(0, 3));\n    }\n  },\n\n  oracledate: {\n    regex: /^(\\d{2})-([A-Z]{3})-(\\d{2})$/i,\n    name: 'd-M-y',\n    callback: function callback(match, day, monthText, year) {\n      var month = {\n        JAN: 0,\n        FEB: 1,\n        MAR: 2,\n        APR: 3,\n        MAY: 4,\n        JUN: 5,\n        JUL: 6,\n        AUG: 7,\n        SEP: 8,\n        OCT: 9,\n        NOV: 10,\n        DEC: 11\n      }[monthText.toUpperCase()];\n      return this.ymd(2000 + parseInt(year, 10), month, parseInt(day, 10));\n    }\n  },\n\n  timeLong12: {\n    regex: RegExp('^' + reHour12 + '[:.]' + reMinute + '[:.]' + reSecondlz + reSpaceOpt + reMeridian, 'i'),\n    name: 'timelong12',\n    callback: function callback(match, hour, minute, second, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, +second, 0);\n    }\n  },\n\n  timeShort12: {\n    regex: RegExp('^' + reHour12 + '[:.]' + reMinutelz + reSpaceOpt + reMeridian, 'i'),\n    name: 'timeshort12',\n    callback: function callback(match, hour, minute, meridian) {\n      return this.time(processMeridian(+hour, meridian), +minute, 0, 0);\n    }\n  },\n\n  timeTiny12: {\n    regex: RegExp('^' + reHour12 + reSpaceOpt + reMeridian, 'i'),\n    name: 'timetiny12',\n    callback: function callback(match, hour, meridian) {\n      return this.time(processMeridian(+hour, meridian), 0, 0, 0);\n    }\n  },\n\n  soap: {\n    regex: RegExp('^' + reYear4 + '-' + reMonthlz + '-' + reDaylz + 'T' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz + reFrac + reTzCorrection + '?', 'i'),\n    name: 'soap',\n    callback: function callback(match, year, month, day, hour, minute, second, frac, tzCorrection) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, +frac.substr(0, 3)) && this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  wddx: {\n    regex: RegExp('^' + reYear4 + '-' + reMonth + '-' + reDay + 'T' + reHour24 + ':' + reMinute + ':' + reSecond),\n    name: 'wddx',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  exif: {\n    regex: RegExp('^' + reYear4 + ':' + reMonthlz + ':' + reDaylz + ' ' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz, 'i'),\n    name: 'exif',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  xmlRpc: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz + 'T' + reHour24 + ':' + reMinutelz + ':' + reSecondlz),\n    name: 'xmlrpc',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  xmlRpcNoColon: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz + '[Tt]' + reHour24 + reMinutelz + reSecondlz),\n    name: 'xmlrpcnocolon',\n    callback: function callback(match, year, month, day, hour, minute, second) {\n      return this.ymd(+year, month - 1, +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  clf: {\n    regex: RegExp('^' + reDay + '/(' + reMonthAbbr + ')/' + reYear4 + ':' + reHour24lz + ':' + reMinutelz + ':' + reSecondlz + reSpace + reTzCorrection, 'i'),\n    name: 'clf',\n    callback: function callback(match, day, month, year, hour, minute, second, tzCorrection) {\n      return this.ymd(+year, lookupMonth(month), +day) && this.time(+hour, +minute, +second, 0) && this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  iso8601long: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond + reFrac, 'i'),\n    name: 'iso8601long',\n    callback: function callback(match, hour, minute, second, frac) {\n      return this.time(+hour, +minute, +second, +frac.substr(0, 3));\n    }\n  },\n\n  dateTextual: {\n    regex: RegExp('^' + reMonthText + '[ .\\\\t-]*' + reDay + '[,.stndrh\\\\t ]+' + reYear, 'i'),\n    name: 'datetextual',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  pointedDate4: {\n    regex: RegExp('^' + reDay + '[.\\\\t-]' + reMonth + '[.-]' + reYear4),\n    name: 'pointeddate4',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  pointedDate2: {\n    regex: RegExp('^' + reDay + '[.\\\\t]' + reMonth + '\\\\.' + reYear2),\n    name: 'pointeddate2',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  timeLong24: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond),\n    name: 'timelong24',\n    callback: function callback(match, hour, minute, second) {\n      return this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  dateNoColon: {\n    regex: RegExp('^' + reYear4 + reMonthlz + reDaylz),\n    name: 'datenocolon',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  pgydotd: {\n    regex: RegExp('^' + reYear4 + '\\\\.?' + reDayOfYear),\n    name: 'pgydotd',\n    callback: function callback(match, year, day) {\n      return this.ymd(+year, 0, +day);\n    }\n  },\n\n  timeShort24: {\n    regex: RegExp('^t?' + reHour24 + '[:.]' + reMinute, 'i'),\n    name: 'timeshort24',\n    callback: function callback(match, hour, minute) {\n      return this.time(+hour, +minute, 0, 0);\n    }\n  },\n\n  iso8601noColon: {\n    regex: RegExp('^t?' + reHour24lz + reMinutelz + reSecondlz, 'i'),\n    name: 'iso8601nocolon',\n    callback: function callback(match, hour, minute, second) {\n      return this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  iso8601dateSlash: {\n    // eventhough the trailing slash is optional in PHP\n    // here it's mandatory and inputs without the slash\n    // are handled by dateslash\n    regex: RegExp('^' + reYear4 + '/' + reMonthlz + '/' + reDaylz + '/'),\n    name: 'iso8601dateslash',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  dateSlash: {\n    regex: RegExp('^' + reYear4 + '/' + reMonth + '/' + reDay),\n    name: 'dateslash',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  american: {\n    regex: RegExp('^' + reMonth + '/' + reDay + '/' + reYear),\n    name: 'american',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  americanShort: {\n    regex: RegExp('^' + reMonth + '/' + reDay),\n    name: 'americanshort',\n    callback: function callback(match, month, day) {\n      return this.ymd(this.y, month - 1, +day);\n    }\n  },\n\n  gnuDateShortOrIso8601date2: {\n    // iso8601date2 is complete subset of gnudateshort\n    regex: RegExp('^' + reYear + '-' + reMonth + '-' + reDay),\n    name: 'gnudateshort | iso8601date2',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(processYear(year), month - 1, +day);\n    }\n  },\n\n  iso8601date4: {\n    regex: RegExp('^' + reYear4withSign + '-' + reMonthlz + '-' + reDaylz),\n    name: 'iso8601date4',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(+year, month - 1, +day);\n    }\n  },\n\n  gnuNoColon: {\n    regex: RegExp('^t?' + reHour24lz + reMinutelz, 'i'),\n    name: 'gnunocolon',\n    callback: function callback(match, hour, minute) {\n      // this rule is a special case\n      // if time was already set once by any preceding rule, it sets the captured value as year\n      switch (this.times) {\n        case 0:\n          return this.time(+hour, +minute, 0, this.f);\n        case 1:\n          this.y = hour * 100 + +minute;\n          this.times++;\n\n          return true;\n        default:\n          return false;\n      }\n    }\n  },\n\n  gnuDateShorter: {\n    regex: RegExp('^' + reYear4 + '-' + reMonth),\n    name: 'gnudateshorter',\n    callback: function callback(match, year, month) {\n      return this.ymd(+year, month - 1, 1);\n    }\n  },\n\n  pgTextReverse: {\n    // note: allowed years are from 32-9999\n    // years below 32 should be treated as days in datefull\n    regex: RegExp('^' + '(\\\\d{3,4}|[4-9]\\\\d|3[2-9])-(' + reMonthAbbr + ')-' + reDaylz, 'i'),\n    name: 'pgtextreverse',\n    callback: function callback(match, year, month, day) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateFull: {\n    regex: RegExp('^' + reDay + '[ \\\\t.-]*' + reMonthText + '[ \\\\t.-]*' + reYear, 'i'),\n    name: 'datefull',\n    callback: function callback(match, day, month, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateNoDay: {\n    regex: RegExp('^' + reMonthText + '[ .\\\\t-]*' + reYear4, 'i'),\n    name: 'datenoday',\n    callback: function callback(match, month, year) {\n      return this.ymd(+year, lookupMonth(month), 1);\n    }\n  },\n\n  dateNoDayRev: {\n    regex: RegExp('^' + reYear4 + '[ .\\\\t-]*' + reMonthText, 'i'),\n    name: 'datenodayrev',\n    callback: function callback(match, year, month) {\n      return this.ymd(+year, lookupMonth(month), 1);\n    }\n  },\n\n  pgTextShort: {\n    regex: RegExp('^(' + reMonthAbbr + ')-' + reDaylz + '-' + reYear, 'i'),\n    name: 'pgtextshort',\n    callback: function callback(match, month, day, year) {\n      return this.ymd(processYear(year), lookupMonth(month), +day);\n    }\n  },\n\n  dateNoYear: {\n    regex: RegExp('^' + reDateNoYear, 'i'),\n    name: 'datenoyear',\n    callback: function callback(match, month, day) {\n      return this.ymd(this.y, lookupMonth(month), +day);\n    }\n  },\n\n  dateNoYearRev: {\n    regex: RegExp('^' + reDay + '[ .\\\\t-]*' + reMonthText, 'i'),\n    name: 'datenoyearrev',\n    callback: function callback(match, day, month) {\n      return this.ymd(this.y, lookupMonth(month), +day);\n    }\n  },\n\n  isoWeekDay: {\n    regex: RegExp('^' + reYear4 + '-?W' + reWeekOfYear + '(?:-?([0-7]))?'),\n    name: 'isoweekday | isoweek',\n    callback: function callback(match, year, week, day) {\n      day = day ? +day : 1;\n\n      if (!this.ymd(+year, 0, 1)) {\n        return false;\n      }\n\n      // get day of week for Jan 1st\n      var dayOfWeek = new Date(this.y, this.m, this.d).getDay();\n\n      // and use the day to figure out the offset for day 1 of week 1\n      dayOfWeek = 0 - (dayOfWeek > 4 ? dayOfWeek - 7 : dayOfWeek);\n\n      this.rd += dayOfWeek + (week - 1) * 7 + day;\n    }\n  },\n\n  relativeText: {\n    regex: RegExp('^(' + reReltextnumber + '|' + reReltexttext + ')' + reSpace + '(' + reReltextunit + ')', 'i'),\n    name: 'relativetext',\n    callback: function callback(match, relValue, relUnit) {\n      // todo: implement handling of 'this time-unit'\n      // eslint-disable-next-line no-unused-vars\n      var _lookupRelative = lookupRelative(relValue),\n          amount = _lookupRelative.amount,\n          behavior = _lookupRelative.behavior;\n\n      switch (relUnit.toLowerCase()) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n          this.rs += amount;\n          break;\n        case 'min':\n        case 'mins':\n        case 'minute':\n        case 'minutes':\n          this.ri += amount;\n          break;\n        case 'hour':\n        case 'hours':\n          this.rh += amount;\n          break;\n        case 'day':\n        case 'days':\n          this.rd += amount;\n          break;\n        case 'fortnight':\n        case 'fortnights':\n        case 'forthnight':\n        case 'forthnights':\n          this.rd += amount * 14;\n          break;\n        case 'week':\n        case 'weeks':\n          this.rd += amount * 7;\n          break;\n        case 'month':\n        case 'months':\n          this.rm += amount;\n          break;\n        case 'year':\n        case 'years':\n          this.ry += amount;\n          break;\n        case 'mon':\n        case 'monday':\n        case 'tue':\n        case 'tuesday':\n        case 'wed':\n        case 'wednesday':\n        case 'thu':\n        case 'thursday':\n        case 'fri':\n        case 'friday':\n        case 'sat':\n        case 'saturday':\n        case 'sun':\n        case 'sunday':\n          this.resetTime();\n          this.weekday = lookupWeekday(relUnit, 7);\n          this.weekdayBehavior = 1;\n          this.rd += (amount > 0 ? amount - 1 : amount) * 7;\n          break;\n        case 'weekday':\n        case 'weekdays':\n          // todo\n          break;\n      }\n    }\n  },\n\n  relative: {\n    regex: RegExp('^([+-]*)[ \\\\t]*(\\\\d+)' + reSpaceOpt + '(' + reReltextunit + '|week)', 'i'),\n    name: 'relative',\n    callback: function callback(match, signs, relValue, relUnit) {\n      var minuses = signs.replace(/[^-]/g, '').length;\n\n      var amount = +relValue * Math.pow(-1, minuses);\n\n      switch (relUnit.toLowerCase()) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n          this.rs += amount;\n          break;\n        case 'min':\n        case 'mins':\n        case 'minute':\n        case 'minutes':\n          this.ri += amount;\n          break;\n        case 'hour':\n        case 'hours':\n          this.rh += amount;\n          break;\n        case 'day':\n        case 'days':\n          this.rd += amount;\n          break;\n        case 'fortnight':\n        case 'fortnights':\n        case 'forthnight':\n        case 'forthnights':\n          this.rd += amount * 14;\n          break;\n        case 'week':\n        case 'weeks':\n          this.rd += amount * 7;\n          break;\n        case 'month':\n        case 'months':\n          this.rm += amount;\n          break;\n        case 'year':\n        case 'years':\n          this.ry += amount;\n          break;\n        case 'mon':\n        case 'monday':\n        case 'tue':\n        case 'tuesday':\n        case 'wed':\n        case 'wednesday':\n        case 'thu':\n        case 'thursday':\n        case 'fri':\n        case 'friday':\n        case 'sat':\n        case 'saturday':\n        case 'sun':\n        case 'sunday':\n          this.resetTime();\n          this.weekday = lookupWeekday(relUnit, 7);\n          this.weekdayBehavior = 1;\n          this.rd += (amount > 0 ? amount - 1 : amount) * 7;\n          break;\n        case 'weekday':\n        case 'weekdays':\n          // todo\n          break;\n      }\n    }\n  },\n\n  dayText: {\n    regex: RegExp('^(' + reDaytext + ')', 'i'),\n    name: 'daytext',\n    callback: function callback(match, dayText) {\n      this.resetTime();\n      this.weekday = lookupWeekday(dayText, 0);\n\n      if (this.weekdayBehavior !== 2) {\n        this.weekdayBehavior = 1;\n      }\n    }\n  },\n\n  relativeTextWeek: {\n    regex: RegExp('^(' + reReltexttext + ')' + reSpace + 'week', 'i'),\n    name: 'relativetextweek',\n    callback: function callback(match, relText) {\n      this.weekdayBehavior = 2;\n\n      switch (relText.toLowerCase()) {\n        case 'this':\n          this.rd += 0;\n          break;\n        case 'next':\n          this.rd += 7;\n          break;\n        case 'last':\n        case 'previous':\n          this.rd -= 7;\n          break;\n      }\n\n      if (isNaN(this.weekday)) {\n        this.weekday = 1;\n      }\n    }\n  },\n\n  monthFullOrMonthAbbr: {\n    regex: RegExp('^(' + reMonthFull + '|' + reMonthAbbr + ')', 'i'),\n    name: 'monthfull | monthabbr',\n    callback: function callback(match, month) {\n      return this.ymd(this.y, lookupMonth(month), this.d);\n    }\n  },\n\n  tzCorrection: {\n    regex: RegExp('^' + reTzCorrection, 'i'),\n    name: 'tzcorrection',\n    callback: function callback(tzCorrection) {\n      return this.zone(processTzCorrection(tzCorrection));\n    }\n  },\n\n  tzAbbr: {\n    regex: RegExp('^' + reTzAbbr),\n    name: 'tzabbr',\n    callback: function callback(match, abbr) {\n      var offset = tzAbbrOffsets[abbr.toLowerCase()];\n\n      if (isNaN(offset)) {\n        return false;\n      }\n\n      return this.zone(offset);\n    }\n  },\n\n  ago: {\n    regex: /^ago/i,\n    name: 'ago',\n    callback: function callback() {\n      this.ry = -this.ry;\n      this.rm = -this.rm;\n      this.rd = -this.rd;\n      this.rh = -this.rh;\n      this.ri = -this.ri;\n      this.rs = -this.rs;\n      this.rf = -this.rf;\n    }\n  },\n\n  year4: {\n    regex: RegExp('^' + reYear4),\n    name: 'year4',\n    callback: function callback(match, year) {\n      this.y = +year;\n      return true;\n    }\n  },\n\n  whitespace: {\n    regex: /^[ .,\\t]+/,\n    name: 'whitespace'\n    // do nothing\n  },\n\n  dateShortWithTimeLong: {\n    regex: RegExp('^' + reDateNoYear + 't?' + reHour24 + '[:.]' + reMinute + '[:.]' + reSecond, 'i'),\n    name: 'dateshortwithtimelong',\n    callback: function callback(match, month, day, hour, minute, second) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(+hour, +minute, +second, 0);\n    }\n  },\n\n  dateShortWithTimeLong12: {\n    regex: RegExp('^' + reDateNoYear + reHour12 + '[:.]' + reMinute + '[:.]' + reSecondlz + reSpaceOpt + reMeridian, 'i'),\n    name: 'dateshortwithtimelong12',\n    callback: function callback(match, month, day, hour, minute, second, meridian) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(processMeridian(+hour, meridian), +minute, +second, 0);\n    }\n  },\n\n  dateShortWithTimeShort: {\n    regex: RegExp('^' + reDateNoYear + 't?' + reHour24 + '[:.]' + reMinute, 'i'),\n    name: 'dateshortwithtimeshort',\n    callback: function callback(match, month, day, hour, minute) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(+hour, +minute, 0, 0);\n    }\n  },\n\n  dateShortWithTimeShort12: {\n    regex: RegExp('^' + reDateNoYear + reHour12 + '[:.]' + reMinutelz + reSpaceOpt + reMeridian, 'i'),\n    name: 'dateshortwithtimeshort12',\n    callback: function callback(match, month, day, hour, minute, meridian) {\n      return this.ymd(this.y, lookupMonth(month), +day) && this.time(processMeridian(+hour, meridian), +minute, 0, 0);\n    }\n  }\n};\n\nvar resultProto = {\n  // date\n  y: NaN,\n  m: NaN,\n  d: NaN,\n  // time\n  h: NaN,\n  i: NaN,\n  s: NaN,\n  f: NaN,\n\n  // relative shifts\n  ry: 0,\n  rm: 0,\n  rd: 0,\n  rh: 0,\n  ri: 0,\n  rs: 0,\n  rf: 0,\n\n  // weekday related shifts\n  weekday: NaN,\n  weekdayBehavior: 0,\n\n  // first or last day of month\n  // 0 none, 1 first, -1 last\n  firstOrLastDayOfMonth: 0,\n\n  // timezone correction in minutes\n  z: NaN,\n\n  // counters\n  dates: 0,\n  times: 0,\n  zones: 0,\n\n  // helper functions\n  ymd: function ymd(y, m, d) {\n    if (this.dates > 0) {\n      return false;\n    }\n\n    this.dates++;\n    this.y = y;\n    this.m = m;\n    this.d = d;\n    return true;\n  },\n  time: function time(h, i, s, f) {\n    if (this.times > 0) {\n      return false;\n    }\n\n    this.times++;\n    this.h = h;\n    this.i = i;\n    this.s = s;\n    this.f = f;\n\n    return true;\n  },\n  resetTime: function resetTime() {\n    this.h = 0;\n    this.i = 0;\n    this.s = 0;\n    this.f = 0;\n    this.times = 0;\n\n    return true;\n  },\n  zone: function zone(minutes) {\n    if (this.zones <= 1) {\n      this.zones++;\n      this.z = minutes;\n      return true;\n    }\n\n    return false;\n  },\n  toDate: function toDate(relativeTo) {\n    if (this.dates && !this.times) {\n      this.h = this.i = this.s = this.f = 0;\n    }\n\n    // fill holes\n    if (isNaN(this.y)) {\n      this.y = relativeTo.getFullYear();\n    }\n\n    if (isNaN(this.m)) {\n      this.m = relativeTo.getMonth();\n    }\n\n    if (isNaN(this.d)) {\n      this.d = relativeTo.getDate();\n    }\n\n    if (isNaN(this.h)) {\n      this.h = relativeTo.getHours();\n    }\n\n    if (isNaN(this.i)) {\n      this.i = relativeTo.getMinutes();\n    }\n\n    if (isNaN(this.s)) {\n      this.s = relativeTo.getSeconds();\n    }\n\n    if (isNaN(this.f)) {\n      this.f = relativeTo.getMilliseconds();\n    }\n\n    // adjust special early\n    switch (this.firstOrLastDayOfMonth) {\n      case 1:\n        this.d = 1;\n        break;\n      case -1:\n        this.d = 0;\n        this.m += 1;\n        break;\n    }\n\n    if (!isNaN(this.weekday)) {\n      var date = new Date(relativeTo.getTime());\n      date.setFullYear(this.y, this.m, this.d);\n      date.setHours(this.h, this.i, this.s, this.f);\n\n      var dow = date.getDay();\n\n      if (this.weekdayBehavior === 2) {\n        // To make \"this week\" work, where the current day of week is a \"sunday\"\n        if (dow === 0 && this.weekday !== 0) {\n          this.weekday = -6;\n        }\n\n        // To make \"sunday this week\" work, where the current day of week is not a \"sunday\"\n        if (this.weekday === 0 && dow !== 0) {\n          this.weekday = 7;\n        }\n\n        this.d -= dow;\n        this.d += this.weekday;\n      } else {\n        var diff = this.weekday - dow;\n\n        // some PHP magic\n        if (this.rd < 0 && diff < 0 || this.rd >= 0 && diff <= -this.weekdayBehavior) {\n          diff += 7;\n        }\n\n        if (this.weekday >= 0) {\n          this.d += diff;\n        } else {\n          this.d -= 7 - (Math.abs(this.weekday) - dow);\n        }\n\n        this.weekday = NaN;\n      }\n    }\n\n    // adjust relative\n    this.y += this.ry;\n    this.m += this.rm;\n    this.d += this.rd;\n\n    this.h += this.rh;\n    this.i += this.ri;\n    this.s += this.rs;\n    this.f += this.rf;\n\n    this.ry = this.rm = this.rd = 0;\n    this.rh = this.ri = this.rs = this.rf = 0;\n\n    var result = new Date(relativeTo.getTime());\n    // since Date constructor treats years <= 99 as 1900+\n    // it can't be used, thus this weird way\n    result.setFullYear(this.y, this.m, this.d);\n    result.setHours(this.h, this.i, this.s, this.f);\n\n    // note: this is done twice in PHP\n    // early when processing special relatives\n    // and late\n    // todo: check if the logic can be reduced\n    // to just one time action\n    switch (this.firstOrLastDayOfMonth) {\n      case 1:\n        result.setDate(1);\n        break;\n      case -1:\n        result.setMonth(result.getMonth() + 1, 0);\n        break;\n    }\n\n    // adjust timezone\n    if (!isNaN(this.z) && result.getTimezoneOffset() !== this.z) {\n      result.setUTCFullYear(result.getFullYear(), result.getMonth(), result.getDate());\n\n      result.setUTCHours(result.getHours(), result.getMinutes(), result.getSeconds() - this.z, result.getMilliseconds());\n    }\n\n    return result;\n  }\n};\n\nmodule.exports = function strtotime(str, now) {\n  //       discuss at: https://locutus.io/php/strtotime/\n  //      original by: Caio Ariede (https://caioariede.com)\n  //      improved by: Kevin van Zonneveld (https://kvz.io)\n  //      improved by: Caio Ariede (https://caioariede.com)\n  //      improved by: A. Matías Quezada (https://amatiasq.com)\n  //      improved by: preuter\n  //      improved by: Brett Zamir (https://brett-zamir.me)\n  //      improved by: Mirko Faber\n  //         input by: David\n  //      bugfixed by: Wagner B. Soares\n  //      bugfixed by: Artur Tchernychev\n  //      bugfixed by: Stephan Bösch-Plepelits (https://github.com/plepe)\n  // reimplemented by: Rafał Kukawski\n  //           note 1: Examples all have a fixed timestamp to prevent\n  //           note 1: tests to fail because of variable time(zones)\n  //        example 1: strtotime('+1 day', 1129633200)\n  //        returns 1: 1129719600\n  //        example 2: strtotime('+1 week 2 days 4 hours 2 seconds', 1129633200)\n  //        returns 2: 1130425202\n  //        example 3: strtotime('last month', 1129633200)\n  //        returns 3: 1127041200\n  //        example 4: strtotime('2009-05-04 08:30:00+00')\n  //        returns 4: 1241425800\n  //        example 5: strtotime('2009-05-04 08:30:00+02:00')\n  //        returns 5: 1241418600\n  //        example 6: strtotime('2009-05-04 08:30:00 YWT')\n  //        returns 6: 1241454600\n  //        example 7: strtotime('10-JUL-17')\n  //        returns 7: 1499644800\n\n  if (now == null) {\n    now = Math.floor(Date.now() / 1000);\n  }\n\n  // the rule order is important\n  // if multiple rules match, the longest match wins\n  // if multiple rules match the same string, the first match wins\n  var rules = [formats.yesterday, formats.now, formats.noon, formats.midnightOrToday, formats.tomorrow, formats.timestamp, formats.firstOrLastDay, formats.backOrFrontOf,\n  // formats.weekdayOf, // not yet implemented\n  formats.timeTiny12, formats.timeShort12, formats.timeLong12, formats.mssqltime, formats.oracledate, formats.timeShort24, formats.timeLong24, formats.iso8601long, formats.gnuNoColon, formats.iso8601noColon, formats.americanShort, formats.american, formats.iso8601date4, formats.iso8601dateSlash, formats.dateSlash, formats.gnuDateShortOrIso8601date2, formats.gnuDateShorter, formats.dateFull, formats.pointedDate4, formats.pointedDate2, formats.dateNoDay, formats.dateNoDayRev, formats.dateTextual, formats.dateNoYear, formats.dateNoYearRev, formats.dateNoColon, formats.xmlRpc, formats.xmlRpcNoColon, formats.soap, formats.wddx, formats.exif, formats.pgydotd, formats.isoWeekDay, formats.pgTextShort, formats.pgTextReverse, formats.clf, formats.year4, formats.ago, formats.dayText, formats.relativeTextWeek, formats.relativeText, formats.monthFullOrMonthAbbr, formats.tzCorrection, formats.tzAbbr, formats.dateShortWithTimeShort12, formats.dateShortWithTimeLong12, formats.dateShortWithTimeShort, formats.dateShortWithTimeLong, formats.relative, formats.whitespace];\n\n  var result = Object.create(resultProto);\n\n  while (str.length) {\n    var longestMatch = null;\n    var finalRule = null;\n\n    for (var i = 0, l = rules.length; i < l; i++) {\n      var format = rules[i];\n\n      var match = str.match(format.regex);\n\n      if (match) {\n        if (!longestMatch || match[0].length > longestMatch[0].length) {\n          longestMatch = match;\n          finalRule = format;\n        }\n      }\n    }\n\n    if (!finalRule || finalRule.callback && finalRule.callback.apply(result, longestMatch) === false) {\n      return false;\n    }\n\n    str = str.substr(longestMatch[0].length);\n    finalRule = null;\n    longestMatch = null;\n  }\n\n  return Math.floor(result.toDate(new Date(now * 1000)) / 1000);\n};\n//# sourceMappingURL=strtotime.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/info/ini_get.js\":\n/*!**************************************************!*\\\n  !*** ./node_modules/locutus/php/info/ini_get.js ***!\n  \\**************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n\n\nmodule.exports = function ini_get(varname) {\n  //  discuss at: https://locutus.io/php/ini_get/\n  // original by: Brett Zamir (https://brett-zamir.me)\n  //      note 1: The ini values must be set by ini_set or manually within an ini file\n  //   example 1: ini_set('date.timezone', 'Asia/Hong_Kong')\n  //   example 1: ini_get('date.timezone')\n  //   returns 1: 'Asia/Hong_Kong'\n\n  var $global = typeof window !== 'undefined' ? window : __webpack_require__.g;\n  $global.$locutus = $global.$locutus || {};\n  var $locutus = $global.$locutus;\n  $locutus.php = $locutus.php || {};\n  $locutus.php.ini = $locutus.php.ini || {};\n\n  if ($locutus.php.ini[varname] && $locutus.php.ini[varname].local_value !== undefined) {\n    if ($locutus.php.ini[varname].local_value === null) {\n      return '';\n    }\n    return $locutus.php.ini[varname].local_value;\n  }\n\n  return '';\n};\n//# sourceMappingURL=ini_get.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/strings/strlen.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/locutus/php/strings/strlen.js ***!\n  \\****************************************************/\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n\n\nmodule.exports = function strlen(string) {\n  //  discuss at: https://locutus.io/php/strlen/\n  // original by: Kevin van Zonneveld (https://kvz.io)\n  // improved by: Sakimori\n  // improved by: Kevin van Zonneveld (https://kvz.io)\n  //    input by: Kirk Strobeck\n  // bugfixed by: Onno Marsman (https://twitter.com/onnomarsman)\n  //  revised by: Brett Zamir (https://brett-zamir.me)\n  //      note 1: May look like overkill, but in order to be truly faithful to handling all Unicode\n  //      note 1: characters and to this function in PHP which does not count the number of bytes\n  //      note 1: but counts the number of characters, something like this is really necessary.\n  //   example 1: strlen('Kevin van Zonneveld')\n  //   returns 1: 19\n  //   example 2: ini_set('unicode.semantics', 'on')\n  //   example 2: strlen('A\\ud87e\\udc04Z')\n  //   returns 2: 3\n\n  var str = string + '';\n\n  var iniVal = ( true ? __webpack_require__(/*! ../info/ini_get */ \"./node_modules/locutus/php/info/ini_get.js\")('unicode.semantics') : 0) || 'off';\n  if (iniVal === 'off') {\n    return str.length;\n  }\n\n  var i = 0;\n  var lgth = 0;\n\n  var getWholeChar = function getWholeChar(str, i) {\n    var code = str.charCodeAt(i);\n    var next = '';\n    var prev = '';\n    if (code >= 0xd800 && code <= 0xdbff) {\n      // High surrogate (could change last hex to 0xDB7F to\n      // treat high private surrogates as single characters)\n      if (str.length <= i + 1) {\n        throw new Error('High surrogate without following low surrogate');\n      }\n      next = str.charCodeAt(i + 1);\n      if (next < 0xdc00 || next > 0xdfff) {\n        throw new Error('High surrogate without following low surrogate');\n      }\n      return str.charAt(i) + str.charAt(i + 1);\n    } else if (code >= 0xdc00 && code <= 0xdfff) {\n      // Low surrogate\n      if (i === 0) {\n        throw new Error('Low surrogate without preceding high surrogate');\n      }\n      prev = str.charCodeAt(i - 1);\n      if (prev < 0xd800 || prev > 0xdbff) {\n        // (could change last hex to 0xDB7F to treat high private surrogates\n        // as single characters)\n        throw new Error('Low surrogate without preceding high surrogate');\n      }\n      // We can pass over low surrogates now as the second\n      // component in a pair which we have already processed\n      return false;\n    }\n    return str.charAt(i);\n  };\n\n  for (i = 0, lgth = 0; i < str.length; i++) {\n    if (getWholeChar(str, i) === false) {\n      continue;\n    }\n    // Adapt this line at the top of any loop, passing in the whole string and\n    // the current iteration and returning a variable to represent the individual character;\n    // purpose is to treat the first part of a surrogate pair as the whole character and then\n    // ignore the second part\n    lgth++;\n  }\n\n  return lgth;\n};\n//# sourceMappingURL=strlen.js.map\n\n/***/ }),\n\n/***/ \"./node_modules/locutus/php/var/is_numeric.js\":\n/*!****************************************************!*\\\n  !*** ./node_modules/locutus/php/var/is_numeric.js ***!\n  \\****************************************************/\n/***/ (function(module) {\n\n\n\nmodule.exports = function is_numeric(mixedVar) {\n  //  discuss at: https://locutus.io/php/is_numeric/\n  // original by: Kevin van Zonneveld (https://kvz.io)\n  // improved by: David\n  // improved by: taith\n  // bugfixed by: Tim de Koning\n  // bugfixed by: WebDevHobo (https://webdevhobo.blogspot.com/)\n  // bugfixed by: Brett Zamir (https://brett-zamir.me)\n  // bugfixed by: Denis Chenu (https://shnoulle.net)\n  //   example 1: is_numeric(186.31)\n  //   returns 1: true\n  //   example 2: is_numeric('Kevin van Zonneveld')\n  //   returns 2: false\n  //   example 3: is_numeric(' +186.31e2')\n  //   returns 3: true\n  //   example 4: is_numeric('')\n  //   returns 4: false\n  //   example 5: is_numeric([])\n  //   returns 5: false\n  //   example 6: is_numeric('1 ')\n  //   returns 6: false\n\n  var whitespace = [' ', '\\n', '\\r', '\\t', '\\f', '\\x0b', '\\xa0', '\\u2000', '\\u2001', '\\u2002', '\\u2003', '\\u2004', '\\u2005', '\\u2006', '\\u2007', '\\u2008', '\\u2009', '\\u200A', '\\u200B', '\\u2028', '\\u2029', '\\u3000'].join('');\n\n  // @todo: Break this up using many single conditions with early returns\n  return (typeof mixedVar === 'number' || typeof mixedVar === 'string' && whitespace.indexOf(mixedVar.slice(-1)) === -1) && mixedVar !== '' && !isNaN(mixedVar);\n};\n//# sourceMappingURL=is_numeric.js.map\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/global */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.g = (function() {\n/******/ \t\t\tif (typeof globalThis === 'object') return globalThis;\n/******/ \t\t\ttry {\n/******/ \t\t\t\treturn this || new Function('return this')();\n/******/ \t\t\t} catch (e) {\n/******/ \t\t\t\tif (typeof window === 'object') return window;\n/******/ \t\t\t}\n/******/ \t\t})();\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/make namespace object */\n/******/ \t!function() {\n/******/ \t\t// define __esModule on exports\n/******/ \t\t__webpack_require__.r = function(exports) {\n/******/ \t\t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t\t}\n/******/ \t\t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n/*!****************************************!*\\\n  !*** ./resources/assets/js/helpers.js ***!\n  \\****************************************/\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! locutus/php/strings/strlen */ \"./node_modules/locutus/php/strings/strlen.js\");\n/* harmony import */ var locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! locutus/php/array/array_diff */ \"./node_modules/locutus/php/array/array_diff.js\");\n/* harmony import */ var locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! locutus/php/datetime/strtotime */ \"./node_modules/locutus/php/datetime/strtotime.js\");\n/* harmony import */ var locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! locutus/php/var/is_numeric */ \"./node_modules/locutus/php/var/is_numeric.js\");\n/* harmony import */ var locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3__);\n/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Helper functions used by validators\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n\n\n\n\n$.extend(true, laravelValidation, {\n  helpers: {\n    /**\n     * Numeric rules\n     */\n    numericRules: ['Integer', 'Numeric'],\n    /**\n     * Gets the file information from file input.\n     *\n     * @param fieldObj\n     * @param index\n     * @returns {{file: *, extension: string, size: number}}\n     */\n    fileinfo: function (fieldObj, index) {\n      var FileName = fieldObj.value;\n      index = typeof index !== 'undefined' ? index : 0;\n      if (fieldObj.files !== null) {\n        if (typeof fieldObj.files[index] !== 'undefined') {\n          return {\n            file: FileName,\n            extension: FileName.substr(FileName.lastIndexOf('.') + 1),\n            size: fieldObj.files[index].size / 1024,\n            type: fieldObj.files[index].type\n          };\n        }\n      }\n      return false;\n    },\n    /**\n     * Gets the selectors for th specified field names.\n     *\n     * @param names\n     * @returns {string}\n     */\n    selector: function (names) {\n      var selector = [];\n      if (!this.isArray(names)) {\n        names = [names];\n      }\n      for (var i = 0; i < names.length; i++) {\n        selector.push(\"[name='\" + names[i] + \"']\");\n      }\n      return selector.join();\n    },\n    /**\n     * Check if element has numeric rules.\n     *\n     * @param element\n     * @returns {boolean}\n     */\n    hasNumericRules: function (element) {\n      return this.hasRules(element, this.numericRules);\n    },\n    /**\n     * Check if element has passed rules.\n     *\n     * @param element\n     * @param rules\n     * @returns {boolean}\n     */\n    hasRules: function (element, rules) {\n      var found = false;\n      if (typeof rules === 'string') {\n        rules = [rules];\n      }\n      var validator = $.data(element.form, \"validator\");\n      var listRules = [];\n      var cache = validator.arrayRulesCache;\n      if (element.name in cache) {\n        $.each(cache[element.name], function (index, arrayRule) {\n          listRules.push(arrayRule);\n        });\n      }\n      if (element.name in validator.settings.rules) {\n        listRules.push(validator.settings.rules[element.name]);\n      }\n      $.each(listRules, function (index, objRules) {\n        if ('laravelValidation' in objRules) {\n          var _rules = objRules.laravelValidation;\n          for (var i = 0; i < _rules.length; i++) {\n            if ($.inArray(_rules[i][0], rules) !== -1) {\n              found = true;\n              return false;\n            }\n          }\n        }\n      });\n      return found;\n    },\n    /**\n     * Return the string length using PHP function.\n     * http://php.net/manual/en/function.strlen.php\n     * http://phpjs.org/functions/strlen/\n     *\n     * @param string\n     */\n    strlen: function (string) {\n      return locutus_php_strings_strlen__WEBPACK_IMPORTED_MODULE_0___default()(string);\n    },\n    /**\n     * Get the size of the object depending of his type.\n     *\n     * @param obj\n     * @param element\n     * @param value\n     * @returns int\n     */\n    getSize: function getSize(obj, element, value) {\n      if (this.hasNumericRules(element) && this.is_numeric(value)) {\n        return parseFloat(value);\n      } else if (this.isArray(value)) {\n        return parseFloat(value.length);\n      } else if (element.type === 'file') {\n        return parseFloat(Math.floor(this.fileinfo(element).size));\n      }\n      return parseFloat(this.strlen(value));\n    },\n    /**\n     * Return specified rule from element.\n     *\n     * @param rule\n     * @param element\n     * @returns object\n     */\n    getLaravelValidation: function (rule, element) {\n      var found = undefined;\n      $.each($.validator.staticRules(element), function (key, rules) {\n        if (key === \"laravelValidation\") {\n          $.each(rules, function (i, value) {\n            if (value[0] === rule) {\n              found = value;\n            }\n          });\n        }\n      });\n      return found;\n    },\n    /**\n     * Return he timestamp of value passed using format or default format in element.\n     *\n     * @param value\n     * @param format\n     * @returns {boolean|int}\n     */\n    parseTime: function (value, format) {\n      var timeValue = false;\n      var fmt = new DateFormatter();\n      if (typeof value === 'number' && typeof format === 'undefined') {\n        return value;\n      }\n      if (typeof format === 'object') {\n        var dateRule = this.getLaravelValidation('DateFormat', format);\n        if (dateRule !== undefined) {\n          format = dateRule[1][0];\n        } else {\n          format = null;\n        }\n      }\n      if (format == null) {\n        timeValue = this.strtotime(value);\n      } else {\n        timeValue = fmt.parseDate(value, format);\n        if (timeValue instanceof Date && fmt.formatDate(timeValue, format) === value) {\n          timeValue = Math.round(timeValue.getTime() / 1000);\n        } else {\n          timeValue = false;\n        }\n      }\n      return timeValue;\n    },\n    /**\n     * Compare a given date against another using an operator.\n     *\n     * @param validator\n     * @param value\n     * @param element\n     * @param params\n     * @param operator\n     * @return {boolean}\n     */\n    compareDates: function (validator, value, element, params, operator) {\n      var timeCompare = this.parseTime(params);\n      if (!timeCompare) {\n        var target = this.dependentElement(validator, element, params);\n        if (target === undefined) {\n          return false;\n        }\n        timeCompare = this.parseTime(validator.elementValue(target), target);\n      }\n      var timeValue = this.parseTime(value, element);\n      if (timeValue === false) {\n        return false;\n      }\n      switch (operator) {\n        case '<':\n          return timeValue < timeCompare;\n        case '<=':\n          return timeValue <= timeCompare;\n        case '==':\n        case '===':\n          return timeValue === timeCompare;\n        case '>':\n          return timeValue > timeCompare;\n        case '>=':\n          return timeValue >= timeCompare;\n        default:\n          throw new Error('Unsupported operator.');\n      }\n    },\n    /**\n     * This method allows you to intelligently guess the date by closely matching the specific format.\n     *\n     * @param value\n     * @param format\n     * @returns {Date}\n     */\n    guessDate: function (value, format) {\n      var fmt = new DateFormatter();\n      return fmt.guessDate(value, format);\n    },\n    /**\n     * Returns Unix timestamp based on PHP function strototime.\n     * http://php.net/manual/es/function.strtotime.php\n     * http://phpjs.org/functions/strtotime/\n     *\n     * @param text\n     * @param now\n     * @returns {*}\n     */\n    strtotime: function (text, now) {\n      return locutus_php_datetime_strtotime__WEBPACK_IMPORTED_MODULE_2___default()(text, now);\n    },\n    /**\n     * Returns if value is numeric.\n     * http://php.net/manual/es/var.is_numeric.php\n     * http://phpjs.org/functions/is_numeric/\n     *\n     * @param mixed_var\n     * @returns {*}\n     */\n    is_numeric: function (mixed_var) {\n      return locutus_php_var_is_numeric__WEBPACK_IMPORTED_MODULE_3___default()(mixed_var);\n    },\n    /**\n     * Check whether the argument is of type Array.\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray#Polyfill\n     *\n     * @param arg\n     * @returns {boolean}\n     */\n    isArray: function (arg) {\n      return Object.prototype.toString.call(arg) === '[object Array]';\n    },\n    /**\n     * Returns Array diff based on PHP function array_diff.\n     * http://php.net/manual/es/function.array_diff.php\n     * http://phpjs.org/functions/array_diff/\n     *\n     * @param arr1\n     * @param arr2\n     * @returns {*}\n     */\n    arrayDiff: function (arr1, arr2) {\n      return locutus_php_array_array_diff__WEBPACK_IMPORTED_MODULE_1___default()(arr1, arr2);\n    },\n    /**\n     * Check whether two arrays are equal to one another.\n     *\n     * @param arr1\n     * @param arr2\n     * @returns {*}\n     */\n    arrayEquals: function (arr1, arr2) {\n      if (!this.isArray(arr1) || !this.isArray(arr2)) {\n        return false;\n      }\n      if (arr1.length !== arr2.length) {\n        return false;\n      }\n      return $.isEmptyObject(this.arrayDiff(arr1, arr2));\n    },\n    /**\n     * Makes element dependant from other.\n     *\n     * @param validator\n     * @param element\n     * @param name\n     * @returns {*}\n     */\n    dependentElement: function (validator, element, name) {\n      var el = validator.findByName(name);\n      if (el[0] !== undefined && validator.settings.onfocusout) {\n        var event = 'blur';\n        if (el[0].tagName === 'SELECT' || el[0].tagName === 'OPTION' || el[0].type === 'checkbox' || el[0].type === 'radio') {\n          event = 'click';\n        }\n        var ruleName = '.validate-laravelValidation';\n        el.off(ruleName).off(event + ruleName + '-' + element.name).on(event + ruleName + '-' + element.name, function () {\n          $(element).valid();\n        });\n      }\n      return el[0];\n    },\n    /**\n     * Parses error Ajax response and gets the message.\n     *\n     * @param response\n     * @returns {string[]}\n     */\n    parseErrorResponse: function (response) {\n      var newResponse = ['Whoops, looks like something went wrong.'];\n      if ('responseText' in response) {\n        var errorMsg = response.responseText.match(/<h1\\s*>(.*)<\\/h1\\s*>/i);\n        if (this.isArray(errorMsg)) {\n          newResponse = [errorMsg[1]];\n        }\n      }\n      return newResponse;\n    },\n    /**\n     * Escape string to use as Regular Expression.\n     *\n     * @param str\n     * @returns string\n     */\n    escapeRegExp: function (str) {\n      return str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\");\n    },\n    /**\n     * Generate RegExp from wildcard attributes.\n     *\n     * @param name\n     * @returns {RegExp}\n     */\n    regexFromWildcard: function (name) {\n      var nameParts = name.split('[*]');\n      if (nameParts.length === 1) nameParts.push('');\n      return new RegExp('^' + nameParts.map(function (x) {\n        return laravelValidation.helpers.escapeRegExp(x);\n      }).join('\\\\[[^\\\\]]*\\\\]') + '$');\n    },\n    /**\n     * Merge additional laravel validation rules into the current rule set.\n     *\n     * @param {object} rules\n     * @param {object} newRules\n     * @returns {object}\n     */\n    mergeRules: function (rules, newRules) {\n      var rulesList = {\n        'laravelValidation': newRules.laravelValidation || [],\n        'laravelValidationRemote': newRules.laravelValidationRemote || []\n      };\n      for (var key in rulesList) {\n        if (rulesList[key].length === 0) {\n          continue;\n        }\n        if (typeof rules[key] === \"undefined\") {\n          rules[key] = [];\n        }\n        rules[key] = rules[key].concat(rulesList[key]);\n      }\n      return rules;\n    },\n    /**\n     * HTML entity encode a string.\n     *\n     * @param string\n     * @returns {string}\n     */\n    encode: function (string) {\n      return $('<div/>').text(string).html();\n    },\n    /**\n     * Lookup name in an array.\n     *\n     * @param validator\n     * @param {string} name Name in dot notation format.\n     * @returns {*}\n     */\n    findByArrayName: function (validator, name) {\n      var sqName = name.replace(/\\.([^\\.]+)/g, '[$1]'),\n        lookups = [\n        // Convert dot to square brackets. e.g. foo.bar.0 becomes foo[bar][0]\n        sqName,\n        // Append [] to the name e.g. foo becomes foo[] or foo.bar.0 becomes foo[bar][0][]\n        sqName + '[]',\n        // Remove key from last array e.g. foo[bar][0] becomes foo[bar][]\n        sqName.replace(/(.*)\\[(.*)\\]$/g, '$1[]')];\n      for (var i = 0; i < lookups.length; i++) {\n        var elem = validator.findByName(lookups[i]);\n        if (elem.length > 0) {\n          return elem;\n        }\n      }\n      return $(null);\n    },\n    /**\n     * Attempt to find an element in the DOM matching the given name.\n     * Example names include:\n     *    - domain.0 which matches domain[]\n     *    - customfield.3 which matches customfield[3]\n     *\n     * @param validator\n     * @param {string} name\n     * @returns {*}\n     */\n    findByName: function (validator, name) {\n      // Exact match.\n      var elem = validator.findByName(name);\n      if (elem.length > 0) {\n        return elem;\n      }\n\n      // Find name in data, using dot notation.\n      var delim = '.',\n        parts = name.split(delim);\n      for (var i = parts.length; i > 0; i--) {\n        var reconstructed = [];\n        for (var c = 0; c < i; c++) {\n          reconstructed.push(parts[c]);\n        }\n        elem = this.findByArrayName(validator, reconstructed.join(delim));\n        if (elem.length > 0) {\n          return elem;\n        }\n      }\n      return $(null);\n    },\n    /**\n     * If it's an array element, get all values.\n     *\n     * @param validator\n     * @param element\n     * @returns {*|string}\n     */\n    allElementValues: function (validator, element) {\n      if (element.name.indexOf('[]') !== -1) {\n        return validator.findByName(element.name).map(function (i, e) {\n          return validator.elementValue(e);\n        }).get();\n      }\n      return validator.elementValue(element);\n    }\n  }\n});\n/******/ })()\n;\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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", "/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Timezone Helper functions used by validators\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n$.extend(true, laravelValidation, {\n\n    helpers: {\n\n        /**\n         * Check if the specified timezone is valid.\n         *\n         * @param value\n         * @returns {boolean}\n         */\n        isTimezone: function (value) {\n\n            var timezones={\n                \"africa\": [\n                    \"abidjan\",\n                    \"accra\",\n                    \"addis_ababa\",\n                    \"algiers\",\n                    \"asmara\",\n                    \"bamako\",\n                    \"bangui\",\n                    \"banjul\",\n                    \"bissau\",\n                    \"blantyre\",\n                    \"brazzaville\",\n                    \"bujumbura\",\n                    \"cairo\",\n                    \"casablanca\",\n                    \"ceuta\",\n                    \"conakry\",\n                    \"dakar\",\n                    \"dar_es_salaam\",\n                    \"djibouti\",\n                    \"douala\",\n                    \"el_aaiun\",\n                    \"freetown\",\n                    \"gaborone\",\n                    \"harare\",\n                    \"johannesburg\",\n                    \"juba\",\n                    \"kampala\",\n                    \"khartoum\",\n                    \"kigali\",\n                    \"kinshasa\",\n                    \"lagos\",\n                    \"libreville\",\n                    \"lome\",\n                    \"luanda\",\n                    \"lubumbashi\",\n                    \"lusaka\",\n                    \"malabo\",\n                    \"maputo\",\n                    \"maseru\",\n                    \"mbabane\",\n                    \"mogadishu\",\n                    \"monrovia\",\n                    \"nairobi\",\n                    \"ndjamena\",\n                    \"niamey\",\n                    \"nouakchott\",\n                    \"ouagadougou\",\n                    \"porto-novo\",\n                    \"sao_tome\",\n                    \"tripoli\",\n                    \"tunis\",\n                    \"windhoek\"\n                ],\n                \"america\": [\n                    \"adak\",\n                    \"anchorage\",\n                    \"anguilla\",\n                    \"antigua\",\n                    \"araguaina\",\n                    \"argentina\\/buenos_aires\",\n                    \"argentina\\/catamarca\",\n                    \"argentina\\/cordoba\",\n                    \"argentina\\/jujuy\",\n                    \"argentina\\/la_rioja\",\n                    \"argentina\\/mendoza\",\n                    \"argentina\\/rio_gallegos\",\n                    \"argentina\\/salta\",\n                    \"argentina\\/san_juan\",\n                    \"argentina\\/san_luis\",\n                    \"argentina\\/tucuman\",\n                    \"argentina\\/ushuaia\",\n                    \"aruba\",\n                    \"asuncion\",\n                    \"atikokan\",\n                    \"bahia\",\n                    \"bahia_banderas\",\n                    \"barbados\",\n                    \"belem\",\n                    \"belize\",\n                    \"blanc-sablon\",\n                    \"boa_vista\",\n                    \"bogota\",\n                    \"boise\",\n                    \"cambridge_bay\",\n                    \"campo_grande\",\n                    \"cancun\",\n                    \"caracas\",\n                    \"cayenne\",\n                    \"cayman\",\n                    \"chicago\",\n                    \"chihuahua\",\n                    \"costa_rica\",\n                    \"creston\",\n                    \"cuiaba\",\n                    \"curacao\",\n                    \"danmarkshavn\",\n                    \"dawson\",\n                    \"dawson_creek\",\n                    \"denver\",\n                    \"detroit\",\n                    \"dominica\",\n                    \"edmonton\",\n                    \"eirunepe\",\n                    \"el_salvador\",\n                    \"fortaleza\",\n                    \"glace_bay\",\n                    \"godthab\",\n                    \"goose_bay\",\n                    \"grand_turk\",\n                    \"grenada\",\n                    \"guadeloupe\",\n                    \"guatemala\",\n                    \"guayaquil\",\n                    \"guyana\",\n                    \"halifax\",\n                    \"havana\",\n                    \"hermosillo\",\n                    \"indiana\\/indianapolis\",\n                    \"indiana\\/knox\",\n                    \"indiana\\/marengo\",\n                    \"indiana\\/petersburg\",\n                    \"indiana\\/tell_city\",\n                    \"indiana\\/vevay\",\n                    \"indiana\\/vincennes\",\n                    \"indiana\\/winamac\",\n                    \"inuvik\",\n                    \"iqaluit\",\n                    \"jamaica\",\n                    \"juneau\",\n                    \"kentucky\\/louisville\",\n                    \"kentucky\\/monticello\",\n                    \"kralendijk\",\n                    \"la_paz\",\n                    \"lima\",\n                    \"los_angeles\",\n                    \"lower_princes\",\n                    \"maceio\",\n                    \"managua\",\n                    \"manaus\",\n                    \"marigot\",\n                    \"martinique\",\n                    \"matamoros\",\n                    \"mazatlan\",\n                    \"menominee\",\n                    \"merida\",\n                    \"metlakatla\",\n                    \"mexico_city\",\n                    \"miquelon\",\n                    \"moncton\",\n                    \"monterrey\",\n                    \"montevideo\",\n                    \"montreal\",\n                    \"montserrat\",\n                    \"nassau\",\n                    \"new_york\",\n                    \"nipigon\",\n                    \"nome\",\n                    \"noronha\",\n                    \"north_dakota\\/beulah\",\n                    \"north_dakota\\/center\",\n                    \"north_dakota\\/new_salem\",\n                    \"ojinaga\",\n                    \"panama\",\n                    \"pangnirtung\",\n                    \"paramaribo\",\n                    \"phoenix\",\n                    \"port-au-prince\",\n                    \"port_of_spain\",\n                    \"porto_velho\",\n                    \"puerto_rico\",\n                    \"rainy_river\",\n                    \"rankin_inlet\",\n                    \"recife\",\n                    \"regina\",\n                    \"resolute\",\n                    \"rio_branco\",\n                    \"santa_isabel\",\n                    \"santarem\",\n                    \"santiago\",\n                    \"santo_domingo\",\n                    \"sao_paulo\",\n                    \"scoresbysund\",\n                    \"shiprock\",\n                    \"sitka\",\n                    \"st_barthelemy\",\n                    \"st_johns\",\n                    \"st_kitts\",\n                    \"st_lucia\",\n                    \"st_thomas\",\n                    \"st_vincent\",\n                    \"swift_current\",\n                    \"tegucigalpa\",\n                    \"thule\",\n                    \"thunder_bay\",\n                    \"tijuana\",\n                    \"toronto\",\n                    \"tortola\",\n                    \"vancouver\",\n                    \"whitehorse\",\n                    \"winnipeg\",\n                    \"yakutat\",\n                    \"yellowknife\"\n                ],\n                \"antarctica\": [\n                    \"casey\",\n                    \"davis\",\n                    \"dumontdurville\",\n                    \"macquarie\",\n                    \"mawson\",\n                    \"mcmurdo\",\n                    \"palmer\",\n                    \"rothera\",\n                    \"south_pole\",\n                    \"syowa\",\n                    \"vostok\"\n                ],\n                \"arctic\": [\n                    \"longyearbyen\"\n                ],\n                \"asia\": [\n                    \"aden\",\n                    \"almaty\",\n                    \"amman\",\n                    \"anadyr\",\n                    \"aqtau\",\n                    \"aqtobe\",\n                    \"ashgabat\",\n                    \"baghdad\",\n                    \"bahrain\",\n                    \"baku\",\n                    \"bangkok\",\n                    \"beirut\",\n                    \"bishkek\",\n                    \"brunei\",\n                    \"choibalsan\",\n                    \"chongqing\",\n                    \"colombo\",\n                    \"damascus\",\n                    \"dhaka\",\n                    \"dili\",\n                    \"dubai\",\n                    \"dushanbe\",\n                    \"gaza\",\n                    \"harbin\",\n                    \"hebron\",\n                    \"ho_chi_minh\",\n                    \"hong_kong\",\n                    \"hovd\",\n                    \"irkutsk\",\n                    \"jakarta\",\n                    \"jayapura\",\n                    \"jerusalem\",\n                    \"kabul\",\n                    \"kamchatka\",\n                    \"karachi\",\n                    \"kashgar\",\n                    \"kathmandu\",\n                    \"khandyga\",\n                    \"kolkata\",\n                    \"krasnoyarsk\",\n                    \"kuala_lumpur\",\n                    \"kuching\",\n                    \"kuwait\",\n                    \"macau\",\n                    \"magadan\",\n                    \"makassar\",\n                    \"manila\",\n                    \"muscat\",\n                    \"nicosia\",\n                    \"novokuznetsk\",\n                    \"novosibirsk\",\n                    \"omsk\",\n                    \"oral\",\n                    \"phnom_penh\",\n                    \"pontianak\",\n                    \"pyongyang\",\n                    \"qatar\",\n                    \"qyzylorda\",\n                    \"rangoon\",\n                    \"riyadh\",\n                    \"sakhalin\",\n                    \"samarkand\",\n                    \"seoul\",\n                    \"shanghai\",\n                    \"singapore\",\n                    \"taipei\",\n                    \"tashkent\",\n                    \"tbilisi\",\n                    \"tehran\",\n                    \"thimphu\",\n                    \"tokyo\",\n                    \"ulaanbaatar\",\n                    \"urumqi\",\n                    \"ust-nera\",\n                    \"vientiane\",\n                    \"vladivostok\",\n                    \"yakutsk\",\n                    \"yekaterinburg\",\n                    \"yerevan\"\n                ],\n                \"atlantic\": [\n                    \"azores\",\n                    \"bermuda\",\n                    \"canary\",\n                    \"cape_verde\",\n                    \"faroe\",\n                    \"madeira\",\n                    \"reykjavik\",\n                    \"south_georgia\",\n                    \"st_helena\",\n                    \"stanley\"\n                ],\n                \"australia\": [\n                    \"adelaide\",\n                    \"brisbane\",\n                    \"broken_hill\",\n                    \"currie\",\n                    \"darwin\",\n                    \"eucla\",\n                    \"hobart\",\n                    \"lindeman\",\n                    \"lord_howe\",\n                    \"melbourne\",\n                    \"perth\",\n                    \"sydney\"\n                ],\n                \"europe\": [\n                    \"amsterdam\",\n                    \"andorra\",\n                    \"athens\",\n                    \"belgrade\",\n                    \"berlin\",\n                    \"bratislava\",\n                    \"brussels\",\n                    \"bucharest\",\n                    \"budapest\",\n                    \"busingen\",\n                    \"chisinau\",\n                    \"copenhagen\",\n                    \"dublin\",\n                    \"gibraltar\",\n                    \"guernsey\",\n                    \"helsinki\",\n                    \"isle_of_man\",\n                    \"istanbul\",\n                    \"jersey\",\n                    \"kaliningrad\",\n                    \"kiev\",\n                    \"lisbon\",\n                    \"ljubljana\",\n                    \"london\",\n                    \"luxembourg\",\n                    \"madrid\",\n                    \"malta\",\n                    \"mariehamn\",\n                    \"minsk\",\n                    \"monaco\",\n                    \"moscow\",\n                    \"oslo\",\n                    \"paris\",\n                    \"podgorica\",\n                    \"prague\",\n                    \"riga\",\n                    \"rome\",\n                    \"samara\",\n                    \"san_marino\",\n                    \"sarajevo\",\n                    \"simferopol\",\n                    \"skopje\",\n                    \"sofia\",\n                    \"stockholm\",\n                    \"tallinn\",\n                    \"tirane\",\n                    \"uzhgorod\",\n                    \"vaduz\",\n                    \"vatican\",\n                    \"vienna\",\n                    \"vilnius\",\n                    \"volgograd\",\n                    \"warsaw\",\n                    \"zagreb\",\n                    \"zaporozhye\",\n                    \"zurich\"\n                ],\n                \"indian\": [\n                    \"antananarivo\",\n                    \"chagos\",\n                    \"christmas\",\n                    \"cocos\",\n                    \"comoro\",\n                    \"kerguelen\",\n                    \"mahe\",\n                    \"maldives\",\n                    \"mauritius\",\n                    \"mayotte\",\n                    \"reunion\"\n                ],\n                \"pacific\": [\n                    \"apia\",\n                    \"auckland\",\n                    \"chatham\",\n                    \"chuuk\",\n                    \"easter\",\n                    \"efate\",\n                    \"enderbury\",\n                    \"fakaofo\",\n                    \"fiji\",\n                    \"funafuti\",\n                    \"galapagos\",\n                    \"gambier\",\n                    \"guadalcanal\",\n                    \"guam\",\n                    \"honolulu\",\n                    \"johnston\",\n                    \"kiritimati\",\n                    \"kosrae\",\n                    \"kwajalein\",\n                    \"majuro\",\n                    \"marquesas\",\n                    \"midway\",\n                    \"nauru\",\n                    \"niue\",\n                    \"norfolk\",\n                    \"noumea\",\n                    \"pago_pago\",\n                    \"palau\",\n                    \"pitcairn\",\n                    \"pohnpei\",\n                    \"port_moresby\",\n                    \"rarotonga\",\n                    \"saipan\",\n                    \"tahiti\",\n                    \"tarawa\",\n                    \"tongatapu\",\n                    \"wake\",\n                    \"wallis\"\n                ],\n                \"utc\": [\n                    \"\"\n                ]\n            };\n\n            var tzparts= value.split('/',2);\n            var continent=tzparts[0].toLowerCase();\n            var city='';\n            if (tzparts[1]) {\n                city=tzparts[1].toLowerCase();\n            }\n\n            return (continent in timezones && ( timezones[continent].length===0 || timezones[continent].indexOf(city)!==-1))\n        }\n    }\n});\n", "/*!\n * Laravel Javascript Validation\n *\n * https://github.com/proengsoft/laravel-jsvalidation\n *\n * Methods that implement Laravel Validations\n *\n * Copyright (c) 2017 Proengsoft\n * Released under the MIT license\n */\n\n$.extend(true, laravelValidation, {\n\n    methods:{\n\n        helpers: laravelValidation.helpers,\n\n        jsRemoteTimer:0,\n\n        /**\n         * \"Validate\" optional attributes.\n         * Always returns true, just lets us put sometimes in rules.\n         *\n         * @return {boolean}\n         */\n        Sometimes: function() {\n            return true;\n        },\n\n        /**\n         * Bail This is the default behaivour os JSValidation.\n         * Always returns true, just lets us put sometimes in rules.\n         *\n         * @return {boolean}\n         */\n        Bail: function() {\n            return true;\n        },\n\n        /**\n         * \"Indicate\" validation should pass if value is null.\n         * Always returns true, just lets us put \"nullable\" in rules.\n         *\n         * @return {boolean}\n         */\n        Nullable: function() {\n            return true;\n        },\n\n        /**\n         * Validate the given attribute is filled if it is present.\n         */\n        Filled: function(value, element) {\n            return $.validator.methods.required.call(this, value, element, true);\n        },\n\n\n        /**\n         * Validate that a required attribute exists.\n         */\n        Required: function(value, element) {\n            return  $.validator.methods.required.call(this, value, element);\n        },\n\n        /**\n         * Validate that an attribute exists when any other attribute exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWith: function(value, element, params) {\n            var validator=this,\n                required=false;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required=required || (\n                    target!==undefined &&\n                    $.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when all other attribute exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithAll: function(value, element, params) {\n            var validator=this,\n                required=true;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required && (\n                      target!==undefined &&\n                      $.validator.methods.required.call(\n                          validator,\n                          currentObject.elementValue(target),\n                          target,true\n                      ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when any other attribute does not exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithout: function(value, element, params) {\n            var validator=this,\n                required=false;\n            var currentObject=this;\n\n            $.each(params,function(i,param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required ||\n                    target===undefined||\n                    !$.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    );\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when all other attribute does not exists.\n         *\n         * @return {boolean}\n         */\n        RequiredWithoutAll: function(value, element, params) {\n            var validator=this,\n                required=true,\n                currentObject=this;\n\n            $.each(params,function(i, param) {\n                var target=laravelValidation.helpers.dependentElement(\n                    currentObject, element, param\n                );\n                required = required && (\n                    target===undefined ||\n                    !$.validator.methods.required.call(\n                        validator,\n                        currentObject.elementValue(target),\n                        target,true\n                    ));\n            });\n\n            if (required) {\n                return  $.validator.methods.required.call(this, value, element, true);\n            }\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when another attribute has a given value.\n         *\n         * @return {boolean}\n         */\n        RequiredIf: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                var val=String(this.elementValue(target));\n                if (typeof val !== 'undefined') {\n                    var data = params.slice(1);\n                    if ($.inArray(val, data) !== -1) {\n                        return $.validator.methods.required.call(\n                            this, value, element, true\n                        );\n                    }\n                }\n            }\n\n            return true;\n        },\n\n        /**\n         * Validate that an attribute exists when another\n         * attribute does not have a given value.\n         *\n         * @return {boolean}\n         */\n        RequiredUnless: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                var val=String(this.elementValue(target));\n                if (typeof val !== 'undefined') {\n                    var data = params.slice(1);\n                    if ($.inArray(val, data) !== -1) {\n                        return true;\n                    }\n                }\n            }\n\n            return $.validator.methods.required.call(\n                this, value, element, true\n            );\n\n        },\n\n        /**\n         * Validate that an attribute has a matching confirmation.\n         *\n         * @return {boolean}\n         */\n        Confirmed: function(value, element, params) {\n            return laravelValidation.methods.Same.call(this,value, element, params);\n        },\n\n        /**\n         * Validate that two attributes match.\n         *\n         * @return {boolean}\n         */\n        Same: function(value, element, params) {\n\n            var target=laravelValidation.helpers.dependentElement(\n                this, element, params[0]\n            );\n\n            if (target!==undefined) {\n                return String(value) === String(this.elementValue(target));\n            }\n            return false;\n        },\n\n        /**\n         * Validate that the values of an attribute is in another attribute.\n         *\n         * @param value\n         * @param element\n         * @param params\n         * @returns {boolean}\n         * @constructor\n         */\n        InArray: function (value, element, params) {\n            if (typeof params[0] === 'undefined') {\n                return false;\n            }\n            var elements = this.elements();\n            var found = false;\n            var nameRegExp = laravelValidation.helpers.regexFromWildcard(params[0]);\n\n            for ( var i = 0; i < elements.length ; i++ ) {\n                var targetName = elements[i].name;\n                if (targetName.match(nameRegExp)) {\n                    var equals = laravelValidation.methods.Same.call(this,value, element, [targetName]);\n                    found = found || equals;\n                }\n            }\n\n            return found;\n        },\n\n        /**\n         * Validate an attribute is unique among other values.\n         *\n         * @param value\n         * @param element\n         * @param params\n         * @returns {boolean}\n         */\n        Distinct: function (value, element, params) {\n            if (typeof params[0] === 'undefined') {\n                return false;\n            }\n\n            var elements = this.elements();\n            var found = false;\n            var nameRegExp = laravelValidation.helpers.regexFromWildcard(params[0]);\n\n            for ( var i = 0; i < elements.length ; i++ ) {\n                var targetName = elements[i].name;\n                if (targetName !== element.name && targetName.match(nameRegExp)) {\n                    var equals = laravelValidation.methods.Same.call(this,value, element, [targetName]);\n                    found = found || equals;\n                }\n            }\n\n            return !found;\n        },\n\n\n        /**\n         * Validate that an attribute is different from another attribute.\n         *\n         * @return {boolean}\n         */\n        Different: function(value, element, params) {\n            return ! laravelValidation.methods.Same.call(this,value, element, params);\n        },\n\n        /**\n         * Validate that an attribute was \"accepted\".\n         * This validation rule implies the attribute is \"required\".\n         *\n         * @return {boolean}\n         */\n        Accepted: function(value) {\n            var regex = new RegExp(\"^(?:(yes|on|1|true))$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is an array.\n         *\n         * @param value\n         * @param element\n         */\n        Array: function(value, element) {\n            if (element.name.indexOf('[') !== -1 && element.name.indexOf(']') !== -1) {\n                return true;\n            }\n\n            return laravelValidation.helpers.isArray(value);\n        },\n\n        /**\n         * Validate that an attribute is a boolean.\n         *\n         * @return {boolean}\n         */\n        Boolean: function(value) {\n            var regex= new RegExp(\"^(?:(true|false|1|0))$\",'i');\n            return  regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is an integer.\n         *\n         * @return {boolean}\n         */\n        Integer: function(value) {\n            var regex= new RegExp(\"^(?:-?\\\\d+)$\",'i');\n            return  regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is numeric.\n         */\n        Numeric: function(value, element) {\n            return $.validator.methods.number.call(this, value, element, true);\n        },\n\n        /**\n         * Validate that an attribute is a string.\n         *\n         * @return {boolean}\n         */\n        String: function(value) {\n            return typeof value === 'string';\n        },\n\n        /**\n         * The field under validation must be numeric and must have an exact length of value.\n         */\n        Digits: function(value, element, params) {\n            return (\n                $.validator.methods.number.call(this, value, element, true) &&\n                value.length === parseInt(params, 10)\n            );\n        },\n\n        /**\n         * The field under validation must have a length between the given min and max.\n         */\n        DigitsBetween: function(value, element, params) {\n            return ($.validator.methods.number.call(this, value, element, true)\n                && value.length>=parseFloat(params[0]) && value.length<=parseFloat(params[1]));\n        },\n\n        /**\n         * Validate the size of an attribute.\n         *\n         * @return {boolean}\n         */\n        Size: function(value, element, params) {\n            return laravelValidation.helpers.getSize(this, element,value) === parseFloat(params[0]);\n        },\n\n        /**\n         * Validate the size of an attribute is between a set of values.\n         *\n         * @return {boolean}\n         */\n        Between: function(value, element, params) {\n            return ( laravelValidation.helpers.getSize(this, element,value) >= parseFloat(params[0]) &&\n                laravelValidation.helpers.getSize(this,element,value) <= parseFloat(params[1]));\n        },\n\n        /**\n         * Validate the size of an attribute is greater than a minimum value.\n         *\n         * @return {boolean}\n         */\n        Min: function(value, element, params) {\n            value = laravelValidation.helpers.allElementValues(this, element);\n\n            return laravelValidation.helpers.getSize(this, element, value) >= parseFloat(params[0]);\n        },\n\n        /**\n         * Validate the size of an attribute is less than a maximum value.\n         *\n         * @return {boolean}\n         */\n        Max: function(value, element, params) {\n            value = laravelValidation.helpers.allElementValues(this, element);\n\n            return laravelValidation.helpers.getSize(this, element, value) <= parseFloat(params[0]);\n        },\n\n        /**\n         * Validate an attribute is contained within a list of values.\n         *\n         * @return {boolean}\n         */\n        In: function(value, element, params) {\n            if (laravelValidation.helpers.isArray(value)\n                && laravelValidation.helpers.hasRules(element, \"Array\")\n            ) {\n                var diff = laravelValidation.helpers.arrayDiff(value, params);\n\n                return Object.keys(diff).length === 0;\n            }\n\n            return params.indexOf(value.toString()) !== -1;\n        },\n\n        /**\n         * Validate an attribute is not contained within a list of values.\n         *\n         * @return {boolean}\n         */\n        NotIn: function(value, element, params) {\n            return params.indexOf(value.toString()) === -1;\n        },\n\n        /**\n         * Validate that an attribute is a valid IP.\n         *\n         * @return {boolean}\n         */\n        Ip: function(value) {\n            return /^(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/i.test(value) ||\n                /^((([0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b)\\.){3}(\\b((25[0-5])|(1\\d{2})|(2[0-4]\\d)|(\\d{1,2}))\\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$/i.test(value);\n        },\n\n        /**\n         * Validate that an attribute is a valid e-mail address.\n         */\n        Email: function(value, element) {\n            return $.validator.methods.email.call(this, value, element, true);\n        },\n\n        /**\n         * Validate that an attribute is a valid URL.\n         */\n        Url: function(value, element) {\n            return $.validator.methods.url.call(this, value, element, true);\n        },\n\n        /**\n         * The field under validation must be a successfully uploaded file.\n         *\n         * @return {boolean}\n         */\n        File: function(value, element) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            if ('files' in element ) {\n                return (element.files.length > 0);\n            }\n            return false;\n        },\n\n        /**\n         * Validate the MIME type of a file upload attribute is in a set of MIME types.\n         *\n         * @return {boolean}\n         */\n        Mimes: function(value, element, params) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            var lowerParams = $.map(params, function(item) {\n                return item.toLowerCase();\n            });\n\n            var fileinfo = laravelValidation.helpers.fileinfo(element);\n            return (fileinfo !== false && lowerParams.indexOf(fileinfo.extension.toLowerCase())!==-1);\n        },\n\n        /**\n         * The file under validation must match one of the given MIME types.\n         *\n         * @return {boolean}\n         */\n        Mimetypes: function(value, element, params) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            var lowerParams = $.map(params, function(item) {\n                return item.toLowerCase();\n            });\n\n            var fileinfo = laravelValidation.helpers.fileinfo(element);\n\n            if (fileinfo === false) {\n                return false;\n            }\n            return (lowerParams.indexOf(fileinfo.type.toLowerCase())!==-1);\n        },\n\n        /**\n         * Validate the MIME type of a file upload attribute is in a set of MIME types.\n         */\n        Image: function(value, element) {\n            return laravelValidation.methods.Mimes.call(this, value, element, [\n                'jpg', 'png', 'gif', 'bmp', 'svg', 'jpeg'\n            ]);\n        },\n\n        /**\n         * Validate dimensions of Image.\n         *\n         * @return {boolean|string}\n         */\n        Dimensions: function(value, element, params, callback) {\n            if (!window.File || !window.FileReader || !window.FileList || !window.Blob) {\n                return true;\n            }\n            if (element.files === null || typeof element.files[0] === 'undefined') {\n                return false;\n            }\n\n            var fr = new FileReader;\n            fr.onload = function () {\n                var img = new Image();\n                img.onload = function () {\n                    var height = parseFloat(img.naturalHeight);\n                    var width = parseFloat(img.naturalWidth);\n                    var ratio = width / height;\n                    var notValid = ((params['width']) && parseFloat(params['width'] !== width)) ||\n                        ((params['min_width']) && parseFloat(params['min_width']) > width) ||\n                        ((params['max_width']) && parseFloat(params['max_width']) < width) ||\n                        ((params['height']) && parseFloat(params['height']) !== height) ||\n                        ((params['min_height']) && parseFloat(params['min_height']) > height) ||\n                        ((params['max_height']) && parseFloat(params['max_height']) < height) ||\n                        ((params['ratio']) && ratio !== parseFloat(eval(params['ratio']))\n                        );\n                    callback(! notValid);\n                };\n                img.onerror = function() {\n                    callback(false);\n                };\n                img.src = fr.result;\n            };\n            fr.readAsDataURL(element.files[0]);\n\n            return 'pending';\n        },\n\n        /**\n         * Validate that an attribute contains only alphabetic characters.\n         *\n         * @return {boolean}\n         */\n        Alpha: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n\n            var regex = new RegExp(\"^(?:^[a-z\\u00E0-\\u00FC]+$)$\",'i');\n            return  regex.test(value);\n\n        },\n\n        /**\n         * Validate that an attribute contains only alpha-numeric characters.\n         *\n         * @return {boolean}\n         */\n        AlphaNum: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n            var regex = new RegExp(\"^(?:^[a-z0-9\\u00E0-\\u00FC]+$)$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute contains only alphabetic characters.\n         *\n         * @return {boolean}\n         */\n        AlphaDash: function(value) {\n            if (typeof  value !== 'string') {\n                return false;\n            }\n            var regex = new RegExp(\"^(?:^[a-z0-9\\u00E0-\\u00FC_-]+$)$\",'i');\n            return regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute passes a regular expression check.\n         *\n         * @return {boolean}\n         */\n        Regex: function(value, element, params) {\n            var invalidModifiers=['x','s','u','X','U','A'];\n            // Converting php regular expression\n            var phpReg= new RegExp('^(?:\\/)(.*\\\\\\/?[^\\/]*|[^\\/]*)(?:\\/)([gmixXsuUAJ]*)?$');\n            var matches=params[0].match(phpReg);\n            if (matches === null) {\n                return false;\n            }\n            // checking modifiers\n            var php_modifiers=[];\n            if (matches[2]!==undefined) {\n                php_modifiers=matches[2].split('');\n                for (var i=0; i<php_modifiers.length<i ;i++) {\n                    if (invalidModifiers.indexOf(php_modifiers[i])!==-1) {\n                        return true;\n                    }\n                }\n            }\n            var regex = new RegExp(\"^(?:\"+matches[1]+\")$\",php_modifiers.join());\n            return   regex.test(value);\n        },\n\n        /**\n         * Validate that an attribute is a valid date.\n         *\n         * @return {boolean}\n         */\n        Date: function(value) {\n            return (laravelValidation.helpers.strtotime(value)!==false);\n        },\n\n        /**\n         * Validate that an attribute matches a date format.\n         *\n         * @return {boolean}\n         */\n        DateFormat: function(value, element, params) {\n            return laravelValidation.helpers.parseTime(value,params[0])!==false;\n        },\n\n        /**\n         * Validate the date is before a given date.\n         *\n         * @return {boolean}\n         */\n        Before: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '<');\n        },\n\n        /**\n         * Validate the date is equal or before a given date.\n         *\n         * @return {boolean}\n         */\n        BeforeOrEqual: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '<=');\n        },\n\n        /**\n         * Validate the date is after a given date.\n         *\n         * @return {boolean}\n         */\n        After: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '>');\n        },\n\n        /**\n         * Validate the date is equal or after a given date.\n         *\n         * @return {boolean}\n         */\n        AfterOrEqual: function(value, element, params) {\n            return laravelValidation.helpers.compareDates(this, value, element, params[0], '>=');\n        },\n\n\n        /**\n         * Validate that an attribute is a valid date.\n         */\n        Timezone: function(value) {\n            return  laravelValidation.helpers.isTimezone(value);\n        },\n\n\n        /**\n         * Validate the attribute is a valid JSON string.\n         *\n         * @param  value\n         * @return bool\n         */\n        Json: function(value) {\n            var result = true;\n            try {\n                JSON.parse(value);\n            } catch (e) {\n                result = false;\n            }\n            return result;\n        },\n\n        /**\n         * Noop (always returns true).\n         *\n         * @param value\n         * @returns {boolean}\n         */\n        ProengsoftNoop: function (value) {\n            return true;\n        },\n    }\n});\n"]}