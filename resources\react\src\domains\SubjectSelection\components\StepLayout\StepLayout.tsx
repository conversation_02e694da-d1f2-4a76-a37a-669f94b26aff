import ProgressBar from '@/components/base/ProgressBar/ProgressBar';
import { PropsWithChildren } from 'react';

interface Props {
    title: string;
    subtitle?: string;
    description: string;
    progress: number;
}
export default function StepLayout({ title, description, progress, children, subtitle }: PropsWithChildren<Props>) {
    return (
        <div className='step-layout'>
            <ProgressBar className='mb-10' progress={progress} />
            <h1 className='mb-8'>{title}</h1>
            {subtitle && <span style={{
                color: "rgba(0, 0, 0, 0.45)"
            }} className='fs-6 d-block mb-2'>{subtitle}</span>}
            <span className='d-block mb-8'>{description}</span>
            {children}
        </div>
    );
}
