import { useAppDeps } from '@/App';
import { log } from '@/services/Logger.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { StudentRuleProgressResponse } from '../models/Students.api.model';
import { useAppDispatch } from '@/context/App.context';
import { setRulesProgressState } from '@/context/App.actions';

export enum StudentsApiConsumer {
    studentRulesProgress = 'studentRulesProgress',
}

export const useStudentRulesProgress = (studentId: number) => {
    const { studentsService } = useAppDeps();
    const dispatch = useAppDispatch();

    return useQuery<StudentRuleProgressResponse[]>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [StudentsApiConsumer.studentRulesProgress],
        queryFn: async () => {
            const { data } = await studentsService.getStudentRuleProgress(studentId);

            return data.data.rules;
        },
        onSuccess: (data) => {
            dispatch(setRulesProgressState(data.map((rule) => rule.id)));
        },
        onError: (error) => {
            log().error({ message: 'useStudentRulesProgress - fetching student rule progress failed' });
            console.trace(error);
        },
    });
};

export const useSaveStudentRulesProgress = () => {
    const { studentsService } = useAppDeps();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ studentId, ruleProgress }: { studentId: number; ruleProgress: number[] }) => {
            return studentsService.saveStudentRuleProgress(studentId, ruleProgress);
        },
        onSuccess: () => {
            queryClient.invalidateQueries([StudentsApiConsumer.studentRulesProgress]);
        },
    });
};
