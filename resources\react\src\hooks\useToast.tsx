import { useRef } from 'react';
import { createRoot } from 'react-dom/client';
import Toast from '@/components/base/Toast/Toast';

type ShowProps = {
    message?: string;
    onClose?: () => void;
    wait?: number;
};

export interface ToastHookProps {
    show: () => void;
}

const container = document.getElementById('toast') as Element;
const root = createRoot(container);

export default function useToast(props?: ShowProps) {
    const show = ({ wait = 3000, message }: ShowProps) => {
        if (!wait) {
            props = {
                ...props,
            };
        }

        if (container) {
            root.render(<Toast message={message} />);
        }

        wait &&
            (async (root) => {
                await new Promise(() =>
                    setTimeout(() => {
                        root.render(<></>);
                    }, wait || 0),
                );
            })(root);
    };

    const memoShow = useRef(show);

    return {
        show: memoShow.current,
    };
}
