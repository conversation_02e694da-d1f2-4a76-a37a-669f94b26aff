import ApiService from '@/services/Api.decode.service';
import { AxiosResponse } from 'axios';
import { StudentRuleProgressResponse } from '../models/Students.api.model';

export class StudentsService {
    private apiService: ApiService;

    private static instance: StudentsService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): StudentsService {
        if (!this.instance) {
            this.instance = new StudentsService();
        }
        return this.instance;
    }

    public getStudentRuleProgress(
        studentId: number,
    ): Promise<AxiosResponse<{ data: { rules: StudentRuleProgressResponse[] } }>> {
        return this.apiService.get({ url: `/students/${studentId}/rules` });
    }

    public saveStudentRuleProgress(
        studentId: number,
        rules_progress: number[],
    ): Promise<AxiosResponse<{ data: StudentRuleProgressResponse }>> {
        return this.apiService.post({ url: `/students/${studentId}/rules`, data: { rules_progress } });
    }
}
