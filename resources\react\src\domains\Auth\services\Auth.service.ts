import { AxiosResponse } from 'axios';

import { UserResponse } from '../models/Auth.model';

import { VERIFY_TOKEN } from '@/router/Router.api.constants';
import ApiService from '@/services/Api.service';

/**
 * Handles logins and logouts. Persists credentials to local storage.
 */
export class AuthService {
    private apiService: ApiService;

    private static instance: AuthService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): AuthService {
        if (!this.instance) {
            this.instance = new AuthService();
        }
        return this.instance;
    }

    verifyToken(): Promise<AxiosResponse<{ data: UserResponse }>> {
        return this.apiService.post({ url: VERIFY_TOKEN });
    }

    logOut(): Promise<AxiosResponse> {
        return this.apiService.post({ url: '/logout' });
    }
}
