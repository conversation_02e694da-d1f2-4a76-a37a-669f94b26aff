<template>
    <div class="">
        <Form class="form w-100 text-gray-400" id="kt_login_signin_form" @submit="onSubmitLogin" :validation-schema="login">
            <div class="mb-10 text-center">
                <p class="mb-2 text-dark fs-2x fw-bold" v-if="!passwordField">Get Started</p>
                <p class="mb-2 text-dark fs-2x fw-bold" v-if="passwordField">Welcome Back</p>
                <div class="text-gray-400 fw-semobold fs-4"  v-if="!passwordField">
                    Enter your email address.
                </div>
                <div class="text-gray-400 fw-semobold fs-4" v-if="passwordField">
                    Enter your password.
                </div>
            </div>
            <div class="mb-10 fv-row">
                <div class="form-floating ">
                    <Field tabindex="1" class="form-control form-control-lg rounded-0" id="userEmail" type="text" name="email" autocomplete="off" placeholder="Email" />
                    <label for="userEmail">Email</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="email" />
                    </div>
                </div>
            </div>
            <div class="mb-10 fv-row" v-show="passwordField">
                <!-- <div class="mb-2 d-flex flex-stack">
                    <label class="mb-0 form-label fw-bold text-dark fs-6">Password</label>
                </div> -->
                <div class="form-floating ">
                    <Field tabindex="2" class="form-control form-control-lg rounded-0 mb-4" id="userPassword" type="password" name="password" autocomplete="off" placeholder="Password" />
                    <label for="userPassword">Password</label>
                </div>
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <ErrorMessage name="password" />
                    </div>
                </div>
                <router-link to="/password-reset" class="link-primary fs-6 fw-bold float-right mb-4">
                    Forgot Password ?
                </router-link>
            </div>
            <div class="text-center">
                <button tabindex="3" type="submit" ref="submitButton" id="kt_sign_in_submit" class="mb-5 btn btn-lg btn-primary w-100 rounded-0">
                    <span class="indicator-label"> NEXT </span>

                    <span class="indicator-progress">
            Please wait...
            <span
              class="align-middle spinner-border spinner-border-sm ms-2"
            ></span>
                    </span>
                </button>
            </div>
        </Form>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref } from "vue";
import { ErrorMessage, Field, Form,configure } from "vee-validate";
import { Actions } from "@/store/enums/StoreEnums";
import { useRegisterStore } from "@/stores/Auth/RegisterStore";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import Swal from "sweetalert2/dist/sweetalert2.min.js";
import * as Yup from "yup";
configure({
    validateOnBlur: false,
    validateOnChange:false,
    validateOnInput:false,
    validateOnModelUpdate:false
});

export default defineComponent({
    name: "sign-in",
    components: {
        Field,
        Form,
        ErrorMessage,
    },
    setup() {
        const store = useStore();
        const router = useRouter();
        const authStore = useRegisterStore();
        authStore.$reset();
        const submitButton = ref<HTMLButtonElement | null>(null);
        const passwordField = ref(0);
        const loginProcess = ref(0);
        //Create form validation object
        const login = Yup.object().shape({
            email: Yup.string().email("Must be a valid email").required(),
            // password: Yup.string().min(4).required().label("Password"),
        });

        //Form submit function
        const onSubmitLogin = async (values) => {
            // Clear existing errors
            await store.dispatch(Actions.LOGOUT);

            if (submitButton.value) {
                // eslint-disable-next-line
                submitButton.value!.disabled = true;
                // Activate indicator
                submitButton.value.setAttribute("data-kt-indicator", "on");
            }

            // Send login request
            if (passwordField.value) {
                loginProcess.value = 1;
                await store.dispatch(Actions.LOGIN, values);
            } else {
                await store.dispatch(Actions.CHECK_EMAIL, {'email': values.email});
            }

            const [errorName] = Object.keys(store.getters.getErrors);
            const error = store.getters.getErrors[errorName];
            
            if (!error) {
                if (passwordField.value) {
                    sessionStorage.galleryPopupOpen = 'show';
                    await router.push({name: "dashboard"});
                } else {
                    passwordField.value = 1;
                }
            } else {
                if (loginProcess.value) {
                    Swal.fire({
                        html: error[0],
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Try again!",
                        customClass: {
                            confirmButton: "btn fw-semobold btn-light-danger",
                        },
                    });
                } else {
                    authStore.email = values.email;
                    authStore.isNew = true;                
                    authStore.underUniversity = store.getters.getErrors.errors?.[0]?.underUniversity;    
                    authStore.showPostcode = store.getters.getErrors.errors?.[0]?.showPostcode;    
                    authStore.studentDetail.school.id = store.getters.getErrors.errors?.[0]?.schoolId
                    authStore.studentDetail.state = store.getters.getErrors.errors?.[0]?.stateId
                    authStore.studentDetail.school.logo = store.getters.getErrors.errors?.[0]?.schoolLogo
                    authStore.instituteDomain = store.getters.getErrors.errors?.[0]?.schoolDomain
                    authStore.privacyLink = store.getters.getErrors.errors?.[0]?.privacyLink;
                    authStore.studentDetail.school.name = store.getters.getErrors.errors?.[0]?.instituteName;

                    if(store.getters.getErrors.errors?.[0]?.showPostcode === false){
                        authStore.studentDetail.postcode = store.getters.getErrors.errors?.[0]?.postcode
                    }
                    if(authStore.underUniversity == true){ 
                        await router.push({name: "sign-up-institute-details"});
                    }else{
                        await router.push({name: "sign-up"});
                    }   
                }
            }

            //Deactivate indicator
            submitButton.value?.removeAttribute("data-kt-indicator");
            // eslint-disable-next-line
            submitButton.value!.disabled = false;
        };

        return {
            onSubmitLogin,
            login,
            submitButton,
            passwordField,
            loginProcess
        };
    },
});
</script>
<style>
.float-right {
    float: right !important;
}
</style>
