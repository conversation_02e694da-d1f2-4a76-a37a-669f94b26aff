import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSubjectManagementDeps } from '../SubjectManagement';
import { log } from '@/services/Logger.service';
import { StatisticsResponse, StudentsResponse, SchoolGuidesResponse } from '../models/SubjectManagement.api.model';
import useToast from '@/hooks/useToast';
import { AxiosError } from 'axios';
import { FilterApi, PaginationApi } from '@/common/models/Api.model';

export enum SubjectManagementApiConsumer {
    statistics = 'statistics',
    students = 'students',
    studentsPagination = 'studentsPagination',
    schoolGuides = 'schoolGuides',
}

export const useStatistics = () => {
    const { subjectManagementService } = useSubjectManagementDeps();

    return useQuery<StatisticsResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        refetchOnWindowFocus: false,
        queryKey: [SubjectManagementApiConsumer.statistics],
        queryFn: async () => {
            const { data } = await subjectManagementService.getStatistics();
            return data;
        },
        onError: () => {
            log().error({ message: 'useStatistics - fetching statistics failed' });
        },
    });
};

interface Props {
    pagination?: PaginationApi;
    filter?: FilterApi;
}

export const useStudents = ({ pagination, filter }: Props) => {
    const { subjectManagementService } = useSubjectManagementDeps();

    return useQuery<StudentsResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [SubjectManagementApiConsumer.students, pagination, filter],
        keepPreviousData: true,
        queryFn: async () => {
            const { data } = await subjectManagementService.getStudents({ pagination, filter });
            return data;
        },
        onError: () => {
            log().error({ message: 'useStudents - fetching students failed' });
        },
    });
};

export const useSchoolGuides = () => {
    const { subjectManagementService } = useSubjectManagementDeps();

    return useQuery<SchoolGuidesResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [SubjectManagementApiConsumer.schoolGuides],
        queryFn: async () => {
            const { data } = await subjectManagementService.getSchoolGuides();
            return data;
        },
        onError: () => {
            log().error({ message: 'useSchoolGuides - fetching school guides failed' });
        },
    });
};

export const useUploadSchoolGuides = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { subjectManagementService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: (attachments: FormData) => {
            log().debug({ message: `Attachment form data - api consumer` });
            console.log(attachments);
            return subjectManagementService.uploadSchoolGuides({ attachments });
        },
        onError: (error: AxiosError) => {
            log().error({ message: 'useUploadSchoolGuides - school guide upload failed' });
            if (error.response?.status === 413) {
                toast.show({
                    message: 'Selected PDF file is too large. Did not upload the file',
                });
                return;
            }

            toast.show({
                message: 'Selected PDF file did not upload. Please try again later as we are experiencing problems.',
            });
        },
        onSuccess: () => {
            queryClient.invalidateQueries([SubjectManagementApiConsumer.schoolGuides]);
            toast.show({
                message: 'School guide uploaded successfully',
            });
        },
    });
};

export const useShareResultsViaEmail = () => {
    const toast = useToast();
    const { subjectManagementService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: (emails: string[]) => {
            return subjectManagementService.shareResultsViaEmail(emails);
        },
        onSuccess: () => {
            toast.show({
                message: 'Your results have been sent!',
            });
        },
        onError: () => {
            toast.show({
                message:
                    'Something went wrong when sharing your results. If the issue continues please contact our support.',
            });
            log().error({ message: 'useShareResultsViaEmail - sending emails failed' });
        },
    });
};

export const useDeleteSchoolGuides = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { subjectManagementService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: (schoolGuideId: number) => {
            return subjectManagementService.deleteSchoolGuide(schoolGuideId);
        },
        onSuccess: () => {
            queryClient.invalidateQueries([SubjectManagementApiConsumer.schoolGuides]);
            toast.show({
                message: 'School guide deleted!',
            });
        },
        onError: () => {
            toast.show({
                message:
                    'Something went wrong when deleting the school guide. Please contact our support if problem persists.',
            });
            log().error({ message: 'useDeleteSchoolGuides - deleting school guide failed' });
        },
    });
};
