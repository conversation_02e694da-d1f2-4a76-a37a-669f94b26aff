<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
// use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Support\Facades\Storage;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Overtrue\LaravelFavorite\Traits\Favoriteable;
use Auth;
use Illuminate\Support\Facades\Log;

class Industryunit extends Model
{
    // use LogsActivity;
    use \Conner\Tagging\Taggable;
    use Favoriteable;

    public $timestamps = false;
    public $guarded = [];
    protected static $logAttributes = ['title', 'description'];
    protected static $recordEvents = ['updated'];
    protected $appends = ['banner_fullpath', /* 'favourite', */ 'tileimage_fullpath', 'audiofullpath'];

    // public function getDescriptionForEvent($eventName)
    // {
    //     if ($eventName == 'updated' && $this->publish == '1') {
    //         return ('<a href="exploreindustries/' . $this->industries->first()->id . '/unit/' . $this->id . '">' . $this->title . ' has been updated.</a>');
    //     }

    //     return '';
    // }
    protected static function booted()
    {

        static::deleting(function ($industryunit) {
            $industryunit->courses()->detach();
            $industryunit->industries()->detach();
            $industryunit->checklist()->delete();
            $industryunit->factandfaq()->delete();
            $industryunit->jobsmap()->delete();
            $industryunit->phoneafriend()->delete();
            $industryunit->video()->delete();
            $industryunit->virtualtour()->delete();
            $industryunit->courseoption()->delete();
            $industryunit->subjectselectionguide()->delete();
            $industryunit->scrapbook()->delete();
            $industryunit->sendaletter()->delete();
            $industryunit->templateProgress->each->delete();
            $industryunit->states()->detach();
            $industryunit->gethelps()->delete();
            $industryunit->sendtparents()->delete();
            $industryunit->users()->detach();
            $industryunit->locations()->delete();
            $industryunit->skillstrainingTemplates()->detach();
            $industryunit->workexperienceTemplates()->detach();
            ActivityLog::where(['subject_type' => 'App\Industryunit', 'subject_id' => $industryunit->id])->delete();

            if ($industryunit->tags->isNotEmpty()) {
                $industryunit->untag();
            }


            if ($industryunit->inner_banner) {
                Storage::delete($industryunit->inner_banner);
            }
            if ($industryunit->banner) {
                Storage::delete($industryunit->banner);
            }

            if ($industryunit->workingStyles) {
                $industryunit->workingStyles()->detach();
            }
        });

        static::addGlobalScope('industryunits', function (Builder $builder) {
            $builder->when(\Auth::check() && !(\Auth::user()->isParent() || \Auth::user()->isAdmin()), function ($query) {
                $query->where('type', '!=', 'parent');
            })->when(\Auth::check() && !(\Auth::user()->isAdmin()), function ($query) {
                $query->where('publish', '1');
            })->where('type', '<>', 'subjectselectionguide');
            // ->orWhere(function ($query) {
            //     $query->where('type', 'subjectselectionguide')
            //         ->when(\Auth::check() && !(\Auth::user()->isAdmin()), function ($q) {
            //             return $q->whereHas('states', function ($q) {
            //                 $q->whereStateId(\Auth::user()->state_id);
            //             });
            //         });
            // });
        });
    }

    // protected function sortorder(): Attribute
    // {
    //     return Attribute::make(
    //         get: fn ($value) => $value == 0 ? null : $value,
    //     );
    // }

    protected function order(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->sortorder == 0 ? PHP_INT_MAX : $this->sortorder,
        );
    }

    protected function estimatedTime(): Attribute
    {
        return Attribute::make(
            get: fn($value) => unserialize($value),
            set: fn($value) => serialize($value),
        );
    }

    protected function iconClass(): Attribute
    {
        return Attribute::make(
            get: function () {
                switch ($this->type) {
                    case 'checklist':
                        return 'las la-list-alt';
                    case 'factandfaq':
                        return 'las la-expand';
                    case 'jobsmap':
                        return 'las la-map';
                    case 'phoneafriend':
                        return 'las la-headphones';
                    case 'video':
                        return 'las la-play';
                    case 'virtualtour':
                        return 'las la-location-arrow';
                    case 'courseoption':
                        return 'las la-comment-alt';
                    case 'careertimeline':
                        return 'las la-stream';
                    case 'scrapbook':
                        return 'las la-sticky-note';
                    default:
                        return 'la-exclamation-circle'; // Default value
                }
            }
        );
    }

    public function studentChecklist()
    {
        return $this->hasOne("App\ChecklistData", 'template_id')->where('student_id', \Auth::id());
    }

    public function studentSendaletter()
    {
        return $this->hasMany("App\SendaletterData", 'template_id')->where('student_id', \Auth::id());
    }

    public function template()
    {
        return $this->belongsTo("App\Template", 'type', 'slug');
    }

    public function industries()
    {
        return $this->belongsToMany("App\IndustryCategory", 'industry_industryunit', 'industryunit_id', 'industry_id');
        // return $this->belongsTo("App\IndustryCategory", 'industry_category_id');
    }
    public function courses()
    {
        return $this->belongsToMany("App\Course");
    }

    public function checklist()
    {
        return $this->hasMany("App\Checklist");
    }
    public function factandfaq()
    {
        return $this->hasMany("App\Factandfaq");
    }
    public function factandfaqImages()
    {
        return $this->hasMany('App\FactandfaqImage');
    }
    public function jobsmap()
    {
        return $this->hasMany("App\Jobsmap");
    }
    public function phoneafriend()
    {
        return $this->hasOne("App\Phonecall");
    }
    public function video()
    {
        // return $this->hasOne("App\Video");
        return $this->hasMany("App\Video");
    }
    public function virtualtour()
    {
        // return $this->hasOne("App\Virtualtour");
        return $this->hasMany("App\Virtualtour");
    }
    public function courseoption()
    {
        return $this->hasMany("App\Courseoption");
    }
    public function subjectselectionguide()
    {
        return $this->hasMany("App\Subjectselection");
    }
    public function scrapbook()
    {
        return $this->hasMany("App\Scrapbook");
    }
    public function sendaletter()
    {
        return $this->hasMany("App\Letterquestion");
    }
    public function careertimeline()
    {
        return $this->hasMany('App\Careertimeline');
    }

    public function templateProgress()
    {
        return $this->hasMany("App\StudentTemplateProgress", 'template_id');
    }

    public function userTemplateProgress($id = null)
    {
        $id = $id ?? auth()->id();
        return $this->hasOne("App\StudentTemplateProgress", 'template_id')->where('student_id', $id)->latest();
    }

    public static function scopePublished($query)
    {
        return $query->where('publish', "1")->has('industries');
    }

    public static function scopeUserSubjectSelectionGuide($query)
    {
        return $query->where('type', 'subjectselectionguide')
            ->when(\Auth::check() || !(\Auth::user()->isAdmin()), function ($q) {
                return $q->whereHas('states', function ($q) {
                    $q->whereStateId(\Auth::user()->state_id);
                });
            });
    }

    // public static function scopeRemoveSubjectSelection($query)
    // {
    //     return $query->where('type', '<>', 'subjectselectionguide')->orWhere(function ($query) {
    //         $query->where('type', 'subjectselectionguide')
    //             ->when(\Auth::check() || !(\Auth::user()->isAdmin()), function ($q) {
    //                 return $q->whereHas('states', function ($q) {
    //                     $q->whereStateId(\Auth::user()->state_id);
    //                 });
    //             });
    //     });
    // }


    /* for Subject Selection Guide */
    public function states()
    {
        return $this->belongsToMany("App\State");
    }

    public function locations()
    {
        return $this->hasMany(IndustryunitLocation::class);
    }

    public function getIsBookmarkedAttribute()
    {
        return \Auth::check() && \Auth::user()->industryunits()->pluck('industryunit_id')->contains($this->id);
    }

    public function isBookmarked($industryunit)
    {
        return \Auth::check() && $industryunit->contains($this->id);
    }

    public function users()
    {
        return $this->belongsToMany("App\User");
    }

    public function gethelps()
    {
        return $this->hasMany(IndustryunitGethelp::class);
    }

    public function sendtparents()
    {
        return $this->hasMany(IndustryunitSendtoparent::class);
    }

    public function skillstrainingTemplates()
    {
        return $this->belongsToMany(SkillstrainingTemplate::class);
    }

    public function workexperienceTemplates()
    {
        return $this->belongsToMany(WorkexperienceTemplate::class);
    }

    public function workingStyles()
    {
        return $this->belongsToMany(WorkingStyle::class);
    }

    protected function bannerFullpath(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->inner_banner ? Storage::url($this->inner_banner) : null
        );
    }

    protected function favourite(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->hasBeenFavoritedBy(Auth::user())
        );
    }

    /**
     * Get url for featured image stored on Amazon S3.
     *
     * @param string $path
     * @return string
     */
    public function featuredImageUrl(string $path): string
    {
        return Storage::disk('s3')->url($path);
    }

    protected function tileimageFullpath(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->banner ? Storage::url($this->banner) : null
        );
    }

    protected function timelineprofileimageFullpath(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->timeline_profile_image ? Storage::url($this->timeline_profile_image) : null
        );
    }

    public function audiofullpath(): Attribute
    {
        return Attribute::make(
            get: fn() => ($this->audio && Storage::exists($this->audio->path)) ? Storage::url($this->audio->path) : null
        );
    }

    public function jobSearchKeyword(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->job_search) {
                    return $this->job_search;
                } elseif (@$this->tagged->where('tag_level', 'primary')->count()) {
                    return $this->tagged->where('tag_level', 'primary')->pluck('tag_name')->implode(', ');
                } else {
                    return $this->industries()->select('industry_categories.id', 'name')->get()->append('search_keyword')->pluck('search_keyword')->implode(', ');
                }
            }
        );
    }
}
