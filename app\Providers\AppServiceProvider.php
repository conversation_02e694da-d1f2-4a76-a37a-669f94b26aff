<?php

namespace App\Providers;

// use App\Channel;

use App\Gameplan;
use App\GameplanQuestion;
use App\GameplanQuestionOption;
use App\IndustryCategory;
use Cookie;
use Illuminate\Auth\Events\Authenticated;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use App\SeoPage;
use SEO;
use App\Observers\UserObserver;
use App\User;
use App\Observers\IndustryunitObserver;
use App\Industryunit;
use App\Observers\StudentModuleResultObserver;
use App\Standard;
use App\StudentModuleResult;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Pagination\Paginator;
use DB;
use Illuminate\Support\Facades\Cache;
use Doctrine\DBAL\Types\Type;
use Illuminate\Support\Facades\Auth;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(UrlGenerator $url)
    {
        Paginator::useBootstrap();

        if (config('app.env') !== 'local') {
            $url->forceScheme('https');
        }
        Schema::defaultStringLength(191);
        User::observe(UserObserver::class);
        Industryunit::observe(IndustryunitObserver::class);
        StudentModuleResult::observe(StudentModuleResultObserver::class);

        // \View::composer('*', function ($view) {
        //     $channels = \Cache::rememberForever('channels', function () {
        //         return Channel::all();
        //     });

        //     $view->with('channels', $channels);
        // });

        if (!Type::hasType('enum')) {
            Type::addType('enum', 'Doctrine\DBAL\Types\StringType');
        }


        /* Set SEO meta tags */
        if (!request()->ajax()) {
            $currenturl = preg_replace('/^www\./', '', preg_replace("#^[^:/.]*[:/]+#i", "", preg_replace("{/$}", "", urldecode(url()->current()))));
            // phpinfo();die();
            // if(DB::connection()->getDatabaseName() && $currenturl)
            // {
            //     $page = SeoPage::where('url', 'like', '"%' . $currenturl.'"')->first();
            //     // ddd($currenturl);
            //     if ($page) {
            //         SEO::setTitle($page->title);
            //         SEO::setDescription($page->description);
            //         SEO::opengraph()->setUrl($page->url);
            //         if (!empty($page->keywords)) {
            //             SEO::metatags()->setKeywords($page->keywords);
            //         }
            //         if ($page->imagepath) {
            //             SEO::opengraph()->addImage($page->imagepath);
            //         }
            //     }
            // }



            //             /* End SEO meta tags */



            $this->app['events']->listen(Authenticated::class, function ($e) {
                view()->share('user', $e->user);
                $user = $e->user;

                if ($user) {

                    view()->share('planPopup', false);


                    if ($user->isStudent() || (($user->isTeacher() || $user->isStaff()) && session('studentView'))) {
                        // $map = $user->mapData();
                        // view()->share('map', $map);
                        $industries = Cache::rememberForever('allIndustries', function () {
                            return IndustryCategory::explore()->select('name', 'id', 'keyword')->get();
                        });
                        view()->share('allIndustries', $industries);
                    }

                    // if ($user->isStudent()) {

                    //     $lastplan = $user->lastPlan();
                    //     $standards = Standard::secondarySchool()->pluck('title', 'id');

                    //     view()->share('standards', $standards);
                    //     $popup = Cookie::get('planPopup');
                    //     if (!$popup) {
                    //         view()->share('planPopup', $user->hasGamePlanAccess());
                    //     }
                    //     config(['lastplan' => $lastplan]);
                    //     view()->share('latestPlan', $lastplan);
                    // }

                    if ($user->isStudent()) {
                        $gameplan = $user->lastPlan();
                        $standards = Standard::secondarySchool()->pluck('title', 'id');

                        $instituteDetail = $user->school?->detail;
                        $instituteId = $instituteDetail?->school_id;
                        $instituteType = $instituteDetail?->institute_type->value ?? null;
                
                        $questions = GameplanQuestion::with('options')
                        ->orderBy('sort_order')
                        ->when(Auth::user()->isIndividual(), function ($query) {
                                $query->where('question_key', '<>', 'finishing_school');
                            })
                        ->where(function ($query) use ($instituteType, $instituteId) {
                            $query->where('institute_type', $instituteType)
                                ->where(function ($subQuery) use ($instituteId) {
                                    $subQuery->whereNull('institute_id')
                                            ->orWhere('institute_id', $instituteId);
                                })
                                ->orWhereNull('institute_type');
                        })
                        ->get();

                        view()->share('standards', $standards);
                        // Share multiple variables
                        view()->share([
                            'gameplan' => $gameplan,
                            'questions' => $questions,
                        ]);

                        $popup = Cookie::get('planPopup');
                        if (!$popup) {
                            view()->share('planPopup', $user->hasGamePlanAccess());
                        }
                    }

                    if ($user->isTeacher() || $user->isStaff()) {
                        $popup = Cookie::get('teacherLoggedInPopup');
                        if (!$popup) {
                            view()->share('teacherLoggedInPopup', true);
                        }
                    } else {

                        view()->share('teacherLoggedInPopup', false);
                    }
                }
            });
        }

        \Validator::extend('spamfree', 'App\Rules\SpamFree@passes');
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->isLocal()) {
            // $this->app->register(\Barryvdh\Debugbar\ServiceProvider::class);
        }
        \Stripe\Stripe::setApiKey(\Config::get('services.stripe.secret'));
    }
}
