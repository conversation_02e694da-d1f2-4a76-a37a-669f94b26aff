import { PropsWithChildren } from 'react';
import { ButtonType } from '@/utils/enums';
import Button from '../base/Button/Button';

type SORTING = 'ASC' | 'DESC';

interface Props {
    className?: string;
    isHidden: boolean;
    onApply?: () => void;
    onReset?: () => void;
    isSorting?: boolean;
    title: string;
    withButtons?: boolean;
}

export default function Filter({
    isHidden,
    onApply,
    onReset,
    className,
    title,
    withButtons = false,
    children,
}: PropsWithChildren<Props>) {
    return (
        <div
            className={`filter-component ${
                className ?? ''
            } menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-rounded menu-gray-600 menu-state-bg-light-primary ${
                isHidden ? 'hidden' : ''
            }`}
        >
            <span className='filter-component__title fs-5 fw-bold'>{title}</span>
            {children}
            {withButtons && (
                <div className='filter-component__button-row'>
                    <Button
                        className='mb-6 fs-7 fw-normal filter-component__button grey btn-sm'
                        onClick={onReset}
                        title='Reset'
                        type={ButtonType.GRAY}
                    />
                    <Button
                        className='mb-6 fs-7 fw-normal filter-component__button btn-sm'
                        onClick={onApply}
                        title='Apply'
                        type={ButtonType.PRIMARY}
                    />
                </div>
            )}
        </div>
    );
}
