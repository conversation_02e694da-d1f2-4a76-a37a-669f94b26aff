<?php

namespace App\Http\Controllers;

use App\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class BannersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $banners = Banner::pluck('type', 'id');
        $types = collect(["Lessons", "Virtual Work Experience", "Skills Training", "Industries", "Course Finder", "Job Finder", "Scholarship Finder"]);
        return view('banners.index', compact('banners', 'types'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $banner = new Banner;
        $banner->type = $request->type;
        $banner->video = trim($request->video);
        if ($request->image) {
            $image = $request->image->store('banners', ['visibility' => 'public']);
            $banner->image = $image;
        }
        $banner->trailer_video = trim($request->trailer_video);
        $banner->save();

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Banner details has been saved successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Banner  $banner
     * @return \Illuminate\Http\Response
     */
    public function show(Banner $banner)
    {
        if (request()->wantsJson()) {
            $banner->imagefullpath = $banner->imagefullpath;
            return $banner;
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Banner  $banner
     * @return \Illuminate\Http\Response
     */
    public function edit(Banner $banner)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Banner  $banner
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Banner $banner)
    {
        $banner->video = trim($request->video);
        if ($request->image) {
            if ($banner->image && Storage::exists($banner->image)) {
                Storage::delete($banner->image);
            }
            $image = $request->image->store('banners', ['visibility' => 'public']);
            $banner->image = $image;
        } elseif (!$request->image && !$request->old_image) {
            if ($banner->image && Storage::exists($banner->image)) {
                Storage::delete($banner->image);
            }
            $banner->image = null;
        }
        $banner->trailer_video = trim($request->trailer_video);
        $banner->save();

        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Banner details has been updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Banner  $banner
     * @return \Illuminate\Http\Response
     */
    public function destroy(Banner $banner)
    {
        $banner->delete();
        if (request()->wantsJson()) {
            return response([], 204);
        }
        $redirectUrl = url()->previous();
        if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
            $redirectUrl = session()->get('previousUrl');
        }
        return redirect($redirectUrl)->with('message', 'Banner has been deleted successfully!');
    }

    public function getBanner($type)
    {
        return Banner::whereType($type)->first();
    }
}
