import { useLogOut, useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { useState } from 'react';
import Breadcrumbs from '../Breadcrumbs/Breadcrumbs';

export default function Header() {
    const { data } = useUserData();
    const { refetch: logOut } = useLogOut();
    const [isAvatarMenu, setIsAvatarMenu] = useState(false);

    function handleAvatarMenuToggle() {
        setIsAvatarMenu(!isAvatarMenu);
    }

    async function handleLogOut() {
        logOut();
        await new Promise((resolve) => setTimeout(resolve, 1000));
        window.location.href = '/#/';
    }

    return (
        <div id='kt_app_header' className='app-header w-100 position-fixed'>
            <div className='d-flex app-header__container'>
                <div
                    className='app-header__avatar cursor-pointer symbol symbol-35px symbol-md-40px align-self-center'
                    data-kt-menu-trigger='click'
                    data-kt-menu-attach='parent'
                    data-kt-menu-placement='bottom-end'
                    onClick={handleAvatarMenuToggle}
                >
                    <img src='/images/avatars/default.png' alt='Avatar' />
                    {isAvatarMenu && (
                        <div className='app-header__avatar-menu py-4 menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semobold fs-6 w-275px'>
                            <div className='info d-flex align-items-center'>
                                <img src='/images/avatars/default.png' alt='Avatar' />
                                <div className='info__text w-100 ms-3'>
                                    <span className='d-block fw-bold '>{data?.name}</span>
                                    <span className='d-block fw-semobold text-muted text-hover-primary'>
                                        {data?.email}
                                    </span>
                                </div>
                            </div>

                            <div className='my-2 separator'></div>
                            <div className='px-5 menu-item'>
                                <a href='/profiles/edit' className='px-5 menu-link'>
                                    My Profile
                                </a>
                            </div>
                            <div className='px-5 menu-item'>
                                <a onClick={handleLogOut} className='px-5 menu-link'>
                                    {' '}
                                    Sign Out{' '}
                                </a>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <Breadcrumbs />
        </div>
    );
}
