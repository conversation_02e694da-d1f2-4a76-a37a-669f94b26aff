import { useEffect, useState } from 'react';
import studentNavigation from '../../../common/navigation/studentNavigation';
import teacherNavigation from '../../../common/navigation/teacherNavigation';
import SidebarLogo from './SidebarLogo';
import SidebarMenu from './SidebarMenu';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { Roles, UserResponse } from '@/domains/Auth/models/Auth.model';
import { NavigationPages } from '@/common/models/Navigation.model';

export default function Sidebar() {
    const { isLoading, data: user } = useUserData();
    const [navigation, setNavigation] = useState<NavigationPages[]>([]);

    useEffect(() => {
        setNavigation(getUserNavigation(user));
    }, [user]);

    return (
        <div
            className={`app-aside flex-column`}
            data-kt-drawer='true'
            data-kt-drawer-name='aside'
            data-kt-drawer-activate='{default: true, lg: false}'
            data-kt-drawer-overlay='true'
            data-kt-drawer-width='140px'
            data-kt-drawer-direction='start'
            data-kt-drawer-toggle='#kt_app_sidebar_mobile_toggle'
        >
            <SidebarLogo />
            {(isLoading && (
                <div className='h-100 d-flex flex-column align-items-center' style={{ marginTop: 200 }}>
                    {Array(4)
                        .fill(0)
                        .map((_, index) => (
                            <div
                                key={index}
                                className='spinner-grow mb-10'
                                style={{ width: '2rem', height: '2rem', backgroundColor: 'rgb(255 255 255 / 20%)' }}
                                role='status'
                            >
                                <span className='visually-hidden'>Loading...</span>
                            </div>
                        ))}
                </div>
            )) || <SidebarMenu navigation={navigation} />}
        </div>
    );
}

function getUserNavigation(user: UserResponse | undefined): NavigationPages[] {
    if (!user) {
        return [];
    }

    if (user.role === Roles.Student) {
        return studentNavigation;
    } else if (user.role === Roles.Teacher) {
        return teacherNavigation;
    } else {
        return [];
    }
}
