<?php

namespace App;

use App\Services\TimelineService;
use Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Plan extends Model
{
    // use LogsActivity;



    protected $table = "plans";
    protected $dates = ['created_at', 'updated_at'];

    protected static $logAttributes = ['user_id', 'updated_at'];






    // public function getActivitylogOptions(): LogOptions
    // {
    //    return LogOptions::defaults()
    //    -> logFillable();
    // }

    public function getDescriptionForEvent($eventName)
    {
        if ($eventName == 'created') {
            return ('<a href="students/' . Auth::id() . '/timeline">' . Auth()->user()->name . ' updated their plan</a>');
        }
        return false;
    }
    protected $appends = ['InstituteOther', 'Workplace'];
    protected $guarded = [];
    protected $hidden = [
        'updated_at',
    ];
    /**
     * Determine if the current user is subscribed to the thread.
     *
     * @return boolean
     */
    // public function getStudyOtherAttribute()
    // {
    //     $institutes = [];
    //     if ($this->studyoption == "pickingmany") {
    //         $institutes = DB::table('plan_study_category')->where('plan_id', $this->id)->where('study_category_other', '<>', '')->pluck('study_category_other');
    //     }
    //     return $institutes;
    // }

    protected static function booted()
    {

        static::deleting(function ($plan) {
            if ($plan->institituteOthers) {
                $plan->institituteOthers()->delete();
            }
            if ($plan->tafes) {
                $plan->tafes()->detach();
            }
            if ($plan->universities) {
                $plan->universities()->detach();
            }
            if ($plan->industries) {
                $plan->industries()->detach();
            }

            if ($plan->activityLog) {
                $plan->activityLog()->delete();
            }
        });

        static::created(function ($plan) {
            (new TimelineService)->log(
                $plan, // model
                'created', // event
                'created', // description
            );
        });
    }

    // public function studycategories()
    // {
    //     return $this->belongsToMany(StudyCategory::class);
    // }

    public function getInstituteOtherAttribute()
    {
        $institutes = collect();
        if ($this->studyplace == "pickingmany") {
            $institutes = DB::table('institute_plan')->where('plan_id', $this->id)->where('institute_type', 'Other')->pluck('institute_other');
        }
        return $institutes;
    }

    public function institituteOthers()
    {
        return $this->hasMany(InstitutePlan::class)->where('institute_type', 'Other');
    }

    public function tafes()
    {
        return $this->belongsToMany(Institute::class)->where('institute_type', 'TAFE')->withTrashed();;
    }

    public function colleges()
    {
        return $this->belongsToMany(College::class, 'institute_plan', 'plan_id', 'institute_id')->where('institute_type', 'College')->withTrashed();;
    }

    public function universities()
    {
        return $this->belongsToMany(University::class, 'institute_plan', 'plan_id', 'institute_id')->where('institute_type', 'University')->withTrashed();;
    }

    public function getWorkplaceAttribute()
    {
        $industries = [];
        if ($this->studyplace == "multiple") {
            $institutes = DB::table('industry_category_plan')->where('plan_id', $this->id)->where('industry_other', '<>', '')->pluck('industry_other');
        }
        return $industries;
    }

    public function industries()
    {
        return $this->belongsToMany(IndustryCategory::class);
    }

    public function scopeNewest($query)
    {
        return $query->latest();
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'user_id');
    }

    public function individual()
    {
        return $this->belongsTo(IndividualStudent::class, 'user_id');
    }

    public function getInterestedAttribute()
    {
        if ($this->attributes['interestedin'] == 'notsure') {
            return 'Not sure yet';
        } elseif ($this->attributes['interestedin'] == 'gettingajob') {
            return 'Going straight into a job (not studying or doing a trade)';
        } elseif ($this->attributes['interestedin'] == 'gettingtrade') {
            return 'Getting an apprenticeship/trade';
        } elseif ($this->attributes['interestedin'] == 'gapyear') {
            return 'Going on a gap year';
        } elseif ($this->attributes['interestedin'] == 'continuestudy') {
            return 'Continue studying';
        } elseif ($this->attributes['interestedin'] == 'enteringdefence') {
            return 'Entering the Defence Force or going through ADFA';
        }
    }

    public function activityLog()
    {
        return $this->morphOne(ActivityLog::class, 'subject');
    }
}
