import useSelectionNavigation from '../../utils/useSelectionNavigation';
import { SelectionSteps } from '../../models/SubjectSelection.model';
import StepLayout from '../../components/StepLayout/StepLayout';
import copy from '../../utils/content.json';
import { useEffect } from 'react';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';
import { setNavigation, setQuizSelection } from '../../context/SubjectSelection.actions';
import { useQuiz, useQuizResults, useQuizSubmission } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import LabelRadio from '@/components/base/LabelRadio/LabelRadio';
import useModal from '@/hooks/useModal/useModal';
import RecommendationsCalculationLayout from '@/components/layouts/RecommendationsCalculationLayout/RecommendationsCalculationLayout';

export default function SubjectSelectionMyElectivesView() {
    const content = copy.subjectSelection[SelectionSteps.ELECTIVES];
    const dispatch = useSubjectSelectionDispatch();

    const { data } = useQuiz();
    const { refetch: submitQuiz } = useQuizSubmission();
    const { refetch: getResults } = useQuizResults();
    const { quizSelection } = useSubjectSelectionContextStore();

    const handleCheckboxClick = (id: number) => {
        dispatch(setQuizSelection({ key: SelectionSteps.ELECTIVES, value: id }));
    };

    useSelectionNavigation({
        currentStep: SelectionSteps.ELECTIVES,
    });

    const { isOpen, Modal, toggle } = useModal({
        isPage: true,
    });

    useEffect(() => {
        dispatch(
            setNavigation({
                submit: async () => {
                    submitQuiz();
                    getResults();
                    toggle();
                },
            }),
        );
    }, []);

    return (
        <div className='electives-view'>
            <StepLayout title={content.title} description={content.description} progress={content.progress}>
                <div className='electives-view__list-container'>
                    {data?.electives &&
                        data.electives.map((elective) => {
                            const isSelected = quizSelection.electives.includes(elective.id);

                            return (
                                <LabelRadio
                                    key={elective.id}
                                    id={elective.id}
                                    text={elective.title}
                                    onClick={handleCheckboxClick}
                                    selected={isSelected}
                                />
                            );
                        })}
                </div>
                {isOpen && (
                    <Modal>
                        <RecommendationsCalculationLayout onButtonClick={toggle} />
                    </Modal>
                )}
            </StepLayout>
        </div>
    );
}
