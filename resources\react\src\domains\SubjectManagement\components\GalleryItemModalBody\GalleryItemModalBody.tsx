import Select, { SingleValue } from 'react-select';
import { GalleryType, SubjectGalleryItemRequest } from '../../models/Subjects.service.api.model';
import { useState } from 'react';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';

interface Props {
    onCancel: () => void;
    onSubmit: (data: SubjectGalleryItemRequest) => void;
    itemForEdit?: SubjectGalleryItemRequest;
}

export default function GalleryItemModalBody({ onCancel, onSubmit, itemForEdit }: Props) {
    const [data, setData] = useState<SubjectGalleryItemRequest>(
        itemForEdit ?? {
            type: GalleryType.Image,
            caption: '',
            description: '',
            src: '',
            file: undefined,
        },
    );

    const options = [
        { value: GalleryType.Image, label: 'Image' },
        { value: GalleryType.Youtube, label: 'Youtube' },
        { value: GalleryType.Vimeo, label: 'Vimeo' },
        { value: GalleryType.Wistia, label: 'Wistia' },
        { value: GalleryType.Iframe, label: 'Iframe' },
    ];

    const handleSubmit = (e: any) => {
        e.preventDefault();
        onSubmit(data);
    };

    const handleChange = (key: string, value: any) => {
        setData({ ...data, [key]: value });
    };

    const handleFileChange = (event: any) => {
        const file = event.target.files[0];
        handleChange('file', file);
    };

    const handleTypeChange = (
        newValue: SingleValue<{
            value: GalleryType;
            label: string;
        }>,
    ) => {
        handleChange('type', newValue?.value);
    };

    const formGroup = new Map([
        [
            GalleryType.Image,
            <div key='image' className='form-group'>
                <label htmlFor='file'>Image</label>
                <input type='file' onChange={handleFileChange} />
            </div>,
        ],
        [
            GalleryType.Youtube,
            <div key='youtube' className='form-group'>
                <label htmlFor='src'>Youtube link</label>
                <input
                    type='text'
                    id='src'
                    value={data?.src}
                    onChange={(e) => handleChange('src', e.target.value)}
                    placeholder='e.g https://youtu.be/o-YBDTqX_ZU'
                />
            </div>,
        ],
        [
            GalleryType.Vimeo,
            <div key='vimeo' className='form-group'>
                <label htmlFor='src'>Vimeo link</label>
                <input
                    type='text'
                    id='src'
                    value={data?.src}
                    onChange={(e) => handleChange('src', e.target.value)}
                    placeholder='e.g https://vimeo.com/375468729'
                />
            </div>,
        ],
        [
            GalleryType.Wistia,
            <div key='wistia' className='form-group'>
                <label htmlFor='src'>Wistia link</label>
                <input
                    type='text'
                    id='src'
                    value={data?.src}
                    onChange={(e) => handleChange('src', e.target.value)}
                    placeholder='e.g https://private-sharing.wistia.com/medias/mwhrulrucj'
                />
            </div>,
        ],
        [
            GalleryType.Iframe,
            <div
                key='iframe'
                className='form-group'
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                }}
            >
                <label htmlFor='src'>Embed</label>
                <textarea
                    id='src'
                    style={{
                        height: '120px',
                        backgroundColor: 'rgb(244, 241, 241)',
                        padding: '10px',
                        borderRadius: '4px',
                        outline: 'none',
                        border: 'none',
                    }}
                    value={data?.src}
                    onChange={(e) => handleChange('src', e.target.value)}
                    placeholder='place your iframe embedded code'
                />
            </div>,
        ],
    ]);

    return (
        <div className='gallery-item-modal-body'>
            <form onSubmit={handleSubmit}>
                <div className='form-group-select'>
                    <Select
                        id='select'
                        value={options.find((option) => option.value === data?.type)}
                        options={options}
                        onChange={handleTypeChange}
                    />
                </div>
                <div className='form-group'>
                    <label htmlFor='caption'>Caption</label>
                    <input
                        type='text'
                        id='caption'
                        value={data?.caption}
                        onChange={(e) => handleChange('caption', e.target.value)}
                    />
                </div>
                <div className='form-group'>
                    <label htmlFor='description'>Description</label>
                    <input
                        type='text'
                        id='description'
                        value={data?.description}
                        onChange={(e) => handleChange('description', e.target.value)}
                    />
                </div>
                {data?.type && formGroup.get(data.type)}
                <div className='button-group'>
                    <Button type={ButtonType.OUTLINED_BLACK} title={'Ok'} spanClassName='fs-6' element='submit' />
                    <Button
                        type={ButtonType.OUTLINED_BLACK}
                        title={'Cancel'}
                        spanClassName='fs-6'
                        element='button'
                        onClick={onCancel}
                    />
                </div>
            </form>
        </div>
    );
}
