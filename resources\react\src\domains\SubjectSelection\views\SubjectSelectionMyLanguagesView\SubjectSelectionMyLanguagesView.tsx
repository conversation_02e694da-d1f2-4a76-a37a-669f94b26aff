import useSelectionNavigation from '../../utils/useSelectionNavigation';
import { SelectionSteps } from '../../models/SubjectSelection.model';
import StepLayout from '../../components/StepLayout/StepLayout';
import copy from '../../utils/content.json';
import { useQuiz } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';
import { setQuizSelection } from '../../context/SubjectSelection.actions';
import LabelRadio from '@/components/base/LabelRadio/LabelRadio';

export default function SubjectSelectionMyLanguagesView() {
    useSelectionNavigation({ currentStep: SelectionSteps.LANGUAGES });
    const { data } = useQuiz();
    const { quizSelection } = useSubjectSelectionContextStore();
    const dispatch = useSubjectSelectionDispatch();

    const handleCheckboxClick = (id: number) => {
        dispatch(setQuizSelection({ key: SelectionSteps.LANGUAGES, value: id }));
    };

    const content = copy.subjectSelection[SelectionSteps.LANGUAGES];

    return (
        <div className='languages-view'>
            <StepLayout title={content.title} description={content.description} progress={content.progress}>
                <div className='languages-view__list-container'>
                    {data?.languages.map((language) => {
                        const isSelected = quizSelection.languages.includes(language.id);

                        return (
                            <LabelRadio
                                key={language.id}
                                id={language.id}
                                text={language.name}
                                onClick={handleCheckboxClick}
                                selected={isSelected}
                            />
                        );
                    })}
                </div>
            </StepLayout>
        </div>
    );
}
