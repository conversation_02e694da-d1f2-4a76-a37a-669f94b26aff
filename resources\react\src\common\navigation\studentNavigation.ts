import HomeSolid from '../../assets/icons/sidebar/student/HomeSolid';
import UserSolid from '../../assets/icons/sidebar/student/UserSolid';
import Explore from '../../assets/icons/sidebar/student/Explore';
import LaptopSolid from '../../assets/icons/sidebar/student/LaptopSolid';
import ToolboxSolid from '../../assets/icons/sidebar/student/ToolboxSolid';
import Connect from '../../assets/icons/sidebar/student/Connect';
import Support from '@/assets/icons/sidebar/student/Support';
import { NavigationPages } from '../models/Navigation.model';

const studentNavigation: NavigationPages[] = [
    {
        title: 'Home',
        route: '/#/dashboard',
        isExternal: true,
        svgIcon: HomeSolid,
        fontIcon: 'bi-app-indicator',
    },
    {
        title: 'Profile',
        isExternal: true,
        route: '/profiles/edit',
        svgIcon: UserSolid,
        fontIcon: 'bi-person',
    },
    {
        title: 'Explore',
        route: '/explore',
        isExternal: true,
        svgIcon: Explore,
        fontIcon: 'bi-person',
        sub: [
            {
                title: 'Industries',
                route: '/exploreindustries',
                isExternal: true,
            },
            {
                title: 'e-Magazine',
                route: '/e-magazines/editions',
                isExternal: true,
            },
        ],
    },

    {
        title: 'Tasks',
        route: '/profile',
        isExternal: true,
        svgIcon: LaptopSolid,
        fontIcon: 'bi-stack',
        sub: [
            {
                title: 'My Path',
                route: 'my-path',
                isExternal: true,
            },
            {
                title: 'Lessons',
                route: '/#/tasks/lessons',
                isExternal: true,
            },
            {
                title: 'Virtual Work Experience',
                route: '/exploreworkexperience',
                isExternal: true,
            },
            {
                title: 'Skills Training',
                route: '/wew/skillstraining',
                isExternal: true,
            },
            {
                title: 'Work, Health & Safety',
                route: '/wew/workhealthsafety',
                isExternal: true,
            },
        ],
    },
    {
        title: 'Tools',
        route: '/profile',
        svgIcon: ToolboxSolid,
        fontIcon: 'bi-bar-chart',
        isExternal: true,
        sub: [
            {
                title: 'Job Finder',
                route: '/jobfinder',
                isExternal: true,
                inNewTab: true,
            },
            {
                title: 'Scholarship Finder',
                route: '/scholarships',
                isExternal: true,
            },
            {
                title: 'Resume Builder',
                route: '/cvs',
                isExternal: true,
            },
            {
                title: 'Course Finder',
                route: '/coursefinder',
                isExternal: true,
            },
            {
                title: 'ePortfolio',
                route: '/eportfolio',
                isExternal: true,
            },
            {
                title: 'Subject Selections',
                route: '/subjects-selection/student',
            },
            {
                title: 'Video Profiling',
                route: 'video/profiling',
                isExternal: true,
            },
            {
                title: 'Career Profiling',
                route: 'career/profiling',
                isExternal: true,
            },
        ],
    },
    {
        title: 'Support',
        route: '#',
        svgIcon: Support,
        fontIcon: 'bi-node-minus',
        svgClass: 'connect-icon',
        isExternal: true,
        sub: [
            // {
            //     title: 'Noticeboard',
            //     route: '/noticeboard',
            //     isExternal: true,
            // },

            {
                title: 'Help Centre',
                route: 'https://help.thecareersdepartment.com/en/',
                isExternal: true,
                inNewTab: true,
            },
        ],
    },
];

export default studentNavigation;
