import { useEffect, useRef, useState } from 'react';
import FilterIcon from '@/assets/icons/subjectSelection/FilterIcon';
import SearchIcon from '@/assets/icons/subjectSelection/SearchIcon';
import Button from '@/components/base/Button/Button';
import SubjectCard from '@/components/Cards/SubjectCard/SubjectCard';
import { ButtonType } from '@/utils/enums';
import { useQuizResults, useSubjects, useSubmitPreferences } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { generatePath, useNavigate } from 'react-router-dom';
import { SUBJECTS_SELECTION, SUBJECT_DETAILS } from '@/router/Router.constants';
import { useAppContextStore, useAppDispatch } from '@/context/App.context';
import { setSelectedPreferencesState } from '@/context/App.actions';
import { SubjectPreference, SubjectsMap } from '../../models/SubjectSelection.model';
import SearchMenu from '../../components/SearchMenu/SearchMenu';
import { SubjectPreferenceStatus } from '../../models/SubjectSelection.api.model';
import axios from 'axios';

enum HeaderSelection {
    FILTER = 'filter',
    SEARCH = 'search',
}

type CourseType = {
    id: number;
    name: string;
};

type SubjectType = {
    id: number;
    name: string;
};

interface Filter {
    subjectAreaId: string;
    subjectType: string;
    sorting: SORTING;
    courseType:string;
}

type SORTING = 'ASC' | 'DESC';

export default function SubjectResultsView() {
    const navigate = useNavigate();
    const { isLoading } = useSubjects();
    const { data: quizResults } = useQuizResults();
    const { mappedSubjects } = useAppContextStore();
    const [selected, setSelected] = useState<HeaderSelection | undefined>(undefined);
    const [search, setSearch] = useState('');
    const [filter, setFilter] = useState<Filter>({ subjectAreaId: 'all', subjectType: 'all', sorting: 'DESC', courseType: 'all'});

    const [subjects, setSubjects] = useState(mappedSubjects);


    useEffect(() => {
        setSubjects(mappedSubjects);
    }, [mappedSubjects]);

    const handleFilterClick = () => {
        if (selected === HeaderSelection.FILTER) {
            setSelected(undefined);
            return;
        }
        setSelected(HeaderSelection.FILTER);
    };

    const handleSearchClick = () => {
        if (selected === HeaderSelection.SEARCH) {
            setSelected(undefined);
            return;
        }
        setSelected(HeaderSelection.SEARCH);
    };

    // function handleApplyFilter() {
    //     if (!mappedSubjects) return;

    //     if (filter.subjectAreaId === 'all' && filter.subjectType.toLocaleLowerCase() === 'all' && filter.courseType.toLocaleLowerCase() === 'all') {
    //         setSubjects(sortSubjects(mappedSubjects));
    //         handleFilterClick();
    //         return;
    //     }

    //     const filteredSubjects = new Map<number | 'other', SubjectsMap>();

    //     if (filter.subjectAreaId === 'all' && filter.subjectType.toLocaleLowerCase() !== 'all') {
    //         for (const [key, value] of Array.from(mappedSubjects)) {
    //             const newItem = {
    //                 ...value,
    //                 subjects: value.subjects.filter(
    //                     (subject) => subject.type.toLowerCase() === filter.subjectType.toLowerCase(),
    //                 ),
    //             };

    //             filteredSubjects.set(key, newItem);
    //         }
    //         setSubjects(sortSubjects(filteredSubjects));
    //         handleFilterClick();
    //         return;
    //     }

    //     let existingItem = mappedSubjects.get(parseInt(filter.subjectAreaId, 10));

    //     if (existingItem && filter.subjectType && filter.subjectType.toLowerCase() !== 'all') {
    //         existingItem = {
    //             ...existingItem,
    //             subjects: existingItem.subjects.filter(
    //                 (subject) => subject.type.toLowerCase() === filter.subjectType.toLowerCase(),
    //             ),
    //         };
    //     }

    //     filteredSubjects.set(parseInt(filter.subjectAreaId, 10), existingItem as SubjectsMap);

    //     setSubjects(sortSubjects(filteredSubjects));
    //     handleFilterClick();
    // }



    // Function to get all course types




    function handleApplyFilter() {
        if (!mappedSubjects) return;

        if (filter.subjectAreaId === 'all' && filter.subjectType.toLocaleLowerCase() === 'all' && filter.courseType.toLocaleLowerCase() === 'all') {
            setSubjects(sortSubjects(mappedSubjects));
            handleFilterClick();
            return;
        }

        const filteredSubjects = new Map<number | 'other', SubjectsMap>();

        for (const [key, value] of Array.from(mappedSubjects)) {
            let newItem = { ...value };

            if (filter.subjectType.toLocaleLowerCase() !== 'all') {
                newItem = {
                    ...newItem,
                    subjects: newItem.subjects.filter(
                        (subject) => subject.type.toLowerCase() === filter.subjectType.toLowerCase()
                    ),
                };
            }

            if (filter.courseType.toLocaleLowerCase() !== 'all') {
                newItem = {
                    ...newItem,
                    subjects: newItem.subjects.filter(
                        (subject) => subject.course_type?.toLowerCase() === filter.courseType.toLowerCase()
                    ),
                };
            }

            if (filter.subjectAreaId === 'all' || key.toString() === filter.subjectAreaId) {
                filteredSubjects.set(key, newItem);
            }
        }

        setSubjects(sortSubjects(filteredSubjects));
        handleFilterClick();
    }


    function handleSearchFilter(e: React.ChangeEvent<HTMLInputElement>) {
        if (!subjects) return;

        setSearch(e.target.value);
        const filteredSubjects = new Map<number | 'other', SubjectsMap>();

        if (e.target.value === '') {
            setFilter({ subjectAreaId: 'all', subjectType: 'All', sorting: 'DESC',courseType: 'all' });
            setSubjects(mappedSubjects);
            return;
        }

        for (const [key, value] of Array.from(subjects)) {
            const newItem = {
                ...value,
                subjects: value.subjects.filter((subject) =>
                    subject.name.toLowerCase().includes(e.target.value.toLowerCase()),
                ),
            };

            filteredSubjects.set(key, newItem);
        }

        setSubjects(filteredSubjects);
    }

    function sortSubjects(filteredSubjects: Map<number | 'other', SubjectsMap>) {
        let sortedMap = filteredSubjects;

        if (filter.sorting === 'ASC') {
            sortedMap = new Map(Array.from(sortedMap).sort((a, b) => a[1].subjectAreaScore - b[1].subjectAreaScore));
        }

        if (filter.sorting === 'DESC') {
            sortedMap = new Map(Array.from(sortedMap).sort((a, b) => b[1].subjectAreaScore - a[1].subjectAreaScore));
        }

        return sortedMap;
    }

    function handleResetFilter() {
        setFilter({ subjectAreaId: 'all', subjectType: 'All', sorting: 'DESC',courseType: 'all'});
        setSubjects(mappedSubjects);
        handleFilterClick();
    }



    return (
        <div className='results-container'>
            <div className='results-container__results-header'>
                <div className='results-container__results-header__container px-20'>
                    <div style={{ maxWidth: 1495 }}>
                        <div className='results-container__header-icons-container'>
                            <span
                                className={`results-container__filter-icon ${
                                    selected === HeaderSelection.FILTER ? 'selected' : ''
                                }`}
                                onClick={handleFilterClick}
                            >
                                <FilterIcon />
                            </span>
                            <FilterComponent
                                isHidden={selected !== HeaderSelection.FILTER}
                                isSorting={quizResults?.completed}
                                onApply={handleApplyFilter}
                                onReset={handleResetFilter}
                                selected={{
                                    subjectAreaId: filter.subjectAreaId,
                                    subjectType: filter.subjectType,
                                    sorting: filter.sorting,
                                    courseType: filter.courseType,
                                }}
                                onSubjectSortChange={(value) => setFilter({ ...filter, sorting: value })}
                                onSubjectAreaChange={(value) => setFilter({ ...filter, subjectAreaId: value })}
                                onSubjectTypeChange={(value) => setFilter({ ...filter, subjectType: value })}
                                onCourseTypeChange={(value) => setFilter({ ...filter, courseType: value })}
                                subjectAreas={
                                    mappedSubjects &&
                                    Array.from(mappedSubjects).map(([, value]) => ({
                                        id: value.id,
                                        name: value.subjectArea,
                                    }))
                                }
                            />
                            <div className='position-relative'>
                                <span
                                    className={`results-container__search-icon ${
                                        selected === HeaderSelection.SEARCH ? 'selected' : ''
                                    }`}
                                    onClick={handleSearchClick}
                                >
                                    <SearchIcon />
                                </span>
                                {selected === HeaderSelection.SEARCH && (
                                    <SearchMenu
                                        style={{ left: '-340px', top: '50px' }}
                                        className='position-absolute'
                                        value={search}
                                        onChange={handleSearchFilter}
                                    />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className='results-container__list-container'>
                {isLoading && <div>Loading...</div>}
                {subjects &&
                    Array.from(subjects).map(([key, value]) => (
                        <SubjectGroupComponent key={key} title={value.subjectArea} subjects={value.subjects} />
                    ))}
            </div>
            <div className='results-container__footer row'>
                <div className='results-container__footer__content row'>
                    <span
                        onClick={() => navigate(SUBJECTS_SELECTION)}
                        className='results-container__footer__back-link col'
                    >
                        ← Restart Questionnaire
                    </span>
                    <div className='results-container__footer__button-row col row row-cols-md-2 g-2'>
                        {/* TODO: add logic for view results button when there are any */}
                        {false && (
                            <Button
                                className='mb-6 results-container__footer__button col'
                                onClick={() => {}}
                                title='View Results'
                                type={ButtonType.PRIMARY}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

interface SubjectGroupComponentProps {
    title: string;
    subjects: Array<SubjectPreference>;
}

function SubjectGroupComponent({ title, subjects }: SubjectGroupComponentProps) {
    const taskRef = useRef<any>(null);

    const mutation = useSubmitPreferences();
    const navigate = useNavigate();
    const dispatch = useAppDispatch();
    const { selectedPreferences } = useAppContextStore();
    const selectedPreferencesIds = selectedPreferences.map((subject) => subject.id);

    function handleSelectSubject(subject: SubjectPreference) {
        triggerBackgroundTask();
        dispatch(setSelectedPreferencesState(subject));
    }

    function triggerBackgroundTask() {
        if (taskRef.current) {
            return;
        }

        taskRef.current = setTimeout(() => {
            mutation.mutate(SubjectPreferenceStatus.IN_PROGRESS);
            taskRef.current = null;
        }, 10000);
    }

    const matchOrder: { [key in 'Strong' | 'Good' | '']: number } = { 'Strong': 1, 'Good': 2, '': 3 };
    const sortedSubjects = [...subjects].sort((a, b) => {
        const aMatch = a.match ?? '';
        const bMatch = b.match ?? '';

        if (aMatch === null || bMatch === null) {
            return a.name.localeCompare(b.name);
        }

        if (matchOrder[aMatch as keyof typeof matchOrder] !== matchOrder[bMatch as keyof typeof matchOrder]) {
            return matchOrder[aMatch as keyof typeof matchOrder] - matchOrder[bMatch as keyof typeof matchOrder];
        }

        return a.name.localeCompare(b.name);
    });

    return (
        <div className='subject-group-container px-15'>
            <span className='subject-group-container__title'>{title}</span>
            <span className='subject-group-container__subtitle'>
                Click on the ‘+’ to add this subject to your selections
            </span>
            <div className='subject-group-container__single-subject-container'>
                <div className='row pb-3'>
                    {sortedSubjects.map((subject) => (
                        <div key={subject.id} className='col-xl-4 col-md-6 col-sm-12'>
                            <SubjectCard
                                imageUrl={subject.featured_image_url}
                                title={subject.name}
                                type={subject.type}
                                course_type={subject.course_type}
                                match={subject.match}
                                units={subject.units}
                                selected={selectedPreferencesIds.includes(subject.id)}
                                onDetailsClick={() =>
                                    subject?.id &&
                                    navigate(generatePath(SUBJECT_DETAILS, { id: subject?.id.toString() }))
                                }
                                hasAddButton
                                onClick={() => handleSelectSubject(subject)}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

interface FilterComponentProps {
    isHidden: boolean;
    onApply?: () => void;
    onReset?: () => void;
    selected: { subjectAreaId: string; subjectType: string; sorting: SORTING; courseType: string; };
    subjectAreas?: Array<{ id: number; name: string }>;
    onSubjectAreaChange?: (subjectAreaId: string) => void;
    onSubjectSortChange?: (sort: SORTING) => void;
    onSubjectTypeChange?: (subjectType: string) => void;
    onCourseTypeChange?: (courseType: string) => void;
    isSorting?: boolean;
}

function FilterComponent({
    isHidden,
    onApply,
    onReset,
    subjectAreas,
    onSubjectSortChange,
    onSubjectAreaChange,
    onSubjectTypeChange,
    onCourseTypeChange,
    selected,
    isSorting = false,
}: FilterComponentProps) {

    const getAllCourseType = async (): Promise<CourseType[]> => {
        try {
            const response = await axios.get<CourseType[]>('/api/v1/getAllCourseType');
            return response.data;
        } catch (error) {
            console.error('Error fetching course types', error);
            return [];
        }
    };

    const getAllSubjectType = async (): Promise<CourseType[]> => {
        try {
            const response = await axios.get<SubjectType[]>('/api/v1/getAllSubjectType');
            return response.data;
        } catch (error) {
            console.error('Error fetching course types', error);
            return [];
        }
    };


    const [courseTypes, setCourseTypes] = useState<CourseType[]>([]);

    const [subjectTypes, setSubjectTypes] =  useState<SubjectType[]>([]);

    useEffect(() => {
        const fetchCourseTypes = async () => {
            const types = await getAllCourseType();
            setCourseTypes(types);
        };

        const fetchSubjectTypes = async () => {
            const types = await getAllSubjectType();
            setSubjectTypes(types);
        };

        fetchSubjectTypes();
        fetchCourseTypes();
    }, []);

    return (
        <div
            className={`filter-component menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-rounded menu-gray-600 menu-state-bg-light-primary ${
                isHidden ? 'hidden' : ''
            }`}
        >
            <span className='filter-component__title fs-4 fw-normal'>Filter Options</span>
            {isSorting && (
                <>
                    <span className='filter-component__filter-title'>Match</span>
                    <select
                        value={selected.sorting}
                        className='filter-component__dropdown fs-6 fw-bold'
                        onChange={(event) => onSubjectSortChange?.(event.currentTarget.value as SORTING)}
                    >
                        <option value={'DESC'}>Most suited - Least suited</option>
                        <option value={'ASC'}>Least suited - Most suited</option>
                    </select>
                </>
            )}
            <span className='filter-component__filter-title'>Subject Area</span>
            <select
                value={selected.subjectAreaId}
                className='filter-component__dropdown fs-6 fw-bold'
                onChange={(event) => onSubjectAreaChange?.(event.currentTarget.value)}
            >
                {subjectAreas &&
                    [{ id: 'all', name: 'All' }, ...subjectAreas].map((subjectArea) => (
                        <option key={subjectArea.id} value={subjectArea.id}>
                            {subjectArea.name}
                        </option>
                    ))}
            </select>
            <span className='filter-component__filter-title'>Subject Type</span>
            <select
                value={selected.subjectType}
                className='filter-component__dropdown fs-6 fw-bold'
                onChange={(event) => onSubjectTypeChange?.(event.currentTarget.value)}
            >
                 <option value='all'>All</option>
                 {subjectTypes.map((subjectType) => (
                    <option key={subjectType.id} value={subjectType.name}>
                        {subjectType.name}
                    </option>
                ))}
            </select>

            <span className='filter-component__filter-title'>Course Type</span>
            <select
                value={selected.courseType}
                className='filter-component__dropdown fs-6 fw-bold'
                onChange={(event) => onCourseTypeChange?.(event.currentTarget.value)}
            >
               <option value='all'>All</option>
                {courseTypes.map((courseType) => (
                    <option key={courseType.id} value={courseType.name}>
                        {courseType.name}
                    </option>
                ))}
            </select>
            <div className='filter-component__button-row'>
                <Button
                    className='mb-6 fs-7 fw-normal filter-component__button grey btn-sm'
                    onClick={onReset}
                    title='Reset'
                    type={ButtonType.GRAY}
                />
                <Button
                    className='mb-6 fs-7 fw-normal filter-component__button btn-sm'
                    onClick={onApply}
                    title='Apply'
                    type={ButtonType.PRIMARY}
                />
            </div>
        </div>
    );
}
