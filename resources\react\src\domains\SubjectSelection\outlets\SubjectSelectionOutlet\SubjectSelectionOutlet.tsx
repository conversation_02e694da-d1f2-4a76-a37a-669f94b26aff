import { SUBJECTS_SELECTION_INTERESTS } from '@/router/Router.constants';
import { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import { useQuiz } from '../../consumers/useSubjectSelectionQuizApiConsumer';
import { useSubjectSelectionContextStore, useSubjectSelectionDispatch } from '../../SubjectSelection';
import classNames from 'classnames';
import StepNavigation from '../../components/StepNavigation/StepNavigation';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import Spinner from '@/components/base/Spinner/Spinner';
import { useAppContextStore } from '@/context/App.context';
import { setSelectionQuizState } from '../../context/SubjectSelection.actions';

export default function SubjectSelectionOutlet() {
    const { isLoading } = useQuiz();
    const navigate = useNavigate();
    const dispatch = useSubjectSelectionDispatch();
    const { submittedQuizSelection } = useAppContextStore();
    const {
        navigation: { clickBack, clickNext, submit, currentStep },
    } = useSubjectSelectionContextStore();

    useEffect(() => {
        submittedQuizSelection && dispatch(setSelectionQuizState({ ...submittedQuizSelection }));
    }, [submittedQuizSelection]);

    useEffect(() => {
        !isLoading && navigate(SUBJECTS_SELECTION_INTERESTS);
    }, [isLoading]);

    const prevButtonStyling = classNames({
        'button-group__button--hidden': clickBack === undefined,
    });

    const nextButtonStyling = classNames({
        'button-group__button--hidden': clickNext === undefined,
    });

    return (
        <div className='d-flex h-100 subject-selection-outlet'>
            <div className='col-xl-5 col-lg-4 d-flex align-items-center subject-selection-outlet__image'>
                <StepNavigation currentStep={currentStep} />
            </div>
            <div className='col-xl-7 col-lg-8 d-flex flex-column justify-content-between subject-selection-outlet__quiz'>
                {(isLoading && (
                    <div className='h-100 d-flex align-items-center justify-content-center'>
                        <Spinner text='loading' />
                    </div>
                )) || (
                    <div className='overflow-auto'>
                        <Outlet />
                    </div>
                )}
                <div className='d-flex justify-content-between align-items-auto button-group'>
                    <Button
                        className={prevButtonStyling}
                        onClick={clickBack}
                        title='Back'
                        type={ButtonType.OUTLINED_WHITE}
                    />
                    {!submit && (
                        <Button
                            className={nextButtonStyling}
                            onClick={clickNext}
                            title='Next'
                            type={ButtonType.OUTLINED_WHITE}
                        />
                    )}
                    {submit && <Button onClick={submit} title='Submit' type={ButtonType.OUTLINED_WHITE} />}
                </div>
            </div>
        </div>
    );
}
