import { FilterApi } from '@/common/models/Api.model';
import { useSubjectManagementDeps } from '@/domains/SubjectManagement/SubjectManagement';
import {
    SubjectGalleryItemRequest,
    SubjectGalleryItemResponse,
} from '@/domains/SubjectManagement/models/Subjects.service.api.model';
import useToast from '@/hooks/useToast';
import { log } from '@/services/Logger.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export enum SubjectsApiConsumer {
    subjectGallery = 'subjectGallery',
}

export const useGetSubjectGallery = ({ subjectId, filter }: { subjectId: number; filter?: FilterApi }) => {
    const { subjectsService } = useSubjectManagementDeps();

    return useQuery<SubjectGalleryItemResponse[]>({
        staleTime: Infinity,
        cacheTime: Infinity,
        keepPreviousData: true,
        queryKey: [SubjectsApiConsumer.subjectGallery, filter, subjectId],
        queryFn: async () => {
            const response = await subjectsService.getSubjectGallery({ subjectId, filter });
            return response.data.data;
        },
        onError: () => {
            log().error({ message: 'useGetSubjectGallery - fetching of subject gallery failed' });
        },
    });
};

export const useOrderSubjectGallery = () => {
    const { subjectsService } = useSubjectManagementDeps();
    const queryClient = useQueryClient();
    const toast = useToast();

    return useMutation({
        mutationFn: async ({ subjectId, subjectIds }: { subjectId: number; subjectIds: { id: number }[] }) => {
            return subjectsService.orderGalleryItemsInSubject({ subjectId, order: subjectIds });
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
            toast.show({ message: 'Subject gallery order changed' });
        },
        onError: () => {
            toast.show({ message: 'Something went wrong when changing items order' });
            log().error({ message: 'useOrderSubjectGallery - ordering of subject gallery failed' });
        },
    });
};

export const useAddGalleryItem = () => {
    const toast = useToast();
    const { subjectsService } = useSubjectManagementDeps();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ subjectId, data }: { subjectId: number; data: SubjectGalleryItemRequest }) => {
            return subjectsService.addGalleryItemToSubject({ subjectId, data });
        },
        onSuccess: () => {
            toast.show({ message: 'Gallery item added' });
            queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
        },
        onError: () => {
            toast.show({ message: 'Something went wrong when adding gallery item' });
            log().error({ message: 'useAddGalleryItem - adding of gallery item failed' });
        },
    });
};

export const useUpdateGalleryItem = () => {
    const toast = useToast();
    const { subjectsService } = useSubjectManagementDeps();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            subjectId,
            galleryItemId,
            data,
        }: {
            subjectId: number;
            galleryItemId: number;
            data: SubjectGalleryItemRequest;
        }) => {
            return subjectsService.updateGalleryItemInSubject({ subjectId, data, galleryItemId });
        },
        onSuccess: () => {
            toast.show({ message: 'Gallery item added' });
            queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
        },
        onError: () => {
            toast.show({ message: 'Something went wrong when adding gallery item' });
            log().error({ message: 'useAddGalleryItem - adding of gallery item failed' });
        },
    });
};

export const useAddGalleryImage = () => {
    const { subjectsService } = useSubjectManagementDeps();
    const toast = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ subjectId, data }: { subjectId: number; data: SubjectGalleryItemRequest }) => {
            return subjectsService.addGalleryImageToSubject({ subjectId, data });
        },
        onSuccess: () => {
            queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
            toast.show({ message: 'Gallery image added' });
        },
        onError: () => {
            toast.show({ message: 'Something went wrong when adding gallery image' });
            log().error({ message: 'useAddGalleryImage - adding of gallery image item failed' });
        },
    });
};

export const useUpdateGalleryImage = () => {
    const { subjectsService } = useSubjectManagementDeps();
    const toast = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({
            subjectId,
            galleryItemId,
            data,
        }: {
            subjectId: number;
            galleryItemId: number;
            data: SubjectGalleryItemRequest;
        }) => {
            return subjectsService.updateGalleryItemImageInSubject({ subjectId, data, galleryItemId });
        },
        onSuccess: () => {
            queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
            toast.show({ message: 'Gallery image added' });
        },
        onError: () => {
            toast.show({ message: 'Something went wrong when adding gallery image' });
            log().error({ message: 'useAddGalleryImage - adding of gallery image item failed' });
        },
    });
};

export const useRemoveGalleryItems = () => {
    const { subjectsService } = useSubjectManagementDeps();
    const toast = useToast();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ subjectId, galleryItemIds }: { subjectId: number; galleryItemIds: number[] }) => {
            return subjectsService.removeGalleryItemsFromSubject(subjectId, galleryItemIds);
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SubjectsApiConsumer.subjectGallery]);
            toast.show({ message: 'Gallery items removed' });
        },
        onError: () => {
            log().error({ message: 'useRemoveGalleryItems - deleting of gallery items failed' });
        },
    });
};
