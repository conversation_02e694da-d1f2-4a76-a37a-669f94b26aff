<?php

namespace App\Http\Controllers;

use App\ChildInvitee;
use App\ChildParent;
use App\CloudVideo;
use Jenssegers\Agent\Agent;

use App\College;
use App\Country;
use App\Course;
use App\IndividualStudent;
use App\IndustryCategory;
use App\Industryunit;
use App\ThingstoknowTemplate;
use App\ThingstoknowCategory;
use App\ThingstoknowKeyword;
use App\Institute;
use App\Media;
use App\Organisation;
use App\ParentInvitee;
use App\Staff;
use App\School;
use App\SchoolvisitTemplate;
use App\Subject;
use App\SubjectCategory;
use App\University;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Cookie;
use Newsletter;
use PDF;
use Auth;
use function Opis\Closure\serialize;
use App\Step;
use App\Student;
use App\SchoolDetail;
use App\State;
use App\Teacher;
use App\WorkexperienceTemplate;
use App\LearningArea;
use App\GeneralCapability;
use Conner\Tagging\Model\Tag;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

use Illuminate\Http\Request;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Laravel\Forge\Forge;

class ClosuresController extends Controller
{
    protected $ids_to_process = [];
    public function checkloadbalancer(Request $request)
    {
        $verificationToken = config("services.aws.lambda_token");
        if ($request->token != $verificationToken) {
            return ['result' => false, 'messasge' => "Invalid token"];
        }
        $instances = $request->instances;
        $action = $request->action;
        if (!$instances || !in_array($action, ["start", "stop"])) {
            return ['result' => false, 'messasge' => "Invalid action or instance ids"];
        }
        $lbid = config("services.forge.loadbalancer_server_id");
        $lbsiteid = config("services.forge.loadbalancer_site_id");
        $forge = new Forge(config("services.forge.token"));

        $response = Http::withToken(config("services.forge.token"))->get("https://forge.laravel.com/api/v1/servers/{$lbid}/sites/{$lbsiteid}/balancing");
        if ($response->status() != 200) {
            return ['result' => false, 'messasge' => "Invalid api keys"];
        }
        $down = ($action == 'start') ? 0 : 1;
        $nodes = collect($response->json()['nodes']);
        $servers = collect($forge->servers());
        $loadbalancer = $servers->where("type", "loadbalancer")->first();
        $lbsite = $forge->site($loadbalancer->id, $lbsiteid);
        $instanceids = $newnodes = [];
        foreach ($instances as $key => $instanceid) {
            $server = $servers->where("attributes.provider_id", $instanceid)->first();
            if ($server) {
                $nodes = $nodes->map(function ($item, $key) use ($server, $down) {
                    if ($item['server_id'] == $server->id) {
                        $item['down'] = $down;
                        $this->ids_to_process[] = $server->attributes['provider_id'];
                    }
                    $item['id'] = $item['server_id'];
                    return $item;
                });
            }
        }
        $activeworkers = $nodes->where("down", 0)->count();
        if ($activeworkers) {
            $data = [
                'servers' => $nodes->toArray(),
                'method' => 'least_conn'
            ];
            $response = Http::acceptJson()->withToken(config("services.forge.token"))->put("https://forge.laravel.com/api/v1/servers/{$lbid}/sites/{$lbsiteid}/balancing", $data);

            if ($response->status() == 200) {
                return ['result' => true, 'instances_to_process' => $this->ids_to_process, "ids" => ""];
            } else {
                return ['result' => false, 'instances_to_process' => [], "ids" => ""];
            }
        }

        return ['result' => false, 'message' => "empty response"];
    }
    public function command()
    {
        // \Artisan::call('view:clear');
        // \Artisan::call('optimize');
        if (strpos(request('command'), "clear") || strpos(request('command'), "cache") || strpos(request('command'), "optimize")) {
            \Artisan::call(request('command'));
            return "Command " . request('command') . " run successfully";
        }
        abort(403, 'Command not allowed!');
    }

    public function removeCache()
    {
        if (request('cache')) {
            Cache::forget(request('cache'));
        }
        if (request('tags')) {
            Cache::tags(request('tags'))->flush();
        }

        return "Cache removed successfully!";
    }

    public function stripecheckoutsession(Request $request)
    {
        $licensecount = $request->licensecount;
        $parentdata = $request->formdata;

        $session = \Stripe\Checkout\Session::create([
            'payment_method_types' => ['card'],
            'subscription_data' => [
                // 'trial_period_days' => 30,
            ],
            'line_items' => [[
                'price' => config('services.stripe.child_key'),
                'quantity' => $licensecount,
            ]],
            'mode' => 'subscription',
            'allow_promotion_codes' => true,
            'success_url' => route('stripe.childsuccess'),
            'cancel_url' => route('stripe.childfailed'),
        ]);
        $request->session()->put('cart.parentlicense', ['parentlicense' => $parentdata, 'session' => $session]);
        return $session;
    }
    public function stripechildsuccess(Request $request)
    {
        $cart = $request->session()->get('cart.parentlicense');
        // $request->session()->forget('cart.parentlicense');
        $sessionid = $cart['session']->id;
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

        $sessiondetail = $stripe->checkout->sessions->retrieve($sessionid, []);
        dump($sessiondetail);
        if ($sessiondetail) {
            if ($sessiondetail->payment_status == "paid") {
                /*Payment successful*/
                echo $subscriptionid = $sessiondetail->subscription;
                /*Store user and complete the registration process . Registration form  data will be stored in $cart['parentlicense']  */
            }
        } else {
            /*Fraud . Show exception*/
        }
    }
    public function stripechildfailed(Request $request)
    {

        /*Show failed reason and discuss how we will handle failed payments */
    }



    public function refreshInvitations()
    {
        $userId = User::pluck('id');

        \App\ChildInvitee::whereNotIn('child_id', $userId)->delete();

        dd('success');
    }

    public function sampleEmail()
    {
        try {
            $mail = \Mail::send('emails.sample-mail', [], function ($message) {
                // $message->from(config('mail.from.address'));
                $message->to('<EMAIL>')->subject('Account created successfully.');
            });
            dd($mail);
            return 'success';
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    public function sendSampleMail($email)
    {
        try {
            $sendTo = config('mail.contact.to');
            // if (config('app.env') == 'local') {
            //     $sendTo = '<EMAIL>';
            // }
            \Mail::send('emails.' . $email, [], function ($message) use ($sendTo) {

                $message->to($sendTo)->subject('Sample email');
            });
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'Email sent successfully to ' . $sendTo);
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    public function refreshTemplates()
    {
        $units = Industryunit::select('id', 'type')->get();
        IndustryCategory::select('id')->chunk(11, function ($industries) use ($units) {
            foreach ($industries as $industry) {
                foreach ($units as $unit) {
                    $exists = Industryunit::published()->where([
                        'type' => $unit->type,
                    ])->whereHas('industries', function ($q) use ($industry) {
                        $q->where('industry_id', $industry->id);
                    })->exists();

                    if ($exists) {
                        DB::table('industry_category_template')->where([
                            'industry_category_id' => $industry->id,
                            'template_id' => $unit->template->id,
                        ])->update(['has_units' => '1']);
                        Cache::forget('allIndustries');
                    } else {
                        DB::table('industry_category_template')->where([
                            'industry_category_id' => $industry->id,
                            'template_id' => $unit->template->id,
                        ])->update(['has_units' => '0']);
                        Cache::forget('allIndustries');
                    }
                    $unit = '';
                    $exists = '';
                }
                $industry = '';
            }
        });
        return 'Success';
    }

    public function school()
    {
        $wixurl = config('services.wix.url') . "/school";
        return redirect($wixurl, 301);
        $schools = SchoolDetail::where('school_id', '<>', 127)->whereShowinfrontend(true)->whereOrderConfirmed('1')->orderBy('name')->pluck('name');
        $chunk = ceil($schools->count() / 2);
        $agent = new Agent();
        if ($agent->isDesktop()) {
            $chunk = ceil($schools->count() / 3);
        }
        $schools = $schools->chunk($chunk);
        return view('frontend.school', compact('schools'));
    }

    public function parent()
    {
        $wixurl = config('services.wix.url') . "/parent";
        return redirect($wixurl, 301);
        $states = Country::whereName('Australia')->first()->states()->pluck('name', 'code');
        return view('frontend.parent', compact('states'));
    }

    public function complaint()
    {
        $states = State::pluck('name');
        return view('frontend.complaint', compact('states'));
    }


    public function api_institutes($value = '')
    {
        $first = Institute::select('id', 'name', DB::raw("'TAFE' as type"))->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->where('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name');
        $second = University::select('id', 'name', DB::raw("'University' as type"))->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->where('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name');
        return College::select('id', 'name', DB::raw("'College' as type"))->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->where('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name')->union($first)->union($second)->get();
    }

    


    public function api_college()
    {
        return College::where('name', 'LIKE', '%' . request('q') . '%')->orderBy('name')->paginate(10);
    }

    public function api_university()
    {
        return University::where('name', 'LIKE', '%' . request('q') . '%')->orderBy('name')->paginate(10);
    }

    // public function getTags()
    // {
    //     return DB::table('tagging_tags')
    //         ->where(function ($query) {
    //             foreach (explode(' ', request('q')) as $term) {
    //                 $query->orWhere('name', 'like', '%' . $term . '%');
    //             }
    //         })
    //         ->orderBy('name')->pluck('name');
    // }

    public function getUnitKeywords()
    {
        return Tag::inGroup('IndustryUnitKeywords')->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->orWhere('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name')->distinct()->pluck('name');

        // return DB::table('tagging_tags')
        //     ->select('tagging_tags.*')
        //     ->join('tagging_tagged', 'tagging_tagged.tag_slug', '=', 'slug')
        //     ->where('tagging_tagged.taggable_type', 'App\Industryunit')
        //     ->where(function ($query) {
        //         foreach (explode(' ', request('q')) as $term) {
        //             $query->orWhere('name', 'like', '%' . $term . '%');
        //         }
        //     })
        //     ->orderBy('name')->distinct()->pluck('name');
    }

    public function getSkillKeywords()
    {
        return Tag::inGroup('Skills')->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->orWhere('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name')->distinct()->pluck('name');

        // return DB::table('tagging_tags')
        //     ->select('tagging_tags.*')
        //     ->join('tagging_tagged', 'tagging_tagged.tag_slug', '=', 'slug')
        //     ->where('tagging_tagged.taggable_type', 'App\Workexperiencetemplate')
        //     ->where(function ($query) {
        //         foreach (explode(' ', request('q')) as $term) {
        //             $query->orWhere('name', 'like', '%' . $term . '%');
        //         }
        //     })
        //     ->orderBy('name')->distinct()->pluck('name');
    }

    public function getLearningAreas()
    {
        $learning_area = LearningArea::where('title', 'like', '%' . request('q') . '%')->pluck('title');
        return $learning_area;
    }

    public function getGeneralCapabilities()
    {
        $general_capability = GeneralCapability::where('title', 'like', '%' . request('q') . '%')->pluck('title');
        return $general_capability;
    }

    public function getGroupKeywords()
    {
        return Tag::inGroup('GroupKeywords')->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->orWhere('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name')->distinct()->pluck('name');
    }


    public function setKeywordsGroup()
    {
        $wet = WorkexperienceTemplate::existingTags()->chunk(100, function ($tags) {
            foreach ($tags as $tag) {
                Tag::whereSlug($tag->slug)->first()->setGroup('Skills');
            }
        });
        $ids = Industryunit::existingTags()->chunk(100, function ($tags) {
            foreach ($tags as $tag) {
                Tag::whereSlug($tag->slug)->first()->setGroup('IndustryUnitKeywords');
            }
        });
        dd('ssss');
    }

    public function searchParentsByName()
    {
        return  \App\ChildParent::select('id', 'name', 'email')->where('name', 'like', request('term') . '%')->orderBy('name')->get();
    }

    public function institutions()
    {
        return \App\Institution::where('title', 'LIKE', '%' . request('q') . '%')->where('type', request('type'))->orderBy('title')->paginate(1000);
    }

    public function coursefindersite()
    {
        return redirect('http://coursefinder.thefootnotes.com.au/');
    }

    public function getCourses()
    {
        $courses = Course::select('id', 'name', 'unique_code', 'institution_id', 'campus_id', 'atar')->with('states:code', 'institution', 'campus')
            ->when(request('name'), function ($query) {
                return $query->where('name', 'like', '%' . request('name') . '%');
            })
            ->when(request('code'), function ($query) {
                return $query->where('unique_code', 'like', '%' . request('code'));
            })
            ->when(request('institution'), function ($query) {
                return $query->where('institution_id', request('institution'));
            })
            ->when(request('states'), function ($query) {
                return $query->whereHas('states', function ($q) {
                    $q->whereIn('code', request('states'));
                });
            })
            // ->where('name', 'like', '%' . request('name') . '%')
            // ->where(function ($query) {
            //     foreach (explode(' ', request('q')) as $term) {
            //         $query->where('title', 'like', '%' . $term . '%');
            //     }
            // })
            ->orderBy('name')->get();

        foreach ($courses as $key => $course) {
            $states = $course->states->pluck('code');
            unset($courses[$key]->states);
            $courses[$key]->states = $states;
        }
        return $courses;
    }

    public function favCourse()
    {
        if (Auth::user()->isTeacher()) {
            $user = Teacher::find(Auth::id());
        } elseif (Auth::user()->isStaff()) {
            $user = Staff::find(Auth::id());
        } elseif (Auth::user()->isParent()) {
            $user = ChildParent::find(Auth::id());
        } else {
            $user = IndividualStudent::find(Auth::id());
            if (!$user) {
                $user = Student::find(Auth::id());
            }
        }

        if (request('isFav')) {
            $user->courses()->syncWithoutDetaching(request('course_id'));
            if (Auth::user()->isParent()) {
                $favCount = $user->courses()->count();
                if (!$user->dashboardChecklist->courses && $favCount >= 5) {
                    $user->saveParentDashboardChecklist(['courses' => true]);
                }
            }
            return 'hearted';
        } else {
            $user->courses()->detach(request('course_id'));
            return 'removed';
        }
    }

    public function parent_buylicense()
    {
        $states = \App\State::All();
        return view('auth.license.parentlicense', compact('states'));
    }

    public function invite_parent()
    {
        if (Auth::user()->isStudent()) {
            return view('children.child.invite-parent');
        } else {
            return abort(404);
        }
    }

    public function api_tafe()
    {
        return Institute::where('name', 'LIKE', '%' . request('q') . '%')->orderBy('name')->paginate(10);
    }

    public function searchUnitKeyword()
    {
        $units = Industryunit::select('id', 'title', 'banner', 'type', 'sortorder')->withAllTags(request('keyword'))->with('template')->orderBy('id', 'desc')->get()->sortBy('order');

        $favContent = Auth::user()->industryunits()->pluck('industryunit_id');

        $returnHTML = view('exploreindustries.filter', compact("units", "favContent"))->render();
        return response()->json(array('success' => true, 'html' => $returnHTML));
    }

    public function searchThingstoknowKeyword()
    {
        $query1 = ThingstoknowTemplate::select('id', 'title', 'bannerimage')->published()->withAllTags(request('keyword'));
        $templates = ThingstoknowTemplate::select('id', 'title', 'bannerimage')->published()->withAllTags(request('keyword'))->union($query1)->orderBy('title')->get();
        dd($templates);
        $returnHTML = view('explorethingstoknow.filter', compact('templates'))->render();
        return response()->json(array('success' => true, 'html' => $returnHTML));
    }

    public function searchTemplate()
    {
        $addedIds = IndustryCategory::find(request('industry_id'))->units()->pluck('industryunit_id');
        $addedTemplates = IndustryCategory::find(request('industry_id'))->templates()->pluck('slug');

        return Industryunit::select('id', 'title', 'type')->with(['template:slug,name'])->whereIn('type', $addedTemplates)->whereNotIn('id', $addedIds)->where('title', 'like', '%' . request('term') . '%')->orderBy('title')->get();
    }

    public function searchThingstoknowTemplate()
    {

        $addedIds = ThingstoknowCategory::find(request('category_id'))->templates()->pluck('thingstoknow_template_id');
        $addedTemplates = ThingstoknowCategory::find(request('category_id'))->templates()->distinct()->pluck('type');
        return ThingstoknowTemplate::select('id', 'title', 'type')->with(['template:slug,name'])->whereIn('type', $addedTemplates)->whereNotIn('id', $addedIds)->where('title', 'like', '%' . request('term') . '%')->orderBy('title')->get();
    }
    public function checkUserName()
    {
        if (request('checklicense')) {
            $user = User::where('email', request('email'))->first();
            if ($user) {
                if (!@$user->profile->accountcreated && $user->childInvitations()->exists()) {
                    return "true";
                }
                return "false";
            }
        }

        if (User::where('email', request('email'))->exists()) {
            return "false";
        }
        return "true";
    }

    public function checkParentEmail()
    {
        $email = User::where('email', request('email'))->where('role_id', '<>', 5)->exists();
        if ($email) {
            return "false";
        } else {
            return 'true';
        }
    }

    public function checkSchoolSlug()
    {

        if (request('id')) {
            if (SchoolvisitTemplate::where('slug', request('slug'))->where('id', '<>', request('id'))->exists()) {
                echo "false";
            } else {
                echo "true";
            }
        } else {
            if (SchoolvisitTemplate::where('slug', request('slug'))->exists()) {
                echo "false";
            } else {
                echo "true";
            }
        }
        exit;
    }

    public function checkEmail()
    {
        $cond1 = User::where('email', request('email'))->whereNotIn('role_id', [3, 4])->exists();
        if ($cond1) {
            echo "false";
        } elseif (request('parentId') && request('childId')) {
            $childIds = \App\ChildParent::find(request('parentId'))->childInvitees()->where('child_id', '<>', request('childId'))->pluck('child_id');
            $cond2 = User::whereIn('id', $childIds)->where('email', request('email'))->exists();
            if ($cond2) {
                echo json_encode("Email already entered for another child!");
            } else {
                echo 'true';
            }
        } else {
            echo 'true';
        }
        exit;
    }

    public function checkStudentExist()
    {
        $result = $license = "false";
        $user = User::where('email', request('email'))->whereIn('role_id', [3, 4])->first();
        if ($user) {
            $result = "true";
            $license = $user->activeLicense();
        }
        return response()->json(['result' => $result, 'license' => $license]);
    }

    public function checkParentExist()
    {
        $ids = Auth::user()->parents()->pluck('parent_id');
        $exist = User::where('email', request('email'))->where('role_id', 5)->whereNotIn('id', $ids)->exists();
        if ($exist) {
            return "true";
        }
        return "false";
    }

    public function checkFollowerEmail()
    {
        if (\App\Follower::where('email', request('email'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkMarkerEmail()
    {
        if (User::where('email', request('email'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkEmailOnUpdate()
    {
        if (User::where('email', request('email'))->where('id', '<>', request('id'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkSchoolName()
    {
        if (School::where('name', request('school_name'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkSchoolNameOnUpdate()
    {
        if (School::where('id', '<>', request('id'))->where('name', request('school_name'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkOrganisationName()
    {
        if (Organisation::where('name', request('school_name'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function checkOrganisationNameOnUpdate()
    {
        if (Organisation::where('id', '<>', request('id'))->where('name', request('school_name'))->exists()) {
            echo "false";
        } else {
            echo "true";
        }
        exit;
    }

    public function getSubjects()
    {
        $stateId = Auth::user()->state_id;
        $subjects = Subject::select('id', 'title')->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->where('title', 'like', '%' . $term . '%');
            }
        })
            ->when(!session('studentView'), function ($query) use ($stateId) {
                $query->whereHas('states', function ($q) use ($stateId) {
                    $q->where('state_id', $stateId);
                });
            })
            ->orderBy('title')->get();
        return ($subjects);
    }

    public function getKeywords()
    {
        $stateId = Auth::user()->state_id;
        $keywords = ThingstoknowKeyword::select('id', 'name')->where(function ($query) {
            foreach (explode(' ', request('q')) as $term) {
                $query->where('name', 'like', '%' . $term . '%');
            }
        })->orderBy('name')->get();
        dd($keywords);
        return ($keywords);
    }


    public function getStates()
    {
        if (request('id')) {
            $states = Country::find(request('id'))->states()->get();
            return $states;
        }
        return;
    }

    public function getSchoolCampuses()
    {
        if (request('id')) {
            $campuses = School::find(request('id'))->campuses()->pluck('name', 'id')->toArray();
            return json_encode($campuses);
        }
        return;
    }

    public function getOrganisationCampuses()
    {
        if (request('id')) {
            $campuses = Organisation::find(request('id'))->campuses()->pluck('name', 'id')->toArray();
            return json_encode($campuses);
        }
        return;
    }


    public function api_events($value = '')
    {
        return [
            [
                "title" => "Call Dave",
                "class" => "bg-success-lighter",
                "start" => "2014-10-07T06:00:00",
                "end" => "2014-10-07T08:00:24",
                "other" => [],
            ],
            [
                "title" => "Meeting Roundup",
                "class" => "bg-success-lighter",
                "start" => "2014-11-07T06:00:00",
            ],
            [
                "title" => "Double click Any where",
                "class" => "bg-complete-lighter",
                "start" => "2014-11-07T01:00:00",
                "end" => "2014-11-07T02:00:00",
                "other" => [
                    "note" => "test",
                ],
            ],
        ];
    }

    public function planPopup()
    {
        Cookie::queue('planPopup', true);
    }

    public function sampleInvoice()
    {
        $invoice = \App\Invoice::with('lineItems')->first();
        view()->share(['invoice' => $invoice]);
        $pdf = PDF::loadView('auth.license.invoicepdf');
        return  $pdf->stream();
    }


    public function mailchimpSyncUsers()
    {
        User::select('id', 'role_id')->whereNotIn('role_id', [1, 6])->where('school_id', '<>', 127)->chunk(30, function ($users) {
            foreach ($users as $user) {
                User::updateUserMailchimpDetail($user->id);
            }
        });
        return 'success';
    }

    public function mailchimpSyncIndustries()
    {

        $industries = IndustryCategory::All();

        foreach ($industries as $industry) {

            $stuInterest =  Newsletter::getApi()->post(
                '/lists/' . config('newsletter.lists.students.id') . '/interest-categories/' . config('newsletter.lists.students.interests.industries') . '/interests',
                [
                    'name' => $industry->name,
                    'display_order' => 0,
                ]
            );

            $parInterset =  Newsletter::getApi()->post(
                '/lists/' . config('newsletter.lists.parents.id') . '/interest-categories/' . config('newsletter.lists.parents.interests.industries') . '/interests',
                [
                    'name' => $industry->name,
                    'display_order' => 0,
                ]
            );

            $industry->mailchimp_ids = serialize([
                'student' => $stuInterest['id'],
                'parent' => $parInterset['id'],
            ]);

            $industry->save();
        }

        dd('success');
    }

    public function mailchimpSyncSubjects()
    {

        $subjects = SubjectCategory::all();

        foreach ($subjects as $subject) {
            $interest =  Newsletter::getApi()->post(
                '/lists/' . config('newsletter.lists.students.id') . '/interest-categories/' . config('newsletter.lists.students.interests.subjects') . '/interests',
                [
                    'name' => $subject->title,
                    'display_order' => 0,
                ]
            );

            dump($interest);

            $subject->mailchimp_id = $interest['id'];
            $subject->save();
        }

        dd('success');
    }

    public function subscribeToNewsletter($id)
    {
        $student = Student::find($id);

        if ($student->profile->gender == 'F') {
            $gender = 'Female';
        } elseif ($student->profile->gender == 'M') {
            $gender = 'Male';
        } else {
            $gender = 'Other';
        }

        Newsletter::subscribeOrUpdate(
            $student->email,
            [
                'FNAME' => $student->profile->firstname,
                'LNAME' => $student->profile->lastname,
                'COUNTRY' => $student->state->country->name,
                'STATE' => $student->state->code,
                'POSTCODE' => $student->postcode,
                'GENDER' => $gender,
                'SCHOOL' => $student->school->name,
            ],
            'students',
            [
                'industries' => [
                    'interestId' => true,
                    'interestId' => true
                ]
            ]
        );
    }

    public function vweUpdate()
    {
        $templates = WorkexperienceTemplate::all();

        foreach ($templates as $template) {
            $step = new Step();
            $step->number = 1;
            $step->body = $template->body;
            $template->steps()->save($step);
        }

        dd('success!');
    }

    public function editHelpcrunchDetail()
    {
        User::whereHas('profile', function ($q) {
            $q->whereAccountcreated(true);
        })->where('id', '>', 975)->where('role_id', '<>', 6)->chunk(30, function ($users) {
            foreach ($users as $user) {
                User::addHelpcrunchAccount($user->id);
            }
        });

        return 'success';
    }

    public function childInviteesReset()
    {
        $parents = ChildParent::with('children', 'childInvitees')->get();
        ChildInvitee::where(function ($q) {
            return $q->whereNotnull('parent_id')->doesntHave('parent');
        })->orDoesntHave('child')->delete();
        foreach ($parents as $parent) {
            if ($parent->children()->exists()) {
                $invitations_id = [];
                foreach ($parent->children as $child) {
                    $invitee = $parent->childInvitees()->whereChildId($child->id);
                    if ($invitee->exists()) {
                        $invitee->update(['processed' => $child->profile->accountcreated ? '1' : '0']);
                        $invitations_id[] = $invitee->value('id');
                    } else {
                        $invitee = ChildInvitee::create([
                            'parent_id' => $parent->id,
                            'child_id' => $child->id,
                            'token' => uniqid(Str::random(27)),
                            'processed' => $child->profile->accountcreated ? '1' : '0',
                        ]);
                        $invitations_id[] = $invitee->id;
                    }
                }
                ChildInvitee::whereParentId($parent->id)->whereNotIn('id', $invitations_id)->whereProcessed('1')->update(['processed' => '0']);
            } else {
                ChildInvitee::whereParentId($parent->id)->whereProcessed('1')->update(['processed' => '0']);
            }
        }
        return 'success';
    }


    public function uploadVideo(Request $request)
    {
        try {
            $uploadpath = Storage::cloud()->put('videos', $request->file('file'), 'public');

            if ($uploadpath) {

                if ($request->user && $request->user == 'Admin') {
                    $cloudvideo = new CloudVideo();
                    $cloudvideo->name = $request->file->getClientOriginalName();
                    $cloudvideo->source = $uploadpath;
                    $cloudvideo->filetype = $request->file->getClientMimeType();
                    $cloudvideo->save();
                }
                $link = Storage::cloud()->url($uploadpath);

                return response()->json(["link" => $link], 200);
            }
            throw new \Exception("Error Uploading file", 1);
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    public function uploadFile(Request $request)
    {
        try {
            $uploadpath = Storage::cloud()->put('media', $request->file('file'), 'public');

            if ($uploadpath) {
                if ($request->user && $request->user == 'Admin') {
                    $media = new Media;
                    $media->name = $request->file->getClientOriginalName();
                    $media->path = $uploadpath;
                    $media->filetype = $request->file->getClientMimeType();
                    $media->save();
                }
                $link = Storage::cloud()->url($uploadpath);

                return response()->json(["link" => $link], 200);
            }
            throw new \Exception("File not uploaded", 1);
        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }


    public function flushCache()
    {
        try {
            $schools = School::all();
            if ($schools) {
                foreach ($schools as $school) {
                    Cache::forget("standards" . $school->id);
                }
            }
            Cache::tags(['profile'])->flush();
        } catch (\Exception $ex) {
        }
    }
}
