import { useEffect, useState } from 'react';
import classNames from 'classnames';
import { ButtonType } from '@/utils/enums';
import useModal from '@/hooks/useModal/useModal';
import Button from '@/components/base/Button/Button';
import Share from '@/assets/icons/subjectSelection/Share';
import { useShareResultsViaEmail } from '@/domains/SubjectManagement/consumers/useSubjectManagementApiConsumer';

interface Props {
    url?: string;
    onClose?: () => void;
    onShare?: () => void;
    studentName?: string;
    onViewSubjects?: () => void;
}

export default function SubjectResultsPdfView({ url, onClose, onShare, studentName, onViewSubjects }: Props) {
    const { isOpen, Modal, toggle } = useModal({
        title: 'Enter the email addresses you would like to share these results with',
    });

    const pdfUrl = `${url}#view=FitH&toolbar=0`;
    const pdfDownloadFileName = `${new Date().getFullYear()}_${trimAndFormatString(
        studentName,
    )}_SUBJECT_SELECTION_RESULTS.pdf`;

    return (
        <div className='results-pdf'>
            <div
                onClick={onClose}
                className='results-pdf__actions mb-20 mt-3 d-flex justify-content-end position-relative cursor-pointer'
            >
                <span>close</span>
                <span className='close mx-4'></span>
            </div>
            <div className='results-pdf__container'>
                <div className='details mb-20 d-flex justify-content-between'>
                    <div className='results-pdf__details'>
                        <h3>{studentName}</h3>
                        <h1 className='results-pdf__title'>Results</h1>
                        <div className='share mt-5 d-flex align-items-center cursor-pointer' onClick={toggle}>
                            <span className='me-3'>Share results</span>
                            <Share />
                        </div>
                    </div>
                    <div className='results-pdf__button-group'>
                        <Button
                            className='mb-5'
                            type={ButtonType.SECONDARY}
                            title='Download Results Report'
                            onClick={() => url && studentName && download(url, pdfDownloadFileName)}
                        />
                        <Button type={ButtonType.OUTLINED_WHITE} title='View Subjects' onClick={onViewSubjects} />
                    </div>
                </div>
                <iframe
                    id='#pdf'
                    src={pdfUrl}
                    style={{ width: '100%', height: 'calc(100vh - 350px)', border: 'none' }}
                ></iframe>
            </div>
            {isOpen && (
                <Modal>
                    <EmailForm onSubmit={onShare} />
                </Modal>
            )}
        </div>
    );
}

interface EmailFormProps {
    onSubmit?: () => void;
}

function EmailForm({ onSubmit }: EmailFormProps) {
    const mutation = useShareResultsViaEmail();

    const [email, setEmail] = useState('');
    const [error, setError] = useState('');

    useEffect(() => {
        if (mutation.isSuccess) {
            onSubmit && onSubmit();
        }
    }, [onSubmit, mutation.isSuccess]);

    const handleEmailChange = (e: any) => {
        setEmail(e.target.value);
    };

    const validateEmail = (email: string) => {
        // Use a regular expression to validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleSubmit = async (e: any) => {
        e.preventDefault();
        let isValid = true;

        if (email === '') {
            isValid = false;
            setError('Please enter emails');
            return;
        }

        const emails = email.split(', ');

        for (const email of emails) {
            const isValidEmail = validateEmail(email);

            if (!isValidEmail) {
                isValid = false;
                setError(`Email '${email}' is invalid`);
                break;
            }
        }

        if (!isValid) return;
        setError('');
        await mutation.mutate(emails);
    };

    const inputClassName = classNames('email-input', {
        'email-input--error': error !== '',
    });

    return (
        <>
            <p>Emails have to be valid and are separated with ", "</p>
            <form className='email-modal__form' onSubmit={handleSubmit}>
                <div className='email-modal__form-group'>
                    <input type='text' className={inputClassName} value={email} onChange={handleEmailChange} />
                    {error && <span className='error'>{error}</span>}
                </div>
                <Button
                    type={ButtonType.OUTLINED_BLACK}
                    title={mutation.isLoading ? 'Sending...' : 'Send'}
                    spanClassName='fs-6'
                    spinner={mutation.isLoading}
                />
            </form>
        </>
    );
}

function download(url: string, filename: string) {
    fetch(url).then(function (t) {
        return t.blob().then((b) => {
            var a = document.createElement('a');
            a.href = URL.createObjectURL(b);
            a.setAttribute('download', filename);
            a.click();
        });
    });
}

function trimAndFormatString(input: string | undefined) {
    if (!input) return '';

    // Trim leading and trailing whitespace
    const trimmed = input.trim();

    // Convert all letters to uppercase
    const uppercase = trimmed.toUpperCase();

    // Replace whitespace with an underscore
    const formatted = uppercase.replace(/\s/g, '_');

    return formatted;
}
