<?php

namespace App\Http\Resources;

use App\IntercomUser;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Profile;
use App\User;
use App\Plan;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class UserResource extends JsonResource
{
    protected $profile;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (!$this->profile) {
            $this->profile = Profile::where('user_id', $this->id)->first();
        }

        $role = $this->role()->pluck('name')->first();
        $lasturl = session()->get('url.intended');
        if ($lasturl) {
            // Session::forget('url.intended');
        }

        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => Str::lower($role),
            'avatar_path' => $this->avatar_path,
            'address' => $this->location()->address,
            'isAdmin' => $this->isAdmin(),
            'isTeacher' => $this->isTeacher(),
            'hasAccess' => $this->hasAccess(),
            'hasFullAccess' =>  $this->hasFullAccess(),
            'isPrimaryTeacher' => (($this->hasPrimarySchoolAccess() && !$this->hasSecondarySchoolAccess()) || $this->isCompassTeacher()),
            'isSecondaryTeacher' => $this->hasSecondarySchoolAccess(),
            'isParent' => $this->isParent(),
            'isStudent' => $this->isStudent(),
            'isStaff' => $this->isStaff(),
            'schoolSection' => session()->get('schoolSection'),
            'schoolId' => isset($this->school_id) ? $this->school_id : null,
            'studentView' => (session()->get('studentView') == true) ? true : false,
            'teacherView' => (session()->get('teacherView') == true) ? true : false,
            'hasLimitedAccess' => !$this->hasPremiumAccess($this),
            'hasPremiumAccess' => $this->hasPremiumAccess($this),
            'hasPlan' => $this->hasPlan($this),
            'hasCampus' => $this->hasCampus($this),
            'hasParentActiveSubscription' => $this->hasParentActiveSubscription($this),
            'hasSubjectsSelectionAccess' => $this->hasSubjectsSelectionAccess($this),
            'children' => $this->children($this),
            'sessionChild' => $this->sessionChild(),
            'sessionChildIndustries' => $this->sessionChildIndustries(),
            'sessionChildLatestIndustry' => $this->sessionChildLatestIndustry(),
            'planurl' => '/#/gameplan',
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'teacherInsights' => $this->teacherInsights(),
            'lastActiveStudents' => $this->lastActiveStudents(),
            'intended' => $lasturl,
            'hasIndustriesAccess' => $this->hasIndustriesAccess(),
            'hasEMagazineAccess' => $this->hasEMagazineAccess(),
            'hasCareerProfilingAccess' => $this->hasCareerProfilingAccess(),
            'hasVideoProfilingAccess' => $this->hasVideoProfilingAccess(),
            'hasMyPathAccess' => $this->hasMyPathAccess(),
            'hasGamePlanAccess' => $this->hasGamePlanAccess(),
            'hasNewGamePlan' => $this->hasNewGamePlan($this),
            'hasProfilingAccess' => $this->hasProfilingAccess(),
            'hasLessonsAccess' => $this->hasLessonsAccess(),
            'hasVweAccess' => $this->hasVweAccess(),
            'hasSkillsTrainingAccess' => $this->hasSkillsTrainingAccess(),
            'hasWhsAccess' => $this->hasWhsAccess(),
            'hasJobFinderAccess' => $this->hasJobFinderAccess(),
            'hasScholarshipFinderAccess' => $this->hasScholarshipFinderAccess(),
            'hasResumeBuilderAccess' => $this->hasResumeBuilderAccess(),
            'hasCourseFinderAccess' => $this->hasCourseFinderAccess(),
            'hasEPortfolioAccess' => $this->hasEPortfolioAccess(),
            'hasSubjectSelectionsAccess' => $this->hasSubjectSelectionsAccess(),
            'hasNoticeboardAccess' => $this->hasNoticeboardAccess(),
            'hasHelpCentreAccess' => $this->hasHelpCentreAccess(),
            'intercom' => $this->intercomData()
        ];
    }

    public function intercomData()
    {
        if(!$this->intercom) {
            return IntercomUser::where('user_id', $this->id)->first();
        }

        return $this->intercom;
    }

    protected function hasPremiumAccess($user)
    {
        // to do optimization
        if ($user && $user->isParent()) {
            if ($user->childInvitees) {
                foreach ($user->childInvitees as $invitees) {
                    if ($invitees->child && $invitees->child->activeLicense()) {
                        $profile = $user->profile;
                        $profile->premium_access = true;
                        $profile->save();
                        return true;
                        break;
                    }
                }
            }
        }
        return false;

        // if($user->profile && $user->profile->premium_access == 1){
        //     return true;
        //   }
        // return false;
    }
    public function hasPlan($user)
    {
        if ($user->isStudent()) {
            if (!$user->lastPlan() && !$user->profile->year_popup) {
                return false;
            }
        }
        return true;
    }
    public function hasCampus($user)
    {
        if ($user->isStudent() || $user->isTeacher() || $user->isStaff()) {
            if (($user->school && $user->school->campuses->isNotEmpty()) || ($user->organisation && $user->organisation->campuses->isNotEmpty())) {
                    return $user->campuses->isNotEmpty();
            }
        }
        return true;
    }


    public function hasParentActiveSubscription($user)
    {
        if ($user->isParent()) {
            if ($user->childInvitees) {
                foreach ($user->childInvitees as $invitee) {
                    if ($invitee->child) {
                        if ($invitee->child->activeLicense()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public function hasNewGamePlan($user){
        if ($user->isStudent() && $user->hasGamePlanAccess()) {
            if (!$user->lastPlan()) {
                return false;
            }
        }
        return true;
    }



    public function children($user)
    {
        if ($user->isParent()) {
            // $childre=ChildParent::find($user->id)->children()->get();
            $children = [];

            foreach ($user->childInvitees as $invitee) {
                $children[] = [
                    'id' => $invitee->child_id,
                    'name' => $invitee->child->name,
                    'firstname' => $invitee->child->profile->firstname,
                    'lastname' => $invitee->child->profile->lastname,
                    'email' => $invitee->child->email,
                    'processed' => ($invitee->processed == '1') ? true : false,
                    'accountcreated' => ($invitee->child->profile->accountcreated == 1) ? true : false
                ];
            }

            return  $children;
            //    return  UserResource::collection(ChildInvitee::where('parent_id', $this->id)->child()->get());
        }
        return false;
    }

    public function sessionChild()
    {
        $user_id = "";
        $session_id = session()->get('child_id');
        if ($session_id) {
            $user_id = $session_id;
        } else {
            $id = $this->childInvitees->where('processed', "1")->min('child_id');
            if ($id) {
                session()->put('child_id', $id);
                $user_id = session()->get('child_id');
            }
        }
        if ($user_id) {
            $user = User::find($user_id);
            $child = [
                'id' => $user->id,
                'name' => $user->name,
                'firstname' => $user->profile->firstname,
                'lastname' => $user->profile->lastname,
                'email' => $user->email,
                'profiler' => $user->has_completed_profiler,
            ];
            return $child;
        }
    }

    public function sessionChildIndustries()
    {
        $user_id = $this->sessionChild();
        $industries = [];
        if ($user_id) {
            $currentPlan = Plan::where('user_id', $user_id)->with(['industries'])
                ->with(array('industries' => function ($query) {
                    $query
                        ->with(array('units' => function ($query) {
                            $query->orderBy('industryunit_id', 'DESC');
                        }))
                        ->latest()->take(5);
                }))
                ->latest()->first();

            if ($currentPlan && $currentPlan->industries) {
                foreach ($currentPlan->industries as $industry) {
                    $industries[] = [
                        'id' => $industry->id,
                        'title' => $industry->name,
                    ];
                }
            }
        }
        return $industries;
    }

    public function sessionChildLatestIndustry()
    {
        $user_id = $this->sessionChild();
        $industry = [
            'id' => '',
            'title' => '',
        ];
        if ($user_id) {
            $currentPlan = Plan::where('user_id', $user_id)->with(['industries'])
                ->with(array('industries' => function ($query) {
                    $query
                        ->with(array('units' => function ($query) {
                            $query->orderBy('industryunit_id', 'DESC');
                        }))
                        ->latest()->take(5);
                }))
                ->latest()->first();

            if ($currentPlan && $currentPlan->industries) {
                $inds = $currentPlan->industries->first();
                if ($inds) {
                    $industry = [
                        'id' => $inds->id,
                        'title' => $inds->name,
                    ];
                }
            }
        }
        return $industry;
    }

    public function teacherInsights()
    {
        $user = $this;
        if ($user->isTeacher()) {
            $studentsAccountsCreated =  $user->school->detail->total_students . '/' . ($user->school->detail->account_limit ?? $user->school->plans->sum('max_students'));

            $teachersAccountsCreated =  $user->school->detail->total_teachers;
            $schoolRenewalDate =   Carbon::parse($user->school->detail->subscription_ending_on)->format('d/m/y');
            $studentsLogin =   $user->school->detail->students_login;
            $studentsUsage =   $user->school->detail->students_login_duration;
            $schoolPassword =    $user->school->password;

            $insights = [
                'studentsAccountsCreated' => $studentsAccountsCreated,
                'teachersAccountCreated' => $teachersAccountsCreated,
                'schoolRenewalDate' => $schoolRenewalDate,
                'studentsLogin' => $studentsLogin,
                'studentsUsage' => $studentsUsage,
                'schoolPassword' => $schoolPassword,
            ];

            return $insights;
        }
    }

    public function lastActiveStudents()
    {
        $user = $this;
        $students = [];
        if ($user->isTeacher()) {
            foreach ($user->school->detail->lastActiveStudents as $student) {
                $students[] = [
                    'id' => $student->id,
                    'name' => $student->name,
                    'last_active' => Carbon::parse($student->last_active)->isoFormat('HH:mm D MMM'),
                ];
            }
        }
        return $students;
    }

    public function hasAccess()
    {
        $user = $this;
        $access = '';
        if ($user->isTeacher()) {
            $access = ($user->profile->access ?? '');
        }
        return $access;
    }
}
