import ApiService from '@/services/Api.decode.service';
import { AxiosResponse } from 'axios';
import {
    SchoolGuidesResponse,
    StatisticsResponse,
    StudentsQueryParams,
    StudentsResponse,
    UploadSchoolGuideRequest,
} from '../models/SubjectManagement.api.model';
import { log } from '@/services/Logger.service';
import { FilterApi, PaginationApi } from '@/common/models/Api.model';

/**
 * <PERSON>les teacher side subject management
 */
export class SubjectManagementService {
    private apiService: ApiService;

    private static instance: SubjectManagementService;

    private constructor() {
        this.apiService = ApiService.getInstance();
    }

    public static getInstance(): SubjectManagementService {
        if (!this.instance) {
            this.instance = new SubjectManagementService();
        }
        return this.instance;
    }

    public getStatistics(): Promise<AxiosResponse<StatisticsResponse>> {
        return this.apiService.get({ url: '/subject-selections-quiz/statistics' });
    }

    public getStudents({
        pagination,
        filter,
    }: {
        pagination?: PaginationApi;
        filter?: FilterApi & StudentsQueryParams;
    }): Promise<AxiosResponse<StudentsResponse>> {
        const params = {
            ...pagination,
            ...filter,
        };

        return this.apiService.get({
            url: '/students',
            config: {
                params,
                paramsSerializer: {
                    serialize: (params: Record<string, any>): string => {
                        const searchParams = new URLSearchParams();

                        for (const key in params) {
                            if (params.hasOwnProperty(key)) {
                                const value = params[key];
                                searchParams.set(key, value);
                            }
                        }

                        return searchParams.toString();
                    },
                },
            },
        });
    }

    public uploadSchoolGuides({ attachments }: UploadSchoolGuideRequest): Promise<AxiosResponse<SchoolGuidesResponse>> {
        log().debug({ message: `Attachments in SubjectManagement.service` });
        console.log(attachments);

        return this.apiService.post({
            url: '/subject-selections-quiz/school-guides',
            data: attachments,
            config: {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            },
        });
    }

    public deleteSchoolGuide(schoolGuideId: number) {
        return this.apiService.delete({ url: `/subject-selections-quiz/school-guides/${schoolGuideId}` });
    }

    public getSchoolGuides(): Promise<AxiosResponse<SchoolGuidesResponse>> {
        return this.apiService.get({ url: '/subject-selections-quiz/school-guides' });
    }

    public shareResultsViaEmail(emails: string[]) {
        return this.apiService.post({
            url: '/subject-selections-quiz/send-email',
            data: {
                emails,
            },
        });
    }
}
