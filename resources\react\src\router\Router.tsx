import { PropsWithChildren } from 'react';
import { Navigate, Route, Routes, createBrowserRouter, createRoutesFromElements } from 'react-router-dom';
import { ROOT, SUBJECTS_SELECTION } from './Router.constants';
import { RouteConfig, studentRoutes, subjectSelectionQuizRoutes, teacherRoutes } from './Router.config';

import useAuthApiConsumer from '@/domains/Auth/consumers/useAuthApiConsumer';
import { NotFoundPage, RootPage } from '@/domains/Root/Root';
import { Roles } from '@/domains/Auth/models/Auth.model';
import { SubjectSelectionPage } from '@/domains/SubjectSelection/SubjectSelection';
interface PrivateRouteProps {
    roles: Roles[];
}

export default function Router() {
    return (
        <Routes>
            <Route path={ROOT} element={<RootPage />}>
                {mapRoutes(studentRoutes)}
                {mapRoutes(teacherRoutes)}
                <Route path={SUBJECTS_SELECTION} element={<SubjectSelectionPage />}>
                    {mapRoutes(subjectSelectionQuizRoutes)}
                </Route>
            </Route>
            <Route path='*' element={<NotFoundPage />} />
        </Routes>
    );
}

const mapRoutes = (routes: RouteConfig[]) => {
    return routes.map((routeConfig) => {
        const Component = routeConfig.component as React.ElementType;
        return (
            <Route
                key={routeConfig.name}
                path={routeConfig.path}
                handle={{
                    crumb: {
                        name: routeConfig.name,
                        isCrumb: routeConfig?.isCrumb ?? false,
                    },
                }}
                element={
                    <PrivateRoute roles={routeConfig.roles}>
                        <Component />
                    </PrivateRoute>
                }
            />
        );
    });
};

const PrivateRoute = ({ children, roles }: PropsWithChildren<PrivateRouteProps>) => {
    const {
        store: { user },
    } = useAuthApiConsumer();

    const userHasRequiredRole = user && roles.includes(user.role);

    if (user && !userHasRequiredRole) {
        return <Navigate to={ROOT} />;
    }

    return <>{children}</>;
};

export const router = createBrowserRouter(
    createRoutesFromElements(
        <>
            <Route path={ROOT} element={<RootPage />}>
                {mapRoutes(studentRoutes)}
                {mapRoutes(teacherRoutes)}
                <Route path={SUBJECTS_SELECTION} element={<SubjectSelectionPage />}>
                    {mapRoutes(subjectSelectionQuizRoutes)}
                </Route>
            </Route>
            <Route path='*' element={<NotFoundPage />} />
        </>,
    ),
);
