import SearchIcon from '@/assets/icons/subjectSelection/SearchIcon';
import React, { useEffect, useState } from 'react';

export default function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: {
    value: string | number;
    onChange: (value: string | number) => void;
    debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>) {
    const [value, setValue] = useState(initialValue);

    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value);
        }, debounce);

        return () => clearTimeout(timeout);
    }, [value]);

    return (
        <form style={{ width: 160 }} className='search__input w-100 position-relative mb-3'>
            <span className='svg-icon svg-icon-gray-500 position-absolute top-50 translate-middle-y'>
                <SearchIcon />
            </span>
            <input
                {...props}
                style={{
                    background: '#F6F8FA',
                }}
                className='form-control form-control-flush ps-16 fw-bold fs-5'
                value={value}
                onChange={(e) => setValue(e.target.value)}
            />
        </form>
    );
}
