import HomeSolid from '@/assets/icons/sidebar/student/HomeSolid';
import { NavigationPages } from '../models/Navigation.model';
import UserSolid from '@/assets/icons/sidebar/student/UserSolid';
import Users from '@/assets/icons/sidebar/teacher/Users';
import Resources from '@/assets/icons/sidebar/teacher/Resources';
import Manage from '@/assets/icons/sidebar/teacher/Manage';
import Insights from '@/assets/icons/sidebar/teacher/Insights';
import Connect from '@/assets/icons/sidebar/student/Connect';
import Support from '@/assets/icons/sidebar/student/Support';

const teacherNavigation: NavigationPages[] = [
    {
        title: 'Home',
        route: '/#/dashboard',
        isExternal: true,
        svgIcon: HomeSolid,
        fontIcon: 'bi-app-indicator',
    },
    {
        title: 'Profile',
        isExternal: true,
        route: '/profiles/edit',
        svgIcon: UserSolid,
        fontIcon: 'bi-person',
    },
    {
        title: 'Users',
        route: '/users',
        isExternal: true,
        svgIcon: Users,
        fontIcon: 'bi-person',
        sub: [
            // {  //new index for students
            //     heading: "Students",
            //     route: "/users/getting-started",
            //     isExternal:false,
            // },
            {
                title: 'Students',
                route: '/students',
                isExternal: true,
            },
            {
                title: 'Teachers',
                route: '/teachers',
                isExternal: true,
            },
        ],
    },
    {
        title: 'Resources',
        svgIcon: Resources,
        fontIcon: 'bi-people-fill',
        route: '/teacherresources',
        isExternal: true,
        sub: [
            {
                title: 'Teacher Resources',
                route: '/teacherresources',
                isExternal: true,
            },
        ],
    },
    {
        route: '/manage',
        title: 'Manage',
        svgIcon: Manage,
        fontIcon: 'bi-people-fill',
        isExternal: true,
        sub: [
            {
                title: 'Lessons Manage',
                route: '/tasks-manage',
                isExternal: true,
            },
            {
                title: 'Lessons Responses',
                route: '/student-tasks',
                isExternal: true,
            },
            {
                title: 'Work Experience Manage',
                route: '/wew/manage',
                isExternal: true,
            },
            {
                title: 'Work Experience Responses',
                route: '/wew/responses',
                isExternal: true,
            },
            {
                title: 'Subject Selections',
                route: '/subjects-selection/teacher',
            },
        ],
    },
    {
        title: 'Insights',
        svgIcon: Insights,
        route: '/insights',
        fontIcon: 'bi-people-fill',
        isExternal: true,
        sub: [
            {
                title: 'Student Reports',
                route: '/reports',
                isExternal: true,
            },
        ],
    },
    {
        title: 'Support',
        route: '/',
        svgIcon: Support,
        fontIcon: 'bi-node-minus',
        svgClass: 'connect-icon',
        isExternal: true,
        sub: [
            // {
            //     title: 'Noticeboard',
            //     route: '/noticeboard',
            //     isExternal: true,
            // },
            {
                title: 'FB Group',
                route: 'https://www.facebook.com/groups/991216071271987/',
                isExternal: true,
                inNewTab: true,
            },
            {
                title: 'Help Centre',
                route: 'https://help.thecareersdepartment.com/en/',
                isExternal: true,
                inNewTab: true,
            },
        ],
    },
];

export default teacherNavigation;
