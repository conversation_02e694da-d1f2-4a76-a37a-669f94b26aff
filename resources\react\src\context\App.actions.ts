import {
    SubjectPreference,
    SubjectSelectionQuizState,
    SubjectsMap,
} from '@/domains/SubjectSelection/models/SubjectSelection.model';
import {
    AppActionType,
    SetMappedSubjectsState,
    SetQuizSubmissionState,
    SetRulesProgressByIdState,
    SetRulesProgressState,
    SetSelectedPreferencesState,
    SetSubjectPreferenceDataState,
    SetSubjectPreferencesSubmissionState,
    SetSubmittedQuizSelectionState,
    SetUserState,
} from './App.types';
import { UserResponse } from '@/domains/Auth/models/Auth.model';
import { SubjectPreferenceStatus } from '@/domains/SubjectSelection/models/SubjectSelection.api.model';

export const setUserDataAction = (payload: UserResponse): SetUserState => ({
    type: AppActionType.SET_USER_DATA,
    payload,
});

export const setQuizSubmissionState = (payload: boolean): SetQuizSubmissionState => ({
    type: AppActionType.SET_QUIZ_SUBMITTED,
    payload,
});

export const setMappedSubjectsAction = (payload: Map<number | 'other', SubjectsMap>): SetMappedSubjectsState => ({
    type: AppActionType.SET_MAPPED_SUBJECTS,
    payload,
});

export const setSelectedPreferencesState = (payload: SubjectPreference): SetSelectedPreferencesState => ({
    type: AppActionType.SET_SELECTED_PREFERENCES,
    payload,
});

export const setPreferencesDataState = (
    payload: SubjectPreference[],
    status: SubjectPreferenceStatus,
): SetSubjectPreferenceDataState => ({
    type: AppActionType.SET_PREFERENCES_DATA,
    payload: { subjects: payload, status },
});

export const setSubjectPreferencesSubmissionState = (payload: boolean): SetSubjectPreferencesSubmissionState => ({
    type: AppActionType.SET_PREFERENCES_SUBMISSION,
    payload,
});

export const setSubmittedQuizSelectionState = (payload: SubjectSelectionQuizState): SetSubmittedQuizSelectionState => ({
    type: AppActionType.SET_QUIZ_SUBMITTED_SELECTION,
    payload,
});

export const setRulesProgressByIdState = (payload: number): SetRulesProgressByIdState => ({
    type: AppActionType.SET_RULES_PROGRESS_BY_ID,
    payload,
});

export const setRulesProgressState = (payload: number[]): SetRulesProgressState => ({
    type: AppActionType.SET_RULES_PROGRESS,
    payload,
});
