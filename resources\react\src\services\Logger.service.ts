/**
 *
 * Add
 *
 * MIX_LOGGER=true
 * MIX_APP_ENV=local
 *
 * to your .env file for this logger to work
 */

interface Logger {
    message: string;
    item?: any;
    error?: Error;
}

export const log = () => {
    if (process.env.MIX_LOGGER === 'false' && process.env.MIX_APP_ENV !== 'local') {
        return {
            debug: () => {},
            info: () => {},
            warn: () => {},
            error: () => {},
            fatalError: () => {},
        };
    }

    return {
        debug,
        info,
        warn,
        error,
        fatalError,
    };
};

const debug = ({ message, item }: Logger) => {
    console.log(`%c[DEBUG]: ${message}`, 'color:#bc24bc; font-weight:bold;');
    item && console.log(item);
};

const info = ({ message, item }: Logger) => {
    console.log(`%c[INFO]: ${message}`, 'color:#92a8d1; font-weight:bold;');
    item && console.info(item);
};

const warn = ({ message, item }: Logger) => {
    console.log(`%c[WARNING]: ${message}`, 'color:#efc050; font-weight:bold;');
    item && console.log(item);
};

const error = ({ message, error }: Logger) => {
    console.log(`%c[ERROR]: ${message}`, 'color:#bc243c; font-weight:bold;');
    error && console.log(`%c${error?.stack}`, 'color:#bc243c; font-weight:bold;');
};

const fatalError = ({ message, error }: Logger) => {
    console.log(`%c[FATAL - ERROR]: ${message}`, 'color:#961d2f; font-weight:bold;');
    error && console.log(`%c${error?.stack}`, 'color:#961d2f; font-weight:bold;');
};

// TODO: detailed logging for future
// enum LOG_LEVEL {
//     trace = 10,
//     debug = 20,
//     info = 30,
//     warn = 40,
//     error = 50,
//     fatal = 60,
// }

// interface LogMessage {
//     context: {
//         logLevel: LOG_LEVEL;
//     };
//     message: string;
//     sequence: string;
//     time: number;
//     version: string;
// }
