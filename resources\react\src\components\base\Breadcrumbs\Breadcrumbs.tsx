import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import { useSubjects } from '@/domains/SubjectSelection/consumers/useSubjectSelectionQuizApiConsumer';
import { useEffect, useState } from 'react';
import { Link, useLocation, useMatches } from 'react-router-dom';

export default function Breadcrumbs() {
    const location = useLocation();
    const { data: user } = useUserData();
    const { isLoading, data: subjects } = useSubjects();
    const matches: any = useMatches();


    const [cache, setCache] = useState<{ name?: string; path?: string }[]>([]);

    // useEffect(() => {
    //     if (!user && !isLoading) return;

    //     let newCache = [...cache];
    //     const cachedPaths = newCache.map((route) => route.path);
    //     console.log(cachedPaths);

    //     if (user && !cachedPaths.includes(`/subjects-selection/${user.role}`)) {
    //         newCache = [
    //             {
    //                 name: 'Home',
    //                 path: `/subjects-selection/${user.role}`,
    //             },
    //             ...newCache,
    //         ];
    //     }

    //     for (const match of matches) {
    //         if (match.handle?.crumb?.isCrumb && !cachedPaths.includes(match.pathname)) {
    //             const subjectId = match.params?.id;
    //             const subject = subjects?.find((subject) => subject.id === parseInt(subjectId, 10));
    //             newCache = [
    //                 ...cache,
    //                 {
    //                     name: subject?.title ?? match.handle?.crumb?.name,
    //                     path: match.pathname,
    //                 },
    //             ];
    //         }
    //     }

    //     setCache(newCache);
    // }, [location, matches, subjects, isLoading, user]);

    useEffect(() => {
        if (!user && !isLoading) return;

        let newCache: { name?: string; path?: string }[] = [];

        if (user) {
            newCache = [
                {
                    name: 'Home',
                    path: `/subjects-selection/${user.role}`,
                },
            ];
        }

        const lastMatch = matches[matches.length - 1];
        if (lastMatch) {
            const subjectId = lastMatch.params?.id;
            const subject = subjects?.find((subject) => subject.id === parseInt(subjectId, 10));
            if (subject || lastMatch.handle?.crumb?.name !== 'Home') {
                newCache.push({
                    name: subject?.title ?? lastMatch.handle?.crumb?.name,
                    path: lastMatch.pathname,
                });
            }
        }
        setCache(newCache);
    }, [location, matches, subjects, isLoading, user]);

    const regex = new RegExp('\\b' + '/quiz' + '\\b');

    const containsWord = regex.test(location.pathname);


    if (containsWord) {
        return <div />;
    }

    if (!user) return <div />;

    function handleRouteClick(index: number) {
        setCache(cache.slice(0, index + 1));
    }

    function handleHomeClick() {
        if (user) {
            setCache([
                {
                    name: 'Home',
                    path: `/subjects-selection/${user.role}`,
                },
            ]);
        }
    }



    return (
        <div className='breadcrumbs'>
            <div className='breadcrumbs__container'>
                <h1 className='fw-bold fs-4'>Dashboard</h1>
                <ul>
                    {cache.map((route, index) => {
                        let onClick = () => handleRouteClick(index);

                        if (index === 0) {
                            onClick = handleHomeClick;
                        }

                        return (
                            <div className='d-flex' key={`${route.path}-${index}`}>
                                {index !== 0 && <span>-</span>}
                                <li>
                                    <Link to={route.path as string} onClick={onClick}>
                                        {route.name}
                                    </Link>
                                </li>
                            </div>
                        );
                    })}
                </ul>
            </div>
        </div>
    );
}
