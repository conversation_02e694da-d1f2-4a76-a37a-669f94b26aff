import Button from '@/components/base/Button/Button';
import useToast from '@/hooks/useToast';
import { SUBJECTS_SELECTION_STUDENT_LANDING } from '@/router/Router.constants';
import { ButtonType } from '@/utils/enums';
import { useNavigate } from 'react-router-dom';

interface Props {
    onButtonClick?: () => void;
}

const RecommendationsCalculationLayout = ({ onButtonClick }: Props) => {
    const navigate = useNavigate();
    const { show } = useToast();

    return (
        <div className='calculation'>
            <img src={'/videos/ssq-videos/lunchbag.gif'} alt='gif' />
            <div className='calculation__text-container'>
                <p className='calculation__text'>Your recommendations are calculating...</p>
            </div>
            <div className='calculation__button-container'>
                <Button
                    title='View Subjects'
                    type={ButtonType.SECONDARY}
                    onClick={() => {
                        onButtonClick?.();
                        setTimeout(() => {
                            show({
                                message: 'Your results have been submitted! Please wait for their confirmation.',
                            });
                        }, 500);

                        navigate(SUBJECTS_SELECTION_STUDENT_LANDING);
                    }}
                />
            </div>
        </div>
    );
};

export default RecommendationsCalculationLayout;
