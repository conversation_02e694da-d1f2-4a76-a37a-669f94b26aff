import { ButtonType } from '@/utils/enums';
import classNames from 'classnames';

interface ButtonProps {
    id?: string;
    title: string;
    onClick?: () => void;
    type?: string;
    style?: React.CSSProperties;
    spanClassName?: string;
    className?: string;
    spinner?: boolean;
    disabled?: boolean;
    element?: any;
}

const Button = ({
    id,
    title,
    onClick,
    type,
    className,
    style,
    spinner,
    disabled,
    spanClassName,
    element,
}: ButtonProps) => {
    const buttonClassNames = classNames('btn', {
        'button-primary': type === ButtonType.PRIMARY,
        'button-secondary': type === ButtonType.SECONDARY,
        'button-gray': type === ButtonType.GRAY,
        'button-gray-darker': type === ButtonType.GRAY_DARKER,
        'button-outlined-white': type === ButtonType.OUTLINED_WHITE,
        'button-outlined-black': type === ButtonType.OUTLINED_BLACK,
    });

    const borderClassNames = classNames({
        'border border-transparent':
            type === ButtonType.PRIMARY || type === ButtonType.SECONDARY || type === ButtonType.GRAY,
        'border border-white': type === ButtonType.OUTLINED_WHITE,
        'border border-dark': type === ButtonType.OUTLINED_BLACK,
    });

    return (
        <button
            id={id}
            style={style}
            type={element}
            className={`${buttonClassNames} ${borderClassNames} ${className}`}
            onClick={onClick}
            disabled={disabled}
        >
            <span className={`${spinner ? 'fs-4 me-2' : 'fs-4'} ${spanClassName ?? ''}`}>{title}</span>
            {spinner && <div className='spinner-border' style={{ width: '1rem', height: '1rem' }} role='status' />}
        </button>
    );
};

export default Button;
