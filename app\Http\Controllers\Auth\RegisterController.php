<?php

namespace App\Http\Controllers\Auth;

use App\ChildInvitee;
use App\Http\Controllers\Controller;
// use Mail;
use App\Jobs\UpdateMailchimpAudienceJob;
use App\Mail\SchoolAccountLimitNotification;
use App\IndividualStudent;
use App\Mail\PleaseConfirmYourEmail;
use App\Profile;
use App\School;
use App\Standard;
use App\State;
use App\Student;
use App\User;
use Auth;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Country;
use App\Events\LicenseRenewed;
use App\SchoolDetail;
use App\Events\StudentRegistered;
use App\Organisation;
use Illuminate\Support\Facades\Crypt;
use PDO;
use Illuminate\Support\Facades\Storage;
use App\ParentInvitee;
use Carbon\Carbon;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
     */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|max:255',
            'email' => 'required|email|max:255|unique:users',
            'password' => 'required|min:6|confirmed',
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array $data
     * @return User
     */
    protected function create(array $data)
    {
        return User::forceCreate([
            'name' => $data['name'],
            'email' => $data['email'],
            'role_id' => 3,
            'school_id' => 0,
            'password' => bcrypt($data['password']),
            'confirmation_token' => str_limit(md5($data['email'] . str_random()), 25, ''),
        ]);
    }

    /**
     * The user has been registered.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \App\User                $user
     * @return void
     */
    protected function registered(Request $request, $user)
    {
        Mail::to($user)->send(new PleaseConfirmYourEmail($user));
    }

    public function selectschool()
    {
        return view('auth.selectschool');
    }

    public function school(Request $request, $slug)
    {
        if ($slug == "ambition-with-south-west-connect") {
            return redirect('organisation/' . $slug);
        }
        $request->session()->forget('data');
        $school = SchoolDetail::whereHas('school')->whereSlug($slug)->first();
        if ($school) {
            $request->session()->put('data.school_id', $school->school_id);
            return view('auth.school', compact('school'));
        }
        abort(404);
    }
    public function validateParentInviteToken(Request $request)
    {

        $token = $request->invitetoken;
        $countries = Country::All();
        if ($token) {
            $invitation = ParentInvitee::whereToken($token)->with('child:id,email,role_id,school_id,organisation_id', 'child.profile:user_id,firstname,lastname')->first(); //'child:id,email,role_id,school_id,organisation_id' this columns are used to find active licence
            if ($invitation) {
                $parent = User::whereEmail($invitation->email)->first();
                if ($parent) {
                    if (Auth::check()) {
                        if ($parent->email != Auth::user()->email) {
                            return redirect()->route('profile-edit')->with('message', 'You have to log out from this account and log in using the email address you got the invitation on.');
                        } elseif (!$parent->isParent()) {
                            return redirect()->route('profile-edit')->with('message', "Sorry! You can't use the account to purchase the child licence as this is not a parent account.");
                        }
                        return redirect('/profiles/edit#children')->with('message', "You can connect your account with your child from the 'CHILDREN' section below.");
                    }
                    session(['link' => '/profiles/edit#children', 'prevId' => $parent->id]);
                    return redirect('/login')->with('message', "Please login with the parent account to continue!");
                }
            }
        }
        $slug = '';
        // $request->session()->forget('data');
        $school = School::find($request->school);
        if ($school) {
            if ($school->password === $request->password) {
                if ($request->has('campus')) {
                    $request->session()->put('data.campus_id', $request->campus);
                }
                return view('auth.searchemail', compact('school'));
            }
            return redirect('school/' . $slug)->with('error', "Password didn't match!");
        }
        return redirect('school/' . $slug)->with('error', "Whoops, something went wrong. Please try again.");
    }
    public function validateSchoolDetail(Request $request, $slug)
    {
        // $request->session()->forget('data');
        $school = School::find($request->school);
        if ($school) {
            if ($school->password === $request->password) {
                if ($request->has('campus')) {
                    $request->session()->put('data.campus_id', $request->campus);
                }
                return view('auth.searchemail', compact('school'));
            }
            return redirect('school/' . $slug)->with('error', "Password didn't match!");
        }
        return redirect('school/' . $slug)->with('error', "Whoops, something went wrong. Please try again.");
    }

    public function schoolsearchresult()
    {
        $term = request('q');
        $org = Organisation::confirmed()->select('id', 'name', 'role_id')->where('name', 'like', "%" . $term . "%")->with('detail:school_id,slug', 'role:id,name')->whereHas('detail', function ($q) {
            $q->whereDate('subscription_ending_on', '>=', Carbon::now())->where('secondary_section', 1)
                ->where(function ($query) {
                    $query->whereNull('subscription_start_date')->orWhere('subscription_start_date', '<=', date('Y-m-d'));
                });
        });
        $schools = School::confirmed()->select('id', 'name', 'role_id')->where('name', 'like', "%" . $term . "%")->with('detail:school_id,slug,logo', 'role:id,name', 'campuses')->whereHas('detail', function ($q) {
            $q->whereDate('subscription_ending_on', '>=', Carbon::now())->where('secondary_section', 1)
                ->where(function ($query) {
                    $query->whereNull('subscription_start_date')->orWhere('subscription_start_date', '<=', date('Y-m-d'));
                });
        })->union($org)->limit(10)->get();
        // dd($schools);

        $schoollist = [];
        if ($schools) {
            foreach ($schools as $school) {
                /* TODO optimize this to get plan_years with above query */
                $schoollist[] = [
                    'id' => $school->id,
                    'name' => $school->name,
                    'logo' => ($school->detail->logo) ? Storage::url($school->detail->logo) : '',
                    'campuses' => $school->campuses,
                    // 'years'=>$school->plan_years
                    'years' => Standard::secondarySchool()->pluck('title', 'id')->toArray(),
                    'slug' => $school->detail->slug,
                    'type' => $school->role->name,
                ];
            }
        }
        return $schoollist;
    }

    public function searchEmail()
    {
        $term = request('q');
        $school_id = session('data.school_id');
        if ($term) {
            if (filter_var($term, FILTER_VALIDATE_EMAIL)) {
                $children = IndividualStudent::select('id', 'name', 'email')->where('email', $term)->whereHas('profile', function ($query) {
                    $nonstudent = Standard::nonStudentId();
                    $query->where('accountcreated', '1')->where('standard_id', '<>', $nonstudent);
                })->first();

                if ($children) {
                    return $children;
                } else {
                    $student = Student::select('id', 'email')->where('email', $term)
                        ->where(function ($query) use ($school_id) {
                            $query->where('school_id', $school_id)->orWhereNull('school_id');
                        })/* ->where('school_id', $school_id) */ ->orderBy('name')->first();
                }
                if (!$student) {
                    $student = collect(['id' => $term, 'email' => 'Create new account']);
                }
                return $student;
            }
        }
        return 'Invalid email address';
    }
    public function autoregister(Request $request, $token)
    {

        try {
            $decrypted = Crypt::decryptString($token);
            $data = json_decode(base64_decode($decrypted));
            session(['data' => ['school_id' => $data->school_id, 'user_id' => $data->user_id]]);
            return redirect()->action(
                'Auth\RegisterController@createaccount',
                ['school' => $data->school_id, 'student' => $data->user_id]
            );
        } catch (DecryptException $e) {
            abort(500);
        }
    }

    public function createaccount(Request $request)
    {
        if ($request->has('school')) {
            $countries = Country::all();
            $states = State::all();
            $schoolId = $request->session()->get('data.school_id');
            $request->session()->forget('data.user_id');
            $student = null;

            if (!$schoolId) {
                session()->forget('data');
                return redirect('/school/select-school')->with('message', 'Your session has expired due to inactivity. Please try again.');
            }

            $school = School::find($schoolId);
            if ($school->detail->hasFullSchoolAccess()) {
                $years = Standard::onlySchool()->get();
            } elseif ($school->detail->hasSecondarySchoolAccess()) {
                $years = Standard::secondarySchool()->get();
            } else {
                $years = Standard::primarySchool()->get();
            }

            if (is_numeric(request('student'))) {
                $student = User::with('profile')->find(request('student')); //User model is used to get the bot Student and Individual students
            }
            if (!$student) {
                $student = new Student();
                $student->email = request('student');
                $student->school_id = request('school');
                return view('auth/registernew', compact('student', 'years', 'countries', 'states', 'school'));
            }

            $request->session()->put('data.user_id', $student->id);
            if ($student->role_id == 4 || (!$student->school_id && $student->profile->accountcreated)) {
                // if (!School::hasSpace($school, $student->profile->standard_id)) {
                //     return redirect('/school/select-school')->with('message', "Sorry! You can't connect your account to your school's account because maximum number of students has already been registered for year " . $student->profile->standard_id . ".");
                // }

                $request->session()->put('data.connect', true);

                return redirect('/connectschool');

                // return view('auth.connectschool', compact('student', 'school'));
            }
            if (!filter_var($student->email, FILTER_VALIDATE_EMAIL)) {
                $student->email = '';
            }
            if ($student->profile->accountcreated) {
                return redirect('/login')->with('message', 'Looks like you already have gone through this step. Please login with your email and password');
            }
            return view('auth/register', compact('student', 'years', 'countries', 'states', 'school'));
        } else {
            $school_id = $request->session()->get('data.school_id');
            if (!$school_id) {
                session()->forget('data');
                return redirect('/school/select-school')->with('message', 'Your session has expired due to inactivity. Please try again.');
            }
            if (!School::find($school_id)->student_limit_reached) {
                // if ($request->session()->get('data.school_id') && $request->has('year')) {
                //     if (!School::hasSpace($school_id, $request->year)) {
                //         return redirect('/school/select-school')->with('message', 'Oops! Student registration has failed because maximum number of students has already been registered for year ' . $request->year . '!');
                //     }
                // }

                $profile = new Profile();

                if ($request->has(['firstname', 'lastname'])) {
                    $profile->firstname = request('firstname');
                    $profile->lastname = request('lastname');
                }
                if (request('other_gender')) {
                    $gender = request('other_gender');
                } else {
                    $gender = request('gender');
                }

                if ($gender) {
                    $profile->gender = request('gender');
                }
                if ($request->has('year')) {
                    $profile->standard_id = request('year');
                }
                $profile->accountcreated = true;

                if (!$request->session()->get('data.user_id')) {
                    $user = User::where('email', request('email'))->first();
                    if ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists()) {
                        $student = $user;
                    } else {
                        $student = new Student;
                        $student->email = request('email');
                    }
                    $student->role_id = 3;
                    $student->name = request('firstname') . " " . request('lastname');
                    $student->school_id = $school_id;
                    $student->password = bcrypt(request('password'));
                    $student->state_id = request('state');
                    $student->postcode = request('postcode');
                    $student->save();
                    if ($user && !@$user->profile->accountcreated && $user->childInvitations()->exists()) {
                        $student->profile()->update($profile->toArray());
                        ChildInvitee::where('child_id', $student->id)->whereIn(
                            'parent_id',
                            $student->parents()->pluck('parent_id')
                        )->update(['processed' => '1']);
                        if ($school_id) {
                            event(new StudentRegistered($student));
                        }
                    } else {
                        $student->profile()->save($profile);
                        event(new StudentRegistered($student));
                    }
                    if ($request->session()->has('data.campus_id')) {
                        $student->campuses()->sync($request->session()->get('data.campus_id'));
                    }

                    $school = $student->school;
                    if ($student->school->detail->total_students == $student->school->account_limit - 20) {
                        $response = "alert";
                        Mail::send(new SchoolAccountLimitNotification($response, $school));
                    } else if ($student->school->detail->total_students == $student->school->account_limit) {
                        $response = "complete";
                        Mail::send(new SchoolAccountLimitNotification($response, $school));
                    }
                } else {
                    $student = Student::findOrFail($request->session()->get('data.user_id'));
                    if ($student->profile->accountcreated) {
                        return redirect('/login')->with('message', 'Looks like you already have gone through this step. Please login with your email and password');
                    }
                    if ($request->has(['firstname', 'lastname'])) {
                        $student->name = request('firstname') . " " . request('lastname');
                    }
                    if ($request->has('email')) {
                        $student->email = request('email');
                    }
                    $student->password = bcrypt(request('password'));
                    if ($request->has('state')) {
                        $student->state_id = request('state');
                    }
                    if ($request->has('postcode')) {
                        $student->postcode = request('postcode');
                    }
                    $student->save();
                    $student->profile()->update($profile->toArray());
                    if ($student->school) {
                        event(new StudentRegistered($student));
                    }
                    if ($request->session()->has('data.campus_id')) {
                        $student->campuses()->sync($request->session()->get('data.campus_id'));
                    }

                    $school = $student->school;
                    if ($student->school->detail->total_students == $student->school->account_limit - 20) {
                        $response = "alert";
                        Mail::send(new SchoolAccountLimitNotification($response, $school));
                    } else if ($student->school->detail->total_students == $student->school->account_limit) {
                        $response = "complete";
                        Mail::send(new SchoolAccountLimitNotification($response, $school));
                    }
                }

                \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));

                // User::addHelpcrunchAccount($student->id);
                if ($student->is_child) {
                    User::updateAssociatedUsersMailchimpDetail($student->id);
                }
                $user = User::find($student->id);
                UpdateMailchimpAudienceJob::dispatch($user);

                if (Auth::attempt(['email' => $student->email, 'password' => request('password')])) {
                    return redirect('/#/gameplan')->with('message', "Your account has been successfully created.");
                }
            } else {
                $redirectUrl = url()->previous();
                if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                    $redirectUrl = session()->get('previousUrl');
                }
                return redirect($redirectUrl)->with('message', "Sorry! Your school has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.");
            }
        }
    }


    public function showCreateAccount()
    {
        if (!(session()->has('data.connect'))) {
            session()->forget('data');
            return redirect('/school/select-school');
        }
        session()->forget('data.connect');
        $student = User::find(session('data.user_id'));
        $school = School::find(session('data.school_id'));

        return view('auth.connectschool', compact('student', 'school'));
    }



    public function login($id)
    {
        $name = User::where("id", $id)->first();
        return view('auth/login')->with('name', $name);
    }

    public function connectschool(Request $request)
    {
        $student = User::find($request->session()->get('data.user_id'));
        $school_id = $request->session()->get('data.school_id');
        if (!School::find($school_id)->student_limit_reached) {
            if (Auth::validate(['id' => $student->id, 'password' => $request->password])) {
                $student->role_id = 3;
                $student->school_id = $school_id;

                $profile = $student->profile;
                $profile->school = null;

                $student->save();
                $student->profile()->save($profile);
                $user = Student::find($student->id);
                if ($school_id) {
                    event(new StudentRegistered($user));
                }
                if ($request->session()->has('data.campus_id')) {
                    $student->campuses()->sync($request->session()->get('data.campus_id'));
                }

                $school = $student->school;
                if ($student->school->detail->total_students == $student->school->account_limit - 20) {
                    $response = "alert";
                    Mail::send(new SchoolAccountLimitNotification($response, $school));
                } else if ($student->school->detail->total_students == $student->school->account_limit) {
                    $response = "complete";
                    Mail::send(new SchoolAccountLimitNotification($response, $school));
                }

                \Event::dispatch(new LicenseRenewed(User::where("id", $student->id)->first()));

                // User::addHelpcrunchAccount($student->id);
                $user = User::find($student->id);
                UpdateMailchimpAudienceJob::dispatch($user);
                if (Auth::attempt(['email' => $student->email, 'password' => $request->password])) {
                    return redirect('/home')->with('confirmation', "Your account has been successfully connected to your school.");
                }
            }

            // $school = School::find($request->session()->get('data.school_id'));
            // $request->session()->flash('message', 'You have entered a wrong password');
            // return view('auth.connectschool', compact('student', 'school'));
            session()->put('data.connect', true);
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', 'You have entered a wrong password');
        } else {
            $redirectUrl = url()->previous();
            if ($redirectUrl == url('updateUserSession') || strpos($redirectUrl, 'api') !== false) {
                $redirectUrl = session()->get('previousUrl');
            }
            return redirect($redirectUrl)->with('message', "Sorry! Your school has reached their student account limit. Please get in touch with your career adviser to let them know you would like to create an account.");
        }
    }

    public function showConnectschool()
    {
        if (!(session()->has('data.connect'))) {
            session()->forget('data');
            return redirect('/school/select-school');
        }
        session()->forget('data.connect');
        $student = User::find(session('data.user_id'));
        $school = School::find(session('data.school_id'));

        return view('auth.connectschool', compact('student', 'school'));
    }
}
