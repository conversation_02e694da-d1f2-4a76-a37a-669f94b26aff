import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSubjectManagementDeps } from '../SubjectManagement';
import { FilterApi, PaginationApi } from '@/common/models/Api.model';
import {
    SchoolReport,
    SchoolRulesDetailsResponse,
    SchoolSubjectManagementFilterResponse,
    SchoolSubjectsFilterQueryParams,
    SchoolSubjectsResponse,
} from '../models/Schools.service.api.model';
import useToast from '@/hooks/useToast';
import { log } from '@/services/Logger.service';

export enum SchoolsApiConsumer {
    schoolSubjects = 'schoolSubjects',
    schoolRules = 'schoolRules',
    schoolSubjectManagementFilter = 'schoolSubjectManagementFilter',
}

export const useSchoolSubjects = ({
    schoolId,
    pagination,
    filter,
}: {
    schoolId: number | null;
    pagination: PaginationApi;
    filter: FilterApi & SchoolSubjectsFilterQueryParams;
}) => {
    const { schoolsService } = useSubjectManagementDeps();

    return useQuery<SchoolSubjectsResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [SchoolsApiConsumer.schoolSubjects, pagination, filter],
        keepPreviousData: true,
        queryFn: async () => {
            const response = await schoolsService.getSchoolSubjects({ schoolId, pagination, filter });
            return response.data;
        },
        onError: () => {
            log().error({ message: 'useSchoolSubjects - fetching school subjects failed' });
        },
    });
};

export const useExportSchoolSubjects = () => {
    const toast = useToast();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: (schoolId: number) => {
            return schoolsService.requestSchoolSubjectsCsvExport(schoolId);
        },
        onSuccess: async (response) => {
            const { message } = response.data;
            toast.show({
                message,
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while exporting subjects. If error persists, please contact support.',
            });
            log().error({ message: 'useExportSchoolSubjects -  exporting subjects failed' });
        },
    });
};

export const useExportSchoolStudents = () => {
    const toast = useToast();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: (schoolId: number) => {
            return schoolsService.requestSchoolStudentsCsvExport(schoolId);
        },
        onSuccess: async (response) => {
            const { message } = response.data;
            toast.show({
                message,
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while exporting subjects. If error persists, please contact support.',
            });
            log().error({ message: 'useExportSchoolSubjects -  exporting subjects failed' });
        },
    });
};

export const useToggleSubjectVisibility = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: ({ schoolId, subjects }: { schoolId: number; subjects: number[] }) => {
            return schoolsService.updateSchoolSubject(schoolId, subjects);
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SchoolsApiConsumer.schoolSubjects]);

            toast.show({
                message: 'Subject visibility changed successfully',
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while changing subject visibility',
            });
            log().error({ message: 'useShareResultsViaEmail - changing subject visibility failed' });
        },
    });
};

export const useSchoolRules = ({ schoolId }: { schoolId?: number | null }) => {
    const { schoolsService } = useSubjectManagementDeps();

    return useQuery<SchoolRulesDetailsResponse[]>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [SchoolsApiConsumer.schoolRules],
        keepPreviousData: true,
        queryFn: async () => {
            if (!schoolId) return Promise.resolve([]);

            const response = await schoolsService.getSchoolRules({ schoolId });
            return response.data.data;
        },
        onError: () => {
            log().error({ message: 'useSchoolRules - fetching school rules failed' });
        },
    });
};

export const useCreateSchoolRule = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: ({ schoolId, name }: { schoolId?: number | null; name: string }) => {
            if (!schoolId) {
                return Promise.reject();
            }

            return schoolsService.createSchoolRule(schoolId, name);
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SchoolsApiConsumer.schoolRules]);

            toast.show({
                message: 'New school rule created',
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while creating new school rule',
            });
            log().error({ message: 'useCreateSchoolRule - creating new school rule failed' });
        },
    });
};

export const useUpdateSchoolRule = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: ({ schoolId, ruleId, name }: { schoolId?: number | null; ruleId: number; name: string }) => {
            if (!schoolId) {
                return Promise.reject();
            }

            return schoolsService.updateSchoolRule(schoolId, ruleId, name);
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SchoolsApiConsumer.schoolRules]);

            toast.show({
                message: 'School rule updated',
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while updating a school rule',
            });
            log().error({ message: 'useUpdateSchoolRule - updating the school rule failed' });
        },
    });
};

export const useDeleteSchoolRule = () => {
    const toast = useToast();
    const queryClient = useQueryClient();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: ({ schoolId, ruleId }: { schoolId?: number | null; ruleId: number }) => {
            if (!schoolId) {
                return Promise.reject();
            }

            return schoolsService.deleteSchoolRule(schoolId, ruleId);
        },
        onSuccess: async () => {
            await queryClient.invalidateQueries([SchoolsApiConsumer.schoolRules]);

            toast.show({
                message: 'School rule deleted',
            });
        },
        onError: () => {
            toast.show({
                message: 'Something went wrong while deleting the school rule',
            });
            log().error({ message: 'useDeleteSchoolRule - deleting the school rule failed' });
        },
    });
};

export const useSubjectManagementFilters = () => {
    const { schoolsService } = useSubjectManagementDeps();

    return useQuery<SchoolSubjectManagementFilterResponse>({
        staleTime: Infinity,
        cacheTime: Infinity,
        queryKey: [SchoolsApiConsumer.schoolSubjectManagementFilter],
        keepPreviousData: true,
        queryFn: async () => {
            const response = await schoolsService.getSchoolSubjectManagementFilters();
            return response.data.data;
        },
        onError: () => {
            log().error({ message: 'useSubjectManagementFilters - fetching filters failed' });
        },
    });
};

export const useSchoolReport = () => {
    const toast = useToast();
    const { schoolsService } = useSubjectManagementDeps();

    return useMutation({
        mutationFn: async ({ schoolId, type }: { schoolId: number | null; type: SchoolReport }) => {
            const response = await schoolsService.getSchoolReport(schoolId, type);
            return response.data.message;
        },
        onSuccess: (message) => {
            toast.show({
                message,
            });
        },
        onError: () => {
            log().error({ message: 'useSchoolReport - triggering school report generation failed' });
        },
    });
};
