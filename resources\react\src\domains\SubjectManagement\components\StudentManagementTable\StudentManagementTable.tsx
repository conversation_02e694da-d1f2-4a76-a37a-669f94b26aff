import { useEffect, useMemo, useState } from 'react';
import {
    flexRender,
    getCoreRowModel,
    useReactTable,
    ColumnDef,
    getFilteredRowModel,
    getPaginationRowModel,
} from '@tanstack/react-table';
import Table from '@/components/base/Table/Table';
import Pill from '@/components/Pill/Pill';
import classNames from 'classnames';
import { useStudents } from '../../consumers/useSubjectManagementApiConsumer';
import { Student } from '../../models/SubjectManagement.api.model';
import Drawer, { useDrawer } from '@/components/portals/Drawer/Drawer';
import SubjectPreferences from '@/components/SubjectPreferences/SubjectPreferences';
import { SubjectPreference } from '@/domains/SubjectSelection/models/SubjectSelection.model';
import SearchIcon from '@/assets/icons/subjectSelection/SearchIcon';
import FilterIcon from '@/assets/icons/subjectSelection/FilterIcon';
import { SubjectPreferenceStatus } from '@/domains/SubjectSelection/models/SubjectSelection.api.model';
import Filter from '@/components/Filter/Filter';
import Button from '@/components/base/Button/Button';
import { ButtonType } from '@/utils/enums';
import FileExport from '@/assets/icons/subjectSelection/FileExport';
import { useExportSchoolStudents } from '../../consumers/useSchoolsApiConsumer';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import axios from 'axios';

enum FilterKeys {
    QUIZ = 'quiz',
    SELECTIONS = 'selections',
    YEAR = 'year',
    CAMPUS = 'campus'
}

export default function StudentManagementTable() {
    const [selectedPreferences, setSelectedPreferences] = useState<SubjectPreference[]>([]);
    const [status, setStatus] = useState<SubjectPreferenceStatus | undefined>();
    const [page, setPage] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [search, setSearch] = useState('');

    const { data: user } = useUserData();

    const [selectedFilters, setSelectedFilters] = useState(new Map());
    const [queryParams, setQueryParams] = useState({});

    const [isFilter, setIsFilter] = useState(false);

    const { isLoading: isRequestingCsv, mutate: requestStudentCsvExport } = useExportSchoolStudents();

    const { isClosing, isOpen, toggle: handleToggleClick } = useDrawer();
    const {
        isLoading,
        data: students,
        isRefetching,
    } = useStudents({
        pagination: {
            page,
            per_page: perPage,
        },
        filter: {
            search,
            ...queryParams,
        },
    });
    // TODO: enable row selection when needed
    // const [rowSelection, setRowSelection] = useState({});
    const [globalFilter, setGlobalFilter] = useState('');

    const [data, setData] = useState(() => [...(students?.data || [])]);

    useEffect(() => {
        if (!isLoading) {
            setData([...(students?.data || [])]);
        }
        fetchYears();
        fetchCampus();
    }, [isLoading, students]);



    const columns = useMemo<ColumnDef<Student>[]>(() => {
        return [
            // {
            //     id: 'select',
            //     header: ({ table }) => (
            //         <IndeterminateCheckbox
            //             {...{
            //                 checked: table.getIsAllRowsSelected(),
            //                 indeterminate: table.getIsSomeRowsSelected(),
            //                 onChange: table.getToggleAllRowsSelectedHandler(),
            //             }}
            //         />
            //     ),
            //     cell: ({ row }) => (
            //         <div className='px-1'>
            //             <IndeterminateCheckbox
            //                 {...{
            //                     checked: row.getIsSelected(),
            //                     disabled: !row.getCanSelect(),
            //                     indeterminate: row.getIsSomeSelected(),
            //                     onChange: row.getToggleSelectedHandler(),
            //                 }}
            //             />
            //         </div>
            //     ),
            // },
            {
                header: 'User',
                accessorKey: 'name',
                cell: (info) => {
                    return (
                        <div className='d-flex align-items-center'>
                            <Avatar className='avatar me-3' name={info.row.original.name} />
                            <div className='user-info'>
                                <span className='d-block'>{`${info.row.original.name}`}</span>
                                <span className='d-block'>{`${info.row.original.email}`}</span>
                            </div>
                        </div>
                    );
                },
            },
            {
                header: 'Year',
                accessorKey: 'year',
                cell: (info) => info.renderValue(),
            },
            {
                header: 'Last login',
                accessorKey: 'last_login',
                cell: (info) => {
                    const text = info.row.original.last_login === '' ? 'Never' : info.row.original.last_login;

                    return <Pill className='gray' style={{ color: '#7F8297' }} text={`${text}`} />;
                },
            },
            {
                header: 'Quiz',
                accessorKey: 'attachment_url',
                cell: (info) => {
                    const quiz = info.row.original.attachment_url;

                    const quizStatusStyle = classNames({
                        status: true,
                        'status--incomplete': quiz === '',
                    });

                    const text = quiz === '' ? 'incomplete' : 'completed';

                    if (text === 'incomplete') {
                        return <div className={quizStatusStyle}>{text}</div>;
                    }

                    return (
                        <a href={quiz} target='_blank' className={quizStatusStyle}>
                            {text}
                        </a>
                    );
                },
            },
            {
                header: 'Selections',
                accessorKey: 'subject_preferences',
                cell: (info) => {
                    const status = info.row.original.status;
                    const selections = info.row.original.subject_preferences;
                    const isStartedWithSelection =
                        status === SubjectPreferenceStatus.SAVED ||
                        (status === SubjectPreferenceStatus.IN_PROGRESS && !!selections.length);

                    const selectionsStatusStyle = classNames({
                        status: true,
                        'status--incomplete': !isStartedWithSelection,
                    });

                    let text = 'not started';

                    if (status === SubjectPreferenceStatus.IN_PROGRESS && !!selections.length) {
                        text = 'in progress';
                    }

                    if (status === SubjectPreferenceStatus.SAVED) {
                        text = 'submitted';
                    }

                    if (!isStartedWithSelection) {
                        return <div className={selectionsStatusStyle}>{text}</div>;
                    }

                    return (
                        <div
                            className={`cursor-pointer ${selectionsStatusStyle}`}
                            onClick={() => handleDrawerClick(info.row.original.id)}
                        >
                            {text}
                        </div>
                    );
                },
            },
            {
                header: 'Campus',
                accessorKey: 'campus',
                cell: (info: any) => {
                    const campuses = info.row.original.campuses;

                    const campusNames = campuses.join(', ');

                    return (
                        <Pill className='gray' style={{ color: '#7F8297' }} text={campusNames || 'Unassigned'} />
                    );
                },
            }
        ];
    }, [students]);

    const table = useReactTable({
        data,
        columns,
        state: {
            // rowSelection,
            globalFilter,
            pagination: {
                pageIndex: page - 1,
                pageSize: 10,
            },
        },
        pageCount: students?.meta.last_page || 0,
        manualPagination: true,
        // enableRowSelection: true,
        // onRowSelectionChange: setRowSelection,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    });

    function handleDrawerClick(studentId: number) {
        const student = students?.data.find((student) => student.id === studentId);
        const preferences = student?.subject_preferences || [];
        setStatus(student?.status);
        setSelectedPreferences(
            preferences.map((item) => {
                return {
                    id: item.id,
                    name: item.title,
                    units: item.units,
                    type: item.subject_type.name,
                    course_type: item.course_type ? item.course_type.name : null,
                };
            }),
        );
        handleToggleClick();
    };
    const [years, setYears] = useState([]);

    const fetchYears = () => {
        axios.get('/api/v1/getYears')
            .then(response => setYears(response.data))
            .catch(error => console.error('Error fetching options:', error));
    };

    const [campus, setCampus] = useState([]);

    const fetchCampus = () => {
        axios.get('/api/v1/getCampuses')
            .then(response => {
                // console.log(response.data);
                setCampus(response.data);

                // {
                //     Object.entries(campus).map(([id, name]) => (
                //         console.log(id, name)
                //     ))
                // }
            })
            .catch(error => console.error('Error fetching options:', error));
    }

    const handleFilterClick = () => {
        setIsFilter(!isFilter);
    };

    const handleFilterChange = ({ filter, value }: { filter: FilterKeys; value?: any }) => {
        setSelectedFilters((previousState) => {
            const newSelectedFilters = new Map(previousState);
            newSelectedFilters.set(filter, value);

            Array.from(newSelectedFilters.entries()).forEach(([key]) => {
                if (key.length === 0) {
                    newSelectedFilters.delete(key);
                }
            });

            return newSelectedFilters;
        });
    };

    const handleApplyFilters = () => {
        const queryParams: any = {};

        Array.from(selectedFilters.entries()).forEach(([key, value]) => {
            queryParams[key] = value;
        });

        setQueryParams(queryParams);
        setIsFilter(false);
    };

    const handleResetFilters = () => {
        setSelectedFilters((previousSelections) => {
            const newSelections = new Map(previousSelections);
            newSelections.clear();

            return newSelections;
        });

        setQueryParams({});
        setIsFilter(false);
    };

    const handlePerPageChange = (e: any) => {
        setPerPage(e.currentTarget.value);
    };

    const handleExportCsv = () => {
        user?.schoolId && requestStudentCsvExport(user.schoolId);
    };

    return (
        <div className='student-management-table'>
            <div className='student-management-table__actions d-flex justify-content-between'>
                <div className='d-flex'>
                    <DebouncedInput
                        value={globalFilter ?? ''}
                        onChange={(value) => {
                            setSearch(String(value));
                            setPage(1);
                        }}
                        className='p-2 font-lg shadow border border-block'
                        placeholder='Search user'
                    />
                    {isRequestingCsv && (
                        <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                            <div className='d-flex'>
                                <div className='spinner-border me-3 text-primary' role='status' />
                                <div className='fs-5 d-flex align-items-center'>Requesting CSV download...</div>
                            </div>
                        </div>
                    )}
                    {(isLoading || isRefetching) && (
                        <div className='ms-5 w-100 mb-3 d-flex align-items-center'>
                            <div className='d-flex'>
                                <div className='spinner-border me-3 text-primary' role='status' />
                                <div className='fs-5 d-flex align-items-center'>Loading students...</div>
                            </div>
                        </div>
                    )}
                </div>
                <div className='button-actions d-flex'>
                    <div className='filter me-5'>
                        <button className='btn filter__button' onClick={handleFilterClick}>
                            <FilterIcon />
                            <span className='fs-6 ms-2'>Filter</span>
                        </button>
                        <Filter title='Select filter' isHidden={!isFilter} className='filter__dropdown'>
                            <span className='filter-component__filter-title mb-2'>Quiz</span>
                            <select
                                style={{
                                    margin: 0,
                                    border: '1px solid',
                                    backgroundColor: 'hsl(0, 0%, 100%)',
                                    borderColor: 'hsl(0, 0%, 80%)',
                                    borderRadius: 4,
                                    padding: 8,
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                value={selectedFilters.get(FilterKeys.QUIZ) ?? 'default-quiz'}
                                className='filter-component__dropdown fs-6 fw-bold'
                                onChange={(event) =>
                                    handleFilterChange({
                                        filter: FilterKeys.QUIZ,
                                        value: event.currentTarget.value,
                                    })
                                }
                            >
                                <option value='default-quiz' disabled>
                                    Choose an option
                                </option>
                                <option value='completed'>Completed</option>
                                <option value='incomplete'>Incomplete</option>
                            </select>
                            <span className='filter-component__filter-title mb-2'>Selections</span>
                            <select
                                style={{
                                    margin: 0,
                                    border: '1px solid',
                                    backgroundColor: 'hsl(0, 0%, 100%)',
                                    borderColor: 'hsl(0, 0%, 80%)',
                                    borderRadius: 4,
                                    padding: 8,
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                value={selectedFilters.get(FilterKeys.SELECTIONS) ?? 'default-selections'}
                                className='filter-component__dropdown fs-6 fw-bold'
                                onChange={(event) =>
                                    handleFilterChange({
                                        filter: FilterKeys.SELECTIONS,
                                        value: event.currentTarget.value,
                                    })
                                }
                            >
                                <option value='default-selections' disabled>
                                    Choose an option
                                </option>
                                <option value='in_progress'>In progress</option>
                                <option value='not_started'>Not started</option>
                                <option value='submitted'>Submitted</option>
                            </select>
                            <span className='filter-component__filter-title mb-2'>Year</span>
                            <select
                                style={{
                                    margin: 0,
                                    border: '1px solid',
                                    backgroundColor: 'hsl(0, 0%, 100%)',
                                    borderColor: 'hsl(0, 0%, 80%)',
                                    borderRadius: 4,
                                    padding: 8,
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                value={selectedFilters.get(FilterKeys.YEAR) ?? 'default-years'}
                                className='filter-component__dropdown fs-6 fw-bold'
                                onChange={(event) =>
                                    handleFilterChange({
                                        filter: FilterKeys.YEAR,
                                        value: event.currentTarget.value,
                                    })
                                }
                            >
                                <option value='default-years' disabled>
                                    Choose an option
                                </option>
                                {Object.entries(years).map(([id, title]) => (
                                    <option key={id} value={id}>{title}</option>
                                ))}
                            </select>
                            <span className='filter-component__filter-title mb-2'>Campus</span>
                            <select
                                style={{
                                    margin: 0,
                                    border: '1px solid',
                                    backgroundColor: 'hsl(0, 0%, 100%)',
                                    borderColor: 'hsl(0, 0%, 80%)',
                                    borderRadius: 4,
                                    padding: 8,
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                value={selectedFilters.get(FilterKeys.CAMPUS) ?? 'default-campus'}
                                className='filter-component__dropdown fs-6 fw-bold'
                                onChange={(event) =>
                                    handleFilterChange({
                                        filter: FilterKeys.CAMPUS,
                                        value: event.currentTarget.value,
                                    })
                                }
                            >
                                <option value='default-campus' disabled>
                                    Choose an option
                                </option>
                                {Object.entries(campus).map(([id, name]) => (
                                    <option key={id} value={id}>{name}</option>
                                ))}
                            </select>

                            <div className='filter-component__button-row'>
                                <Button
                                    className='mb-6 fs-7 fw-normal filter-component__button grey btn-sm'
                                    onClick={handleResetFilters}
                                    title='Reset'
                                    type={ButtonType.GRAY}
                                />
                                <Button
                                    className='mb-6 fs-7 fw-normal filter-component__button btn-sm'
                                    onClick={handleApplyFilters}
                                    title='Apply'
                                    type={ButtonType.PRIMARY}
                                />
                            </div>
                        </Filter>
                    </div>
                    <button className='btn export' onClick={handleExportCsv}>
                        <FileExport />
                        <span className='fs-6 ms-2'>Export</span>
                    </button>
                </div>
            </div>
            {!!Object.keys(queryParams).length && (

                <div className='my-2'>
                    <span className='fw-bold fs-6 me-2'>Your filters:</span>
                    {Object.entries(queryParams).map(([key, value]) => {
                        const content: any = {
                            [FilterKeys.QUIZ]: `Quiz`,
                            [FilterKeys.SELECTIONS]: `Selections`,
                            [FilterKeys.YEAR]: `Year`,
                            [FilterKeys.CAMPUS]: `Campus`,
                        };

                        const text: any = {
                            completed: 'Completed',
                            incomplete: 'Incomplete',
                            in_progress: 'In progress',
                            not_started: 'Not started',
                            submitted: 'Submitted',
                            "1": "7",
                            "2": "8",
                            "3": "9",
                            "4": "10",
                            "5": "11",
                            "6": "12",
                            "7": "I’ve finished high school",
                            campusestext : campus
                        };

                        return (
                            <span
                                style={{
                                    padding: '4px 6px',
                                    background: 'hsl(0, 0%, 90%)',
                                    borderRadius: '2px',
                                    color: 'hsl(0, 0%, 20%)',
                                }}
                                className='me-2'
                                key={key}
                            >
                                {content[key]}: {key === FilterKeys.CAMPUS ? text.campusestext[value as string] : text[value as string]}
                            </span>
                        );
                    })}
                </div>
            )}
            <Table
                className='student-management-table__component'
                header={() =>
                    table.getHeaderGroups().map((headerGroup) => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                                <th className='fw-bold' key={header.id}>
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(header.column.columnDef.header, header.getContext())}
                                </th>
                            ))}
                        </tr>
                    ))
                }
                body={() =>
                    table.getRowModel().rows.map((row) => (
                        <tr key={row.id}>
                            {row.getVisibleCells().map((cell) => (
                                <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                            ))}
                        </tr>
                    ))
                }
            />
            <div className='h-2' />
            <div className='d-flex justify-content-between'>
                <div className='d-flex align-items-center'>
                    <span className='fs-6 fw-bold me-3'>Go to page:</span>
                    <input
                        type='number'
                        style={{
                            width: 60,
                            height: 40,
                            background: '#F6F8FA',
                        }}
                        className='form-control form-control-flush fw-bold fs-5'
                        defaultValue={table.getState().pagination.pageIndex + 1}
                        onChange={(e) => {
                            const page = e.target.value ? Number(e.target.value) : 0;
                            setPage(page);
                        }}
                    />
                </div>
                <div className='d-flex align-items-center'>
                    <div className='me-5'>
                        <span className='fs-6 fw-bold me-3'>Per page:</span>
                        <select
                            className='border'
                            style={{ width: 40, height: 30 }}
                            value={perPage}
                            onChange={handlePerPageChange}
                        >
                            <option value='10'>10</option>
                            <option value='20'>20</option>
                            <option value='30'>30</option>
                            <option value='40'>40</option>
                            <option value='50'>50</option>
                        </select>
                    </div>
                    <div className='me-5'>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage(1)}
                            disabled={!table.getCanPreviousPage()}
                        >
                            {'<<'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage((old) => Math.max(old - 1, 1))}
                            disabled={!table.getCanPreviousPage()}
                        >
                            {'<'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => {
                                setPage((old) => old + 1);
                            }}
                            disabled={!table.getCanNextPage()}
                        >
                            {'>'}
                        </button>
                        <button
                            style={{ width: 30, height: 30 }}
                            className='border fs-6 fw-bold me-2'
                            onClick={() => setPage(table.getPageCount())}
                            disabled={!table.getCanNextPage()}
                        >
                            {'>>'}
                        </button>
                    </div>
                    <span className='d-flex items-center gap-2'>
                        <div>Page</div>
                        <strong>
                            {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                        </strong>
                    </span>
                </div>
            </div>
            <Drawer isClosing={isClosing} isOpen={isOpen} handleToggleClick={handleToggleClick}>
                <SubjectPreferences items={selectedPreferences} status={status} />
            </Drawer>
        </div>
    );
}

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: {
    value: string | number;
    onChange: (value: string | number) => void;
    debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'>) {
    const [value, setValue] = useState(initialValue);

    useEffect(() => {
        setValue(initialValue);
    }, [initialValue]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value);
        }, debounce);

        return () => clearTimeout(timeout);
    }, [value]);

    return (
        <form style={{ width: 160 }} className='search__input w-100 position-relative mb-3'>
            <span className='svg-icon svg-icon-gray-500 position-absolute top-50 translate-middle-y'>
                <SearchIcon />
            </span>
            <input
                {...props}
                style={{
                    background: '#F6F8FA',
                }}
                className='form-control form-control-flush ps-16 fw-bold fs-5'
                value={value}
                onChange={(e) => setValue(e.target.value)}
            />
        </form>
    );
}

interface Props {
    className?: string;
    name: string;
}

const Avatar: React.FC<Props> = ({ name, className }) => {
    const firstLetter = name ? name[0].toUpperCase() : '';

    return (
        <div className={`${className} avatar`}>
            <span>{firstLetter}</span>
        </div>
    );
};
