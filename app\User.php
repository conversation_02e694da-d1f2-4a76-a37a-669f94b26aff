<?php

namespace App;

use App\Library\CustomClasses\Indeed;
use App\Subject;
use App\Traits\HasIntercomUserInfo;
use App\Traits\UserSessionStatsTrait;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Auth;
use Laravel\Cashier\Billable;
use Newsletter;
use DB;
use Illuminate\Support\Facades\Cache;
use Carbon\CarbonInterval;
use Storage;
use Str;
use Illuminate\Support\Facades\Log;
use Overtrue\LaravelFavorite\Traits\Favoriter;
use App\Events\UserUpdated;
use App\Notifications\CustomResetPasswordNotification;
use App\Traits\HasUserAccess;

class User extends Authenticatable
{
    use Notifiable, Billable, Favoriter, HasIntercomUserInfo, UserSessionStatsTrait, HasUserAccess;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
        'trial_ends_at',
        'license_end_date'
    ];
    protected $fillable = [
        'role_id',
        // 'parent_id',
        'school_id',
        'name',
        'email',
        'password',
        'avatar_path',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'stripe_id',
        'card_brand',
        'card_last_four',
        'trial_ends_at',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'confirmed' => 'boolean',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array
     */
    protected $with = ['profile','intercom'];

    protected static function booted()
    {

        static::saved(function ($student) {
            event(new UserUpdated($student));
        });
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CustomResetPasswordNotification($token));
    }

    public function getEmailAttribute($value)
    {
        return (filter_var($value, FILTER_VALIDATE_EMAIL)) ? $value : "";
    }

    /**
     * User has a role.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function role()
    {
        return $this->belongsTo(Role::class);
    }
    /**
     * Fetch all threads that were created by the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function threads()
    {
        return $this->hasMany(Thread::class)->latest();
    }

    /**
     * Fetch the last published reply for the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function lastReply()
    {
        return $this->hasOne(Reply::class)->latest();
    }

    /**
     * Get all activity for the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function activity()
    {
        return $this->hasMany(Activity::class);
    }
    /**
     * Get all activity for the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function lastPlan()
    {
        // if ($this->isNonStudent()) {
        //     return $this->hasOne(NonStudentPlan::class)->latest()->with(['currentIndustry', 'getintoIndustry'])->first();
        // }
        // $lastPlan = Cache::rememberForever('lastPlan' . $this->id, function () {
        //     return $this->hasOne(Plan::class)->latest()->with(['industries'])/* ->with(['tafes', 'colleges', 'universities', 'institituteOthers', 'industries', 'student.profile', 'individual.profile']) */->first();
        // });

        $lastPlan = Cache::rememberForever('latestGameplan' . $this->id, function () {
            return $this->hasOne(Gameplan::class)->latest()->with(['industries', 'interestedIn', 'jobs', 'institutes', 'companies'])->first();
        });
        return $lastPlan;
    }

    public function latestGameplan()
    {
        return Cache::rememberForever('latestGameplan' . $this->id, function () {
            return $this->hasOne(Gameplan::class)->latest()->with(['industries', 'courses'])->first();
        });
    }

    public function school()
    {
        return $this->belongsTo(School::class);
    }

    public function organisation()
    {
        return $this->belongsTo(Organisation::class);
    }

    public function campuses()
    {
        return $this->belongsToMany(Campus::class);
    }

    public function orgCampuses()
    {
        return $this->belongsToMany(Campus::class, 'orgcampus_user');
    }

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function sessions()
    {
        return $this->hasMany(UserSession::class)->whereNotNull('duration')->where('duration', '<>', 0);
    }

    public function sessionsUnfiltered()
    {
        return $this->hasMany(UserSession::class);
    }

    public function sessionsFilteredLatest()
    {
        return $this->hasMany(UserSession::class)
            ->whereNotNull('duration')
            ->whereNotNull('loggedin_at')
            ->whereNotNull('loggedout_at')
            ->latest();
    }

    public function lastSession()
    {
        return $this->hasOne(UserSession::class)->latest();
    }

    public function login()
    {
        return $this->hasOne(Login::class);
    }


    // public function license()
    // {
    //     return $this->hasOne(Licence::class, 'assigned_to');
    // }

    public function license()
    {
        return $this->hasOne(Licence::class, 'assigned_to')->latest('valid_upto');
    }


    public function activeLicense()
    {
        return $this->subscription_end_date ? $this->subscription_end_date >= date('Y-m-d') : false;
        // return $this->license()->where('valid_upto', '>=', Carbon::today());
    }
    public function updateCardInfoFromIntent($intent)
    {
        $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
        $intent = $stripe->setupIntents->retrieve(
            $intent,
            []
        );
        if ($intent && $intent->payment_method) {
            $pm = $stripe->paymentMethods->retrieve(
                $intent->payment_method,
                []
            );
            if ($pm && isset($pm->card->last4)) {
                $this->card_last_four = $pm->card->last4;
                $this->card_brand = $pm->card->brand;
                $this->save();
                return true;
            }
        }
        return false;
    }
    /**
     * Mark the user's account as confirmed.
     */
    public function confirm()
    {
        $this->confirmed = true;
        $this->confirmation_token = null;

        $this->save();
    }

    /**
     * Determine if the user is an administrator.
     *
     * @return bool
     */
    public function isAdmin()
    {
        $adminRoleId = Cache::tags(['userRole', 'AdminRoleId'])->rememberForever('AdminRoleId', function () {
            return Role::whereName('Admin')->value('id');
        });
        return $this->role_id == $adminRoleId;
    }
    /**
     * Determine if the user is an administrator.
     *
     * @return bool
     */
    public function isTeacher()
    {
        $teacherRoleId = Cache::tags(['userRole', 'TeacherRoleId'])->rememberForever('TeacherRoleId', function () {
            return Role::whereName('Teacher')->value('id');
        });

        return $this->role_id == $teacherRoleId;
    }

    public function isStudent()
    {
        // return $this->role_id==3;
        return in_array($this->role_id, [3, 4]);
    }
    public function isNonStudent()
    {
        if (/* $this->role_id == Role::whereName('Individual Student')->value('id') &&  */$this->profile->class && $this->profile->class->title == 'I’ve finished high school') {
            return true;
        }

        return false;
    }

    public function isPrimaryStudent()
    {
        if (in_array($this->profile->class->title, [3, 4, 5, 6])) {
            return true;
        }

        return false;
    }

    public function hasSecondarySchoolAccess()
    {
        return $this->isTeacher() ? $this->profile->secondary_section : false;
    }

    public function hasPrimarySchoolAccess()
    {
        return $this->isTeacher() && $this->profile->primary_section;
    }

    public function hasSubjectsSelectionAccess()
    {
        if ($this->isAdmin()) {
            return true;
        } elseif (@$this->school) {
            return $this->school->hasSubjectsSelectionToolAccess();
        }
        return true;
    }


    public function isParent()
    {
        $parentRoleId = Cache::tags(['userRole', 'ParentRoleId'])->rememberForever('ParentRoleId', function () {
            return Role::whereName('Parent')->value('id');
        });
        return $this->role_id == $parentRoleId;
    }
    public function isMarker()
    {
        $markerRoleId = Cache::tags(['userRole', 'MarkerRoleId'])->rememberForever('MarkerRoleId', function () {
            return Role::whereName('Marker')->value('id');
        });
        return $this->role_id == $markerRoleId;
    }
    public function isSchool()
    {
        $schoolRoleId = Cache::tags(['userRole', 'SchoolRoleId'])->rememberForever('SchoolRoleId', function () {
            return Role::whereName('School')->value('id');
        });
        return $this->role_id == $schoolRoleId;
    }
    public function isOrganisation()
    {
        $organisationRoleId = Cache::tags(['userRole', 'OrganisationRoleId'])->rememberForever('OrganisationRoleId', function () {
            return Role::whereName('Organisation')->value('id');
        });
        return $this->role_id == $organisationRoleId;
    }
    public function isStaff()
    {
        $staffRoleId = Cache::tags(['userRole', 'StaffRoleId'])->rememberForever('StaffRoleId', function () {
            return Role::whereName('Staff')->value('id');
        });
        return $this->role_id == $staffRoleId;
    }

    public function isCompany()
    {
        $companyRoleId = Cache::tags(['userRole', 'CompanyRoleId'])->rememberForever('CompanyRoleId', function () {
            return Role::whereName('Company')->value('id');
        });
        return $this->role_id == $companyRoleId;
    }


    public function hasFullAccess()  /* For teachers */
    {
        if ($this->isTeacher() || $this->isStaff()) {
            return Cache::tags(['userhasFullAccess', 'hasFullAccess.' . $this->id])->rememberForever('hasFullAccess' . $this->id, function () {
                return in_array($this->profile->access, ['Full', 'Lead Administrator']);
            });
        }
    }

    public function hasManagerAccess()  /* For teachers */
    {
        if ($this->isTeacher() || $this->isStaff()) {
            return Cache::tags(['userhasManagerAccess', 'hasManagerAccess.' . $this->id])->rememberForever('hasManagerAccess' . $this->id, function () {
                return in_array($this->profile->access, ['Manager', 'Full', 'Lead Administrator']);
            });
        }
    }

    public function isChild()
    {
        return Cache::rememberForever('isChild' . $this->id, function () {
            return $this->parents()->exists();
        });
    }

    public function isIndividual()
    {
        // return Cache::rememberForever('isChild' . $this->id, function () {
        return $this->role_id == 4 && $this->parents()->doesntExist() || ($this->license && $this->license->type == 'Individual');
        // });
    }

    public function isCompassTeacher()
    {
        $compassId = Cache::tags(['userRole', 'CompassTeacherRoleId'])->rememberForever('CompassTeacherRoleId', function () {
            return Role::whereName('Compass Teacher')->value('id');
        });
        return $this->role_id == $compassId;
    }

    /**
     * Record that the user has read the given thread.
     *
     * @param Thread $thread
     */
    public function read($thread)
    {
        cache()->forever(
            $this->visitedThreadCacheKey($thread),
            Carbon::now()
        );
    }

    /**
     * Get the path to the user's avatar.
     *
     * @param  string $avatar
     * @return string
     */
    public function getAvatarPathAttribute($avatar)
    {
        return asset(($avatar  && Storage::exists($avatar)) ? Storage::url($avatar) : 'images/avatars/default.png');
    }

    public function getInitialsAttribute()
    {
        if (is_array($this->profile->firstname) && is_array($this->profile->lastname)) {
            return Str::upper($this->profile->firstname[0] . $this->profile->lastname[0]);
        }
        return;
    }

    public function getSubscriptionEndDateAttribute()
    {
        if ($this->profile) {
            if (($this->isTeacher() || $this->isStaff() || ($this->isStudent()) && (!$this->isChild() && !$this->isIndividual() && !$this->profile->removed && !$this->license))) {
                $end_date = false;
                if ($this->school && $this->organisation) {
                    $sdate = $this->school->detail->subscription_ending_on;
                    $odate = $this->organisation->detail->subscription_ending_on;
                    $end_date = max($sdate, $odate);
                } elseif ($this->school) {
                    $end_date = $this->school->detail->subscription_ending_on;
                } elseif ($this->organisation) {
                    $end_date = $this->organisation->detail->subscription_ending_on;
                }
                return $end_date;
            } elseif (($this->isStudent() && !$this->profile->removed) && ($this->isChild() || $this->isIndividual() || $this->license)) {
                $idate = false;
                if ($this->school && $this->organisation) {
                    $sdate = $this->school->detail->subscription_ending_on;
                    $odate = $this->organisation->detail->subscription_ending_on;
                    $idate = max($sdate, $odate);
                } elseif ($this->school) {
                    $idate = $this->school->detail->subscription_ending_on;
                } elseif ($this->organisation) {
                    $idate = $this->organisation->detail->subscription_ending_on;
                }
                if ($this->license) {
                    $pdate = Carbon::parse($this->license->valid_upto)->toDateString();
                    if (!$idate) {
                        return $pdate;
                    }
                    return max($idate, $pdate);
                } else {
                    return $idate;
                }
            } elseif (($this->isStudent() && $this->profile->removed) || $this->isIndividual() || $this->isChild()) {
                if ($this->license) {
                    return Carbon::parse($this->license->valid_upto)->toDateString();
                }
            }
        }
        return false;
    }

    /**
     * Get the cache key for when a user reads a thread.
     *
     * @param  Thread $thread
     * @return string
     */
    public function visitedThreadCacheKey($thread)
    {
        return sprintf("users.%s.visits.%s", $this->id, $thread->id);
    }

    public function showChatWidget()
    {
        if (Auth::user()->isStudent()) {
            if (IndividualStudent::find(Auth::id())) {
                return true;
            }

            $student = Student::find(Auth::id());
            if ($student->organisation && !$student->school) {
                return $student->hasProAccess() && $student->organisation->detail->livechat;
            } else {
                return $student->hasProAccess() && $student->school->detail->livechat;
            }
        }
        // if (Auth::user()->isParent() || Auth::user()->isAdmin()) {
        return false;
        // }
        // return true;
    }

    public function isFirstLogin()
    {
        if (!$this->firstlogin) {
            return true;
        }
        return (Carbon::createFromFormat('Y-m-d H:i:s', $this->firstlogin) === false);
    }

    public function profilerResult()
    {
        $profiler = Cache::rememberForever('profilerid', function () {
            return Module::where('slug', 'profiler')->value('id');
        });

        return $this->hasOne(StudentModuleResult::class)->where('module_id', $profiler);
    }

    public function getHasCompletedProfilerAttribute()
    {
        return Cache::rememberForever('hasCompletedProfiler' . $this->id, function () {
            return $this->profilerResult()->exists();
        });
    }

    public function childInvitations()
    {
        return $this->hasMany(ChildInvitee::class, 'child_id');
    }

    public function childInvitees()
    {
        return $this->hasMany(ChildInvitee::class, 'parent_id');
    }

    public function parentNotifications()
    {
        return $this->hasMany(IndustryunitSendtoparent::class);
    }

    public function eportfolios()
    {
        return $this->hasMany(Eportfolio::class);
    }

    public function parents()
    {
        return $this->belongsToMany(ChildParent::class, 'child_child_parent', 'child_id', 'parent_id');
    }

    public function dashboardChecklist() // for parents
    {
        return $this->hasOne(DashboardChecklist::class, 'user_id');
    }

    public function children()
    {
        return $this->belongsToMany(self::class, 'child_child_parent', 'parent_id', 'child_id')->whereIn('role_id', [3, 4]);
    }

    public function getIsChildAttribute()
    {
        return Cache::rememberForever('isChild' . $this->id, function () {
            return $this->parents()->exists();
        });
    }

    public function getRequestParentsAttribute()
    {
        $childparents = $this->parents()->pluck('email');
        $parentNotifications = $this->parentNotifications()->pluck('email');
        $diff =  $parentNotifications->diff($childparents);
        $emails =  $this->whereIn('email', $diff)->where('role_id', 5)->pluck('email');
        return $emails;
    }

    public function getBelongsToSchoolAttribute()
    {
        $profile = Cache::tags(['profile', 'profile' . $this->id])->rememberForever('profile' . $this->id, function () {
            return $this->profile;
        });
        if ($profile) {
            return Cache::rememberForever('belongs_to_school' . $this->id, function () {
                return $this->school()->exists() || $this->profile->school || $this->organisation()->exists();
            });
        }
        return false;
    }

    public function getIsIndividualAttribute()
    {
        return ($this->role_id == 4 && $this->parents()->doesntExist()) || ($this->license && $this->license->type == 'Individual');
        // return $this->role_id == 4 && $this->parents()->doesntExist();
    }

    public function workexperienceResponses()
    {
        return $this->hasMany(WorkexperienceResponse::class, 'student_id');
    }

    public function experiences()
    {
        return $this->hasMany(UserExperience::class);
    }

    public function workexperienceHours()
    {
        $responses = $this->workexperienceResponses();
        if ($responses->exists()) {
            $timeinsec =  $this->workexperienceResponses()->sum(DB::raw("TIME_TO_SEC(time)"));
            return round($timeinsec);
            return CarbonInterval::seconds(round($timeinsec))->cascade()->forHumans(['short' => true]);
        }
        return 0;
    }

    public function hasAccessToVirtualworkexperience()
    {
        if ($this->isStudent()) {
            if (Auth::user()->role_id == 4) {
                return true;
            }

            return Cache::tags(['userhasProAccess', 'user.' . Auth::id()])->rememberForever("userhasProAccess." . Auth::id(), function () {
                return Student::find(Auth::id())->hasProAccess();
            });
        }
        if ($this->isTeacher()) {
            return Cache::remember("userhasPremiumAccess." . Auth::id(), 2, function () {
                return Teacher::find(Auth::id())->hasPremiumAccess()/* .Auth::id() */;
            });
        }
        if ($this->isStaff()) {
            return Cache::remember("userhasPremiumAccess." . Auth::id(), 2, function () {
                return Staff::find(Auth::id())->hasPremiumAccess()/* .Auth::id() */;
            });
        }
        if ($this->isParent()) {
            return true;
        }
        return false;
    }

    public function cvs()
    {
        return $this->hasMany(Cv::class, 'student_id');
    }

    public function notices()
    {
        return $this->hasMany(Notice::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'gameplan_courses');
    }

    // public function scholarships()
    // {
    //     return $this->belongsToMany(Scholarship::class);
    // }

    public function industryunits()
    {
        return $this->belongsToMany(Industryunit::class);
    }

    public function thingstoknow()
    {
        return $this->belongsToMany(ThingstoknowTemplate::class);
    }

    public function portfolioSetting()
    {
        return $this->hasOne(Setting::class);
    }

    public function getHasCvAttribute()
    {
        return $this->cvs()->exists();
    }

    public function getLastSeenAttribute()
    {
        $lastSession = $this->sessions()->latest()->first();

        return $lastSession ? Carbon::parse($lastSession->loggedout_at)->toDayDateTimeString() : '-';
    }
    public function getLastLoginTime()
    {
        $lastSession = $this->sessions()->latest()->first();

        return $lastSession ? Carbon::parse($lastSession->loggedout_at)->diffForHumans() : '';
    }

    public function getLoginCountAttribute()
    {
        return $this->sessions()->count();
    }


    public function getAvgSessionDurationAttribute()
    {
        return round($this->sessions()/* ->select(DB::raw('IFNULL(duration, 0) as duration'))->get() */->avg('duration'));
    }

    public static function deleteHelpcrunchAccount($user_id)
    {
        try {
            $client = new \GuzzleHttp\Client([
                'headers' => [
                    "Authorization" => 'Bearer api-key="0f8e191467595a69c088178082252ae4da0f6a39"',
                    "Cache-Control" => "no-cache",
                    "Content-Type" => "application/json",
                ],

            ]);
            $response = $client->request(
                'DELETE',
                'https://thecareersdepartment.helpcrunch.com/api/public/customers',
                [
                    'body' => json_encode([[
                        "user_id" => $user_id,
                    ]]),

                ]
            );
            return;
        } catch (\Exception $ex) {
            return $ex;
        }
    }

    public static function updateUserMailchimpDetail($user_id)
    {

        if (config('app.env') == 'local') {
            return;
        }

        try {
            $user = User::find($user_id);
            if (!$user->profile) {
                return;
            }
            if ($user->school_id != 127) {
                if ($user->isTeacher()) {
                    if ($user->profile->newsletter) {
                        self::updateTeacherMailchimpDetail($user_id);
                    } elseif (Newsletter::hasMember($user->email, 'teachers')) {
                        Newsletter::unsubscribe($user->email, 'teachers');
                    }
                } elseif ($user->isParent()) {
                    self::updateParentMailchimpDetail($user_id);
                } elseif ($user->isStudent() || $user->isIndividual()) {
                    self::updateStudentMailchimpDetail($user_id);
                }
            }
            return;
        } catch (\Exception $ex) {
            Log::error('updateUserMailchimpDetail failed: ' . $ex->getMessage());
            return $ex;
        }
    }

    public static function updateStudentMailchimpDetail($user_id)
    {
        $associated = '';
        $school = '';
        $schoolType = '';
        $schoolGender = '';
        $leaveSchool = '';
        $interestedIn = '';
        $gapYear = '';
        $year = '';
        $stage1 = '';
        $stage2 = '';
        $tags = [];
        $subscription = null;

        $user = IndividualStudent::with('parents')->find($user_id);

        if ($user) {
            if ($user->profile->class->title != 'I’ve finished high school') {
                $school = $user->profile->school;
            }

            if ($user->profile->standard_id && $user->profile->class->title != 'I’ve finished high school') {
                $year = @$user->profile->class->title;
                $stage1 = 'I’m still in high school';
            } else {
                $stage1 = 'I’ve finished high school';
                $stage2 = @$user->profile->stage->title;
            }

            $access = "Premium";
            if ($user->is_child && $user->belongs_to_school) {
                $associated = $user->parents->implode('email', ', ');
                $tags = ['Student', 'Child'];

                if (!$user->school) {
                    if ($user->parents->count() > 1) {
                        foreach ($user->parents as $parent) {
                            $subscriber  = User::find($parent->id);
                            if (!$subscriber->stipe_id) {
                                $subscription = 'Inactive';
                                break;
                            }
                        }

                        foreach ($user->parents as $parent) {
                            $subscriber  = User::find($parent->id);
                            if ($subscriber->onTrial()) {
                                $subscription = 'Trial';
                                break;
                            }
                        }

                        foreach ($user->parents as $parent) {
                            $subscriber  = User::find($parent->id);
                            if ($subscriber->subscription('Parent')->recurring()) {
                                $subscription = null;
                                break;
                            }
                        }
                    } else {
                        $subscriber  = User::find($user->parents->first()->id);
                        if ($subscriber->onTrial()) {
                            $subscription = 'Trial';
                        } elseif (!$subscriber->stipe_id) {
                            $subscription = 'Inactive';
                        }
                    }
                }
            } elseif ($user->is_child && !$user->belongs_to_school) {
                $associated = $user->parents->implode('email', ', ');
                $tags = ['Child'];

                if ($user->parents->count() > 1) {
                    foreach ($user->parents as $parent) {
                        $subscriber  = User::find($parent->id);
                        if (!$subscriber->stipe_id) {
                            $subscription = 'Inactive';
                            break;
                        }
                    }

                    foreach ($user->parents as $parent) {
                        $subscriber  = User::find($parent->id);
                        if ($subscriber->onTrial()) {
                            $subscription = 'Trial';
                            break;
                        }
                    }

                    foreach ($user->parents as $parent) {
                        $subscriber  = User::find($parent->id);
                        if ($subscriber->subscription('Parent')->recurring()) {
                            $subscription = null;
                            break;
                        }
                    }
                } else {
                    $subscriber  = User::find($user->parents->first()->id);
                    if ($subscriber->onTrial()) {
                        $subscription = 'Trial';
                    } elseif (!$subscriber->stipe_id) {
                        $subscription = 'Inactive';
                    }
                }
            } elseif ($user->is_individual && $user->belongs_to_school) {
                $tags = ['Student', 'Individual'];
            } elseif ($user->is_individual && !$user->belongs_to_school) {
                $tags = ['Individual'];
            }


            if ($subscription) {
                array_push($tags, $subscription);
            }
        } else {
            $user = Student::with('parents')->find($user_id);

            if ($user->hasProAccess()) {
                $access = "Premium";
            } else {
                $access = "Standard";
            }


            $school = $user->school->name;
            $schoolType = ($user->school->detail->type) ?  $user->school->detail->type : '';
            $schoolGender = ($user->school->detail->gender) ?  $user->school->detail->gender : '';

            if ($user->is_child) {
                $associated = $user->parents->implode('email', ', ');
                $tags = ['Student', 'Child'];
            } elseif ($user->is_individual) {
                $tags = ['Student', 'Individual'];
            } else {
                $tags = ['Student'];
            }

            $year = @$user->profile->class->title;
        }

        $industries = IndustryCategory::pluck('mailchimp_ids')->mapWithKeys(function ($serializedData) {
            $data = unserialize($serializedData);
            return isset($data['student']) ? [$data['student'] => false] : [];
        });

        if ($latestPlan = $user->plans()->latest()->first()) {
            $leaveSchool = $latestPlan->finishschool;
            $interestedIn = $latestPlan->interested;
            $gapYear = $latestPlan->gapyear;

            if ($latestPlan->industries->isNotEmpty()) {
                $selectedIndustries = $latestPlan->industries->pluck('mailchimp_ids')->filter()->mapWithKeys(function ($serializedData) {
                    $data = unserialize($serializedData);
                    return isset($data['student']) ? [$data['student'] => true] : [];
                });
                $industries = $industries->merge($selectedIndustries);
            }
        }

        // $subjects = SubjectCategory::pluck('mailchimp_id')->mapWithKeys(function ($id) {
        //     return  isset($id) ? [$id => true] : [];
        // });

        // if ($user->subjects->isNotEmpty()) {
        //     $choosed = $user->subjects()->get()->mapWithKeys(function ($value, $key) {
        //         return [$value->category->mailchimp_id => true];
        //     });

        //     $subjects = $subjects->merge($choosed);
        // }

        if ($user->profile->gender == 'F') {
            $gender = 'Female';
        } elseif ($user->profile->gender == 'M') {
            $gender = 'Male';
        } else {
            $gender = 'Other';
        }

        // $u = User::with('profile:user_id,removed', 'license:assigned_to,valid_upto,type', 'school:id', 'school.detail:school_id,subscription_ending_on', 'organisation:id', 'organisation.detail:school_id,subscription_ending_on')->select('id', 'role_id', 'school_id', 'organisation_id')->whereId($user_id)->first();

        // $active = $u->subscription_end_date ? $u->subscription_end_date >= date('Y-m-d') : false;
        $fields = [
            'FNAME' => @$user->profile->firstname,
            'LNAME' => @$user->profile->lastname,
            'COUNTRY' => @$user->state->country->name,
            'STATE' => @$user->state->code,
            'POSTCODE' => @$user->postcode,
            'YEAR' => @$year,
            'STAGE1' => @$stage1,
            'STAGE2' => @$stage2,
            'GENDER' => @$gender,
            'ASSOCIATED' => @$associated,
            'SCHOOL' => @$school,
            'SCHOOLTYPE' => @$schoolType,
            'SGENDER' => @$schoolGender,
            'ACCESS' => @$access,
            'LEAVE' => @$leaveSchool,
            'INTERESTED' => @$interestedIn,
            'GAPYEAR' => @$gapYear,
        ];

        // $interests = $subjects->merge($industries)->toArray();

        Newsletter::subscribeOrUpdate($user->email, $fields, 'students', ['interests' => $industries]);
        // Remove tags for a member in a given list
        Newsletter::removeTags(['Student', 'Child', 'Individual'], $user->email);
        Newsletter::addTags($tags, $user->email);
        return;
    }

    public static function updateParentMailchimpDetail($user_id)
    {
        $user = ChildParent::with('children')->find($user_id);

        $associated = '';
        $schools = '';
        $tags = [];

        $subscriber = User::find($user_id);

        if ($subscriber->onTrial()) {
            $tags = ['Trial'];
        } elseif (!$subscriber->stripe_id) {
            $tags = ['Inactive'];
        }

        $years = Standard::pluck('mailchimp_id')->mapWithKeys(function ($id, $key) {
            return [$id => false];
        });

        if (count($user->children)) {
            $associated = $user->children()->whereHas('profile', function ($qu) {
                $qu->whereAccountcreated(true);
            })->get()->implode('email', ', ');

            $selected = $user->children()->whereHas('profile', function ($qu) {
                $qu->whereAccountcreated(true);
            })->get()->mapWithKeys(function ($value, $key) {
                return [$value->profile->class->mailchimp_id => true];
            });

            $years = $years->merge($selected);

            $schlArray =  [];
            foreach ($user->children()->whereHas('school')->get() as $child) {
                $schlArray[] = $child->school->name;
            }
            foreach ($user->children()->doesntHave('school')->get() as $child) {
                if ($child->profile->school) {
                    $schlArray[] = $child->profile->school;
                }
            }

            $schools = implode(', ', $schlArray);
        }


        $industries = IndustryCategory::pluck('mailchimp_ids')->mapWithKeys(function ($serializedData) {
            $data = unserialize($serializedData);
            return isset($data['parent']) ? [$data['parent'] => false] : [];
        });

        if ($user->industries->isNotEmpty()) {

            $selectedIndustries = $user->industries->pluck('mailchimp_ids')->filter()->mapWithKeys(function ($serializedData) {
                $data = unserialize($serializedData);
                return isset($data['parent']) ? [$data['parent'] => true] : [];
            });

            $industries = $industries->merge($selectedIndustries);
        }

        $fields = [
            'FNAME' => @$user->profile->firstname,
            'LNAME' => @$user->profile->lastname,
            'COUNTRY' => @$user->state->country->name,
            'STATE' => @$user->state->code,
            'POSTCODE' => @$user->postcode,
            'ASSOCIATED' => @$associated,
            'SCHOOL' => @$schools,
        ];

        $interests = $years->merge($industries)->toArray();

        Newsletter::subscribeOrUpdate($user->email, $fields, 'parents', ['interests' => $interests]);
        Newsletter::removeTags(['Trial', 'Inactive'], $user->email, 'parents');
        if (!empty($tags)) {
            Newsletter::addTags($tags, $user->email, 'parents');
        }
        return;
    }

    public static function updateTeacherMailchimpDetail($user_id)
    {
        $user = Teacher::with('school')->find($user_id);

        $fields = [
            'FNAME' => @$user->profile->firstname,
            'LNAME' => @$user->profile->lastname,
            'COUNTRY' => (@$user->state->country->name) ? $user->state->country->name : '',
            'STATE' => (@$user->state->code) ? $user->state->code : '',
            'SCHOOL' => @$user->school->name,
            'STYPE' => (@$user->school->detail->type) ? $user->school->detail->type : '',
            'SPOSTCODE' => (@$user->school->postcode) ? $user->school->postcode : '',
            'SGENDER' => (@$user->school->detail->gender) ? $user->school->detail->gender : '',
            'POSITION' => @$user->positions->implode('name', ', '),
        ];

        Newsletter::subscribeOrUpdate($user->email, $fields, 'teachers');
        return;
    }

    public static function updateAssociatedUsersMailchimpDetail($user_id)
    {
        if (config('app.env') == 'local') {
            return;
        }

        try {
            $user = User::find($user_id);


            if ($user->isParent()) {
                $children = $user->children()->whereHas('profile', function ($q) {
                    $q->whereAccountcreated(true);
                })->get();

                if (count($children)) {
                    foreach ($children as $child) {
                        $fields = [
                            'ACCOCIATED' => @$child->parents->implode('email', ', ')
                        ];

                        Newsletter::subscribeOrUpdate($child->email, $fields, 'students');
                    }
                }
            } else {
                if ($user->school_id != 127) {
                    $parents = $user->parents()->get();
                    $years = Standard::pluck('mailchimp_id')->mapWithKeys(function ($id, $key) {
                        return [$id => false];
                    });
                    foreach ($parents as $parent) {
                        $associated = $parent->children()->whereHas('profile', function ($qu) {
                            $qu->whereAccountcreated(true);
                        })->get()->implode('email', ', ');

                        $years = Standard::pluck('mailchimp_id')->mapWithKeys(function ($id, $key) {
                            return [$id => false];
                        });

                        $selected = $parent->children()->whereHas('profile', function ($qu) {
                            $qu->whereAccountcreated(true);
                        })->get()->mapWithKeys(function ($value, $key) {
                            return [$value->profile->class->mailchimp_id => true];
                        });

                        if (count($selected)) {
                            $years = $years->merge($selected);
                        }

                        $schlArray =  [];
                        foreach ($parent->children()->whereHas('school')->get() as $child) {
                            $schlArray[] = $child->school->name;
                        }
                        foreach ($parent->children()->doesntHave('school')->get() as $child) {
                            if ($child->profile->school) {
                                $schlArray[] = $child->profile->school;
                            }
                        }

                        $schools = implode(', ', $schlArray);

                        $interests = $years->toArray();

                        $fields = [
                            'ASSOCIATED' => @$associated,
                            'SCHOOL' => @$schools,
                        ];

                        $se = Newsletter::subscribeOrUpdate($parent->email, $fields, 'parents', ['interests' => $interests]);
                    }
                }
            }
            return;
        } catch (\Exception $ex) {
            Log::error('updateAssociateUserMailchimpDetail failed: ' . $ex->getMessage());
            return $ex;
        }
    }

    /*Remove Child from parent account*/

    public function removeChild($child_id)
    {
        /*Remove Parent Chlid*/
        Licence::where(['assigned_to' => $child_id, 'purchased_by' => Auth::id()])->orderBy('id', 'desc')->limit(1)->update(['assigned_to' => null]);

        Auth::user()->children()->detach($child_id);
        ChildInvitee::where('child_id', $child_id)->where('parent_id', Auth::id())->delete();

        /*Remove child relationship*/
        return true;
    }

    public function getLicenceValidationAttribute()
    {
        $validupto =  Licence::where(['assigned_to' => $this->id, 'purchased_by' => Auth::id()])->value('valid_upto');
        return $validupto;
    }

    public function getSubscriptionsCountAttribute()
    {
        $subscriptions =  Licence::where(['assigned_to' => null, 'purchased_by' => Auth::id()])->count();
        return $subscriptions;
    }

    public function industries($industries = null)
    {
        $lastplan = $this->lastPlan();
        if ($industries) {
            if (is_array($industries) || is_object($industries)) {
                $industries = is_object($industries) ? $industries->implode(', ') : implode(', ', $industries);
            }
        } elseif (@$lastplan->industries) {
            $industries = $lastplan->industries->pluck('name')->implode(', ');
        }
        return $industries;
    }

    public function location()
    {
        $loc = Cache::rememberForever('location' . $this->id, function () {
            $location = collect();
            if (session()->has('userLocation')) {
                $l = session('userLocation');
                $location->vicinity = @$l['vicinity'];
                $location->address = $l['address'];
                $location->lat = $l['latitude'];
                $location->lng = $l['longitude'];
                return $location;
            } else {

                //uncomment this for google map integration

                // $address = urlencode($this->postcode . ' ' . ($this->state ? $this->state->name : 'Australia'));
                // $key = config('services.maps.key');
                // $result = Http::get("https://maps.googleapis.com/maps/api/geocode/json?key={$key}&address={$address}")->json();

                // if ($result['status'] !== 'OK') {
                //     $location->address = 'Australia';
                //     $location->lat = -33.952152;
                //     $location->lng = 151.126722;

                //     return $location;
                // }

                // $location->address = $result['results'][0]['formatted_address'];
                // $location->lat = $result['results'][0]['geometry']['location']['lat'];
                // $location->lng = $result['results'][0]['geometry']['location']['lng'];

                $location->address = $this->postcode . ', ' . ($this->state ? $this->state->name : 'Australia'); //remove this for google map

                return $location;
            }
        });
        return $loc;
    }

    // public function mapData($industries = null)
    // {
    //     if (!$this->isAdmin()) {
    //         $lastplan = $this->lastPlan();
    //         $mapKey = config('services.maps.key');

    //         $map = collect();

    //         if ($industries) {
    //             $industries = is_object($industries) ? $industries->toArray() : $industries;
    //         } elseif (@$lastplan->industries) {
    //             $industries = $lastplan->industries->pluck('search_keyword')->toArray();
    //         }
    //         // $locationquery = urlencode("{$this->location()->address}");

    //         $seconds = 2592000;
    //         // $locationresult = Cache::remember($locationquery, $seconds, function () use ($mapKey, $locationquery) {
    //         //     return Http::get("https://maps.googleapis.com/maps/api/place/textsearch/json?key={$mapKey}&query={$locationquery}")->json()['results'];
    //         // });
    //         // $map->locationresult = $locationresult;
    //         // $map->put('locationresult', $locationresult);
    //         $companies = [];


    //         if (is_array($industries) || is_object($industries)) {

    //             $industries = array_unique(explode(",", str_replace(' and ', ',', strtolower(implode(",", $industries)))));

    //             $count = count($industries);
    //             switch (true) {
    //                 case $count == 1:
    //                     $slice = 20;
    //                     break;
    //                 case $count == 2:
    //                     $slice = 10;
    //                     break;
    //                 case $count == 3:
    //                     $slice = 7;
    //                     break;
    //                 case $count == 4:
    //                     $slice = 5;
    //                     break;
    //                 case $count == 5:
    //                     $slice = 4;
    //                     break;
    //                 case $count == 6:
    //                     $slice = 4;
    //                     break;
    //                 case $count == 7:
    //                     $slice = 3;
    //                     break;
    //                 case $count == 8:
    //                     $slice = 3;
    //                     break;
    //                 case ($count > 8 && $count  < 14):
    //                     $slice = 2;
    //                     break;

    //                 default:
    //                     $slice = 1;
    //                     break;
    //             }
    //             foreach ($industries as $key => $industry) {
    //                 $query = urlencode("{$industry} companies near {$this->location()->address}");
    //                 Cache::forget($query);
    //                 $result = Cache::remember($query, $seconds, function () use ($mapKey, $query) {
    //                     return Http::get("https://maps.googleapis.com/maps/api/place/textsearch/json?key={$mapKey}&query={$query}&rankBy=distance")->json()['results'];
    //                 });
    //                 if ($result) {
    //                     $resultSliced = array_slice($result, 0, $slice);
    //                     $companies = array_merge($companies, $resultSliced);
    //                 }
    //             }
    //             if ($companies) {
    //                 // $companies =  array_unique($companies, SORT_REGULAR);
    //                 $map->companies =  $companies;
    //                 $map->put('companies', $companies);
    //             }
    //         } else {
    //             $query = urlencode("companies near {$this->location()->address}");
    //             Cache::forget($query);
    //             $result = Cache::remember($query, $seconds, function () use ($mapKey, $query) {
    //                 return Http::get("https://maps.googleapis.com/maps/api/place/textsearch/json?key={$mapKey}&query={$query}&rankBy=distance")->json()['results'];
    //             });
    //             $map->companies = $result;
    //             $map->put('companies', $result);
    //         }
    //         return $map;
    //     }
    //     return ['success' => false];
    // }

    public function jobs($industries = null)
    {
        $lastplan = $this->lastPlan();
        $userip = request()->ip();
        $useragent = request()->header('User-Agent');
        $location = $this->location();

        $data = collect();

        $client = new Indeed(config('services.indeed.id'));

        if ($industries) {
            if (is_object($industries)) {
                $industries = $industries->implode(', ');
            } elseif (is_array($industries)) {
                $industries = implode(', ', $industries);
            }
        } elseif (@$lastplan->industries) {
            $industries = $lastplan->industries->pluck('search_keyword')->implode(', ');
        }

        $l = $location->address;

        $params = array(
            "v" => "2",     // API Version
            'q' => $industries,
            'l' => $l,
            "co" => "au",      // Country Code default US
            "userip" => $userip, // user's IP Address
            "useragent" => $useragent,    // user agent
            'limit' => 6,
        );
        $response = $client->search($params);
        dd($response);
        if (!count($response['results'])) {
            if (isset($location->vicinity) && !empty($location->vicinity)) {
                $l =  str_replace(($location->vicinity), '', $location->address);
            } else {
                $l = $location->address;
            }

            $params = array(
                "v" => "2",     // API Version
                'q' => $industries,
                'l' => $l,
                "co" => "au",      // Country Code default US
                "userip" => $userip, // user's IP Address
                "useragent" => $useragent,    // user agent
                'limit' => 6,
            );
            $response = $client->search($params);
        }


        $data->location = $l;
        // $data->put('location', $l);
        $data->results = $response['results'];
        // $data->put('results', $response['results']);
        return $data;




        return $data;
    }

    public function content($industries = null, $id = null)
    {
        $latitude = $this->location()->lat;
        $longitude = $this->location()->lng;

        // return Industryunit::published()->select('id', 'title', 'banner')->with('industries')->take(32)->get();

        if ($industries) {
            $industries = is_object($industries) ? $industries->toArray() : $industries;

            $content = Industryunit::selectRaw("industryunits.id, industryunits.title, industryunits.banner, industryunit_locations.latitude, industryunit_locations.longitude, (6371000 * acos( cos( radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance", [$latitude, $longitude, $latitude])
                ->join('industryunit_locations', 'industryunits.id', '=', 'industryunit_locations.industryunit_id')
                ->where('industryunits.id', '!=', $id)
                ->whereNotNull('address')
                // ->having("distance", "<", $radius)
                ->with('industries')
                ->groupBy('id')
                ->withAnyTag($industries, 'primary')
                ->orderBy('distance')->take(8)->get();
        } else {

            $lastplan = $this->lastPlan();
            $content = Industryunit::selectRaw("industryunits.id, industryunits.title, industryunits.banner, industryunit_locations.latitude, industryunit_locations.longitude, (6371000 * acos( cos( radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance", [$latitude, $longitude, $latitude])
                ->join('industryunit_locations', 'industryunits.id', '=', 'industryunit_locations.industryunit_id')
                ->whereNotNull('address')
                // ->having("distance", "<", $radius)
                ->with('industries')
                ->groupBy('id')
                ->when(@$lastplan->industries, function ($query) use ($lastplan) {
                    return $query->whereHas('industries', function ($q) use ($lastplan) {
                        $q->whereIn('industry_id', $lastplan->industries->pluck('id'));
                    });
                })->orderBy('distance')->take(8)->get();
        }

        return $content;
    }

    public function routeNotificationForMail($notification)
    {
        // Return email address only...
        $email = ($this->email == "<EMAIL>") ? "<EMAIL>" :  $this->email;
        return $email;
    }

    /**
     * User subjects (Chosen and saved in subject selection quiz)
     */
    public function subjects()
    {
        return $this->belongsToMany(Subject::class, 'subject_user')->withPivot(['status']);
    }

    /**
     * User submitted inputs in ssq quiz
     */
    public function ssqInputs()
    {
        return $this->hasMany(SsqInput::class);
    }

    public function isUnderTertiary()
    {
        return optional($this->school?->detail?->institute_type)->value === 'tertiary';
    }

    public function menuAccess()
    {
        return Cache::tags(["schoolmenuAccess"])->rememberForever("schoolMenuAccess-" . $this->school->id, function () {
            return $this->school->menuAccess;
        });
    }

    public function hasIndustriesAccess()
    {
        // dd(Standard::where('title', '<>', 'I’ve finished high school')->count());
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

        return $this->menuAccess()
            ->where('menu', 'Industries')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Industries')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Industries')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasEMagazineAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'eMagazine')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'eMagazine')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'eMagazine')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasCareerProfilingAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {
           return $this->menuAccess()->where('menu', 'careerProfiling')->where('year_id', $this->profile->standard_id)->isEmpty();
        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'careerProfiling')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'careerProfiling')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasVideoProfilingAccess()
    {
        // if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {
        //    return $this->menuAccess()->where('menu', 'videoProfiling')->where('year_id', $this->profile->standard_id)->isEmpty();
        // } elseif ($this->isTeacher() || $this->isStaff()) {
        //     return $this->menuAccess()->where('menu', 'videoProfiling')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        // }
        // return true;

            if(config('app.env') !== 'production' || ($this->isStudent() && in_array($this->school_id, [223899,127,223204, 223410, 224204, 224295, 225410, 226877, 227672, 230837, 218111]))) {
                return true;
            }
            return false;
    }

     public function hasMyPathAccess()
    {
            if(config('app.env') !== 'production' || ($this->isStudent() && in_array($this->school_id, [127, 223204, 223410, 224204, 224295, 225410, 226877, 227672, 230837, 218111]))) {
                return true;
            }
            return false;
    }

    public function hasGamePlanAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Game Plan')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Game Plan')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Game Plan')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasProfilingAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Profiling')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Profiling')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Profiling')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasLessonsAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Lessons')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Lessons')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Lessons')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            // dd($this->profile->premium_access);
            return $this->profile->premium_access;
        }
        return true;
    }

    public function hasVweAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Virtual Work Experience')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

            if ($this->school_id && $this->organisation_id) {
                if ($this->school->campuses()->exists()) {
                    if ($this->campuses()->doesntExist()) {
                        $menu1 = true;
                    } else {
                        $menu1 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->campuses->first()->id)->where(function ($query) {
                            $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                } else {
                    $menu1 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->school_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }

                if ($this->organisation->campuses()->exists()) {
                    if ($this->orgCampuses()->doesntExist()) {
                        $menu2 = true;
                    } else {
                        $menu2 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->orgCampuses->first()->id)->where(function ($query) {
                            $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                } else {
                    $menu2 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->organisation_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu1 && $menu2) {
                    return false;
                }
            } elseif ($this->organisation_id && !$this->school_id) {
                if ($this->organisation->campuses()->exists()) {
                    if ($this->orgCampuses()->doesntExist()) {
                        $menu = true;
                    } else {
                        $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->orgCampuses->first()->id)->where(function ($query) {
                            $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                } else {
                    $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->organisation_id)->where(function ($query) {
                        $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    return false;
                }
            } elseif (!$this->organisation_id && $this->school_id) {
                if ($this->school->campuses()->exists()) {
                    if ($this->campuses()->doesntExist()) {
                        $menu = true;
                    } else {
                        $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->campuses->first()->id)->where(function ($query) {
                            $query->where('title', 'Work Experience')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                } else {
                    $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->school_id)->where(function ($query) {
                        $query->where('title', 'Virtual Work Experience')->orWhere('title', 'Work Experience Week');
                    })->exists();
                }
                if ($menu) {
                    return false;
                }
            }
        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Virtual Work Experience')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Virtual Work Experience')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            return $this->profile->premium_access;
        }
        return true;
    }

    public function hasSkillsTrainingAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {
            // return $this->menuAccess()->where('menu', 'Skills Training')->where('year_id', $this->profile->standard_id)->isEmpty();

            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Skills Training')->isEmpty();
            }

            if ($this->menuAccess()->where('menu', 'Skills Training')->where('year_id', $this->profile->standard_id)->isEmpty()) {
                if ($this->school_id && $this->organisation_id) {
                    if ($this->school->campuses()->exists()) {
                        if ($this->campuses()->doesntExist()) {
                            $menu1 = true;
                        } else {
                            $menu1 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->campuses->first()->id)->where(function ($query) {
                                $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                            })->exists();
                        }
                    } else {
                        $menu1 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->school_id)->where(function ($query) {
                            $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }

                    if ($this->organisation->campuses()->exists()) {
                        if ($this->orgCampuses()->doesntExist()) {
                            $menu2 = true;
                        } else {
                            $menu2 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->orgCampuses->first()->id)->where(function ($query) {
                                $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                            })->exists();
                        }
                    } else {
                        $menu2 = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->organisation_id)->where(function ($query) {
                            $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                    if ($menu1 && $menu2) {
                        return false;
                    }
                } elseif ($this->organisation_id && !$this->school_id) {
                    if ($this->organisation->campuses()->exists()) {
                        if ($this->orgCampuses()->doesntExist()) {
                            $menu = true;
                        } else {
                            $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->orgCampuses->first()->id)->where(function ($query) {
                                $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                            })->exists();
                        }
                    } else {
                        $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->organisation_id)->where(function ($query) {
                            $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                    if ($menu) {
                        return false;
                    }
                } elseif (!$this->organisation_id && $this->school_id) {
                    if ($this->school->campuses()->exists()) {
                        if ($this->campuses()->doesntExist()) {
                            $menu = true;
                        } else {
                            $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereCampusId($this->campuses->first()->id)->where(function ($query) {
                                $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                            })->exists();
                        }
                    } else {
                        $menu = Menu::whereYearId($this->profile->standard_id)->whereParentMenu('Work Experience Week')->whereSchoolId($this->school_id)->where(function ($query) {
                            $query->where('title', 'Skills Training')->orWhere('title', 'Work Experience Week');
                        })->exists();
                    }
                    if ($menu) {
                        return false;
                    }
                }
            }
        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Skills Training')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Skills Training')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            return $this->profile->premium_access;
        }
        return true;
    }

    public function hasWhsAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Work, Health & Safety')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Work, Health & Safety')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Work, Health & Safety')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            return false;
        }
        return true;
    }

    public function hasJobFinderAccess()
    {
        // return false;  // jobfinder not working
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Job Finder')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Job Finder')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Job Finder')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasScholarshipFinderAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Scholarship Finder')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Scholarship Finder')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Scholarship Finder')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasResumeBuilderAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Resume Builder')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Resume Builder')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Resume Builder')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasCourseFinderAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Course Finder')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Course Finder')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Course Finder')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasEPortfolioAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'ePortfolio')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'ePortfolio')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'ePortfolio')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            return $this->profile->premium_access;
        }
        return true;
    }

    public function hasSubjectSelectionsAccess()
    {
        if (!$this->school_id && !$this->organisation_id) {
            return false;
        } elseif ($this->isStudent()) {

            return $this->menuAccess()
            ->where('menu', 'Subject Selections')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Subject Selections')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Subject Selections')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        } elseif ($this->isParent()) {
            return false;
        }
        return true;
    }

    public function hasNoticeboardAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Noticeboard')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Noticeboard')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Noticeboard')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function hasHelpCentreAccess()
    {
        if ($this->isStudent() && !$this->isChild() && !$this->isIndividual()) {

            return $this->menuAccess()
            ->where('menu', 'Help Centre')
            ->when(! $this->isUnderTertiary(), function ($query) {
                $query->where('year_id', $this->profile->standard_id);
            })
            ->isEmpty();

        } elseif ($this->isTeacher() || $this->isStaff()) {
            if($this->isUnderTertiary()) {
                return $this->menuAccess()->where('menu', 'Help Centre')->isEmpty();
            }
            return $this->menuAccess()->where('menu', 'Help Centre')->count() < Standard::where('title', '<>', 'I’ve finished high school')->count();
        }
        return true;
    }

    public function getAccountStatus()
    {
        $query = $this->newQuery(); // Start a new query

        // Check if the user is admin
        if ($this->isAdmin()) {
            // Check for active status
            if ($this->hasActiveLicense()) {
                return 'active';
            } else {
                return 'inactive';
            }
        } else {
            // For non-admin users
            if ($this->hasActiveProfile()) {
                return 'active';
            } else {
                return 'inactive';
            }
        }
    }

    private function hasActiveLicense()
    {
        return $this->license_end_date > now() || $this->accountcreated == 0;
    }

    private function hasActiveProfile()
    {
        return $this->profile()->where(function ($query) {
            $query->where('standard_id', '<>', 7)
                ->whereRemoved(false);
        })->orWhere(function ($query) {
            $query->where('standard_id', null)
                ->where('accountcreated', 0);
        })->exists();
    }

    public function userSelectedOccupations()
    {
        return $this->belongsToMany(AnzscoOccupation::class, 'user_selected_occupations');
    }

    public function profilingPostResponses()
    {
        return $this->hasMany(ProfilingPostResponse::class, 'user_id', 'id');
    }

    public function getVideoProfilingStatusAttribute()
    {
        $viewLimit =  config('profilling.view_limit_job_suggestion');

        $interactionCount = ProfilingPostResponse::where('user_id', $this->id)
            ->where('timespent', '>', 0)
            ->count();

        $hasSelectedJobs = UserSelectedOccupation::where('user_id', $this->id)->exists();

        if ($interactionCount >= $viewLimit && $hasSelectedJobs) {
            return 'Completed';
        } elseif ($interactionCount > 0) {
            return 'In Progress';
        }

        return 'Incomplete';
    }
}
