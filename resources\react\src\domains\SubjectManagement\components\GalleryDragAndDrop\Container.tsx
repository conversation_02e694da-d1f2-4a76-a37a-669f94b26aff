import update from 'immutability-helper';
import { memo, useCallback, useEffect, useState } from 'react';
import { useDrop } from 'react-dnd';
import { Card } from './Card';
import Edit from '@/assets/icons/subjectSelection/Edit';
import Trash from '@/assets/icons/subjectSelection/Trash';

interface Props {
    items: any;
    withCheckbox?: boolean;
    onItemOrderChange?: (items: any) => void;
    onItemClick?: (item: any) => void;
    onCheckboxClick?: (item: any) => void;
    onEdit?: (item: any) => void;
    onSelect?: (item: any) => void;
    onDelete?: (item: any) => void;
    selectedItemIds?: number[];
}

export const Container = memo(function Container({
    items,
    onItemOrderChange,
    onSelect,
    onEdit,
    onDelete,
    selectedItemIds,
}: Props) {
    const [cards, setCards] = useState(items);

    useEffect(() => {
        setCards(items);
    }, [items]);

    useEffect(() => {
        onItemOrderChange?.(cards);
    }, [cards]);

    const findCard = useCallback(
        (id: any) => {
            const card = cards.filter((c: any) => `${c.id}` === id)[0];
            return {
                card,
                index: cards.indexOf(card),
            };
        },
        [cards],
    );

    const moveCard = useCallback(
        (id: any, atIndex: any) => {
            const { card, index } = findCard(id);
            setCards(
                update(cards, {
                    $splice: [
                        [index, 1],
                        [atIndex, 0, card],
                    ],
                }),
            );
        },
        [findCard, cards, setCards],
    );

    const [, drop] = useDrop(() => ({ accept: 'card' }));

    return (
        <div
            ref={drop}
            style={{
                width: 400,
            }}
        >
            {cards.map((card: any) => (
                <div className='d-flex mb-3 align-items-center' key={card.id}>
                    <Card
                        id={`${card.id}`}
                        onClick={() => onSelect?.(card)}
                        selected={selectedItemIds?.includes(card.id)}
                        caption={card.caption}
                        moveCard={moveCard}
                        findCard={findCard}
                    />
                    <div className='ms-4'>
                        {onEdit && (
                            <span className='cursor-pointer me-2' onClick={() => onEdit?.(card)}>
                                <Edit />
                            </span>
                        )}
                        {onDelete && (
                            <span className='cursor-pointer me-2' onClick={() => onDelete?.(card)}>
                                <Trash />
                            </span>
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
});
