<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
   <meta charset="utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1.0, maximum-scale = 1.0, user-scalable = no">
   <meta name="csrf-token" value="{{ csrf_token() }}"/>
   <title>{{ config('app.name', 'The Careers Department') }}</title>
   <link href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700|Roboto:300,400,500,600,700|Material+Icons" rel="stylesheet">
</head>
<body style="--kt-toolbar-height:55px;--kt-toolbar-height-tablet-and-mobile:55px" class="header-fixed header-tablet-and-mobile-fixed toolbar-enabled toolbar-fixed toolbar-tablet-and-mobile-fixed aside-enabled aside-fixed">
   <div id="app" class="d-flex flex-column flex-root">
   </div>
   @php
      $path = mix('js/vapp.js');
      $vappTimestamp = filemtime(public_path('js/vapp.js'));

      $hasQuery = parse_url($path, PHP_URL_QUERY);
      $separator = $hasQuery ? '&' : '?';
   @endphp
   {{-- <script src="{{ mix('js/vapp.js') }}&v={{ filemtime(public_path('js/vapp.js')) }}" type="text/javascript"></script> --}}
   <script src="{{ $path }}{!! $separator !!}v={{ $vappTimestamp }}" type="text/javascript"></script>
   <script>
      window.flash = @json(session('success'));
   </script>
  

</body>
</html>
