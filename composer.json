{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": "^7.3|^8.0", "algolia/algoliasearch-client-php": "^2.6", "artesaos/seotools": "^0.22.0", "aws/aws-sdk-php-laravel": "^3.7", "barryvdh/laravel-debugbar": "^3.2", "barryvdh/laravel-dompdf": "^1.0", "bepsvpt/secure-headers": "^7.2.0 ", "cviebrock/eloquent-sluggable": "^9.0", "diglactic/laravel-breadcrumbs": "^7.1", "digu087/tcd-tagging": "^1.0", "doctrine/dbal": "^3.9", "genealabs/laravel-model-caching": "*", "gmopx/laravel-owm": "^0.1.3", "google/apiclient": "^2.4", "guzzlehttp/guzzle": "^7.0.1", "intercom/intercom-php": "^4.5", "jenssegers/agent": "^2.6", "laravel/cashier": "^13.0", "laravel/forge-sdk": "^3.13", "laravel/framework": "^9.0", "laravel/horizon": "*", "laravel/octane": "^1.2", "laravel/scout": "^9.0", "laravel/socialite": "^5.0", "laravel/telescope": "^4.0", "laravel/tinker": "^2.3", "laravel/ui": "^3.0", "laravelcollective/html": "^6.1", "lavary/laravel-menu": "^1.7", "league/color-extractor": "^0.4.0", "league/flysystem-aws-s3-v3": "^3.0", "maatwebsite/excel": "^3.1.37", "maxbanton/cwh": "^2.0", "overtrue/laravel-favorite": "^5.0", "paquettg/php-html-parser": "^2.2", "phpoffice/phpword": "^0.18.0", "proengsoft/laravel-jsvalidation": "^4.0", "pusher/pusher-php-server": "^7.0", "renoki-co/laravel-aws-webhooks": "^5.0", "spatie/laravel-ignition": "^1.0", "spatie/laravel-newsletter": "^4.8", "spatie/pdf-to-image": "^1.2", "spiral/roadrunner": "^2.8", "symfony/http-client": "^6.1", "symfony/mailgun-mailer": "^6.1", "symfony/options-resolver": "^6.4", "yajra/laravel-datatables-oracle": "^9.9"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.12", "fakerphp/faker": "^1.21", "masterro/laravel-mail-viewer": "^2.0", "mockery/mockery": "0.9.*", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.0", "spatie/laravel-activitylog": "^4.4"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["tests/utilities/functions.php", "app/Helpers/functions.php"]}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "php artisan optimize"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "php artisan optimize", "php artisan vendor:publish --provider=\"Proengsoft\\JsValidation\\JsValidationServiceProvider\" --tag=public --force", "@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"php-http/discovery": true}}, "removedforlater": {"thujohn/twitter": "^2.2", "spatie/laravel-backup": "^6.8"}, "removedforlater-requiredev": {"facade/ignition": "^2.0"}, "checkupgradeguide": {"cashier": "", "Sluggable": "", "seotools": "", "proengsoft/laravel-jsvalidation": "^4.0"}}