import { SubjectGalleryItemResponse } from '@/domains/SubjectManagement/models/Subjects.service.api.model';
import { SubjectSelectionQuizState } from './SubjectSelection.model';

export interface SubjectSelectionQuizResponse {
    interests: {
        featured_image: string;
        featured_image_url: string;
        id: number;
        name: string;
    }[];
    jobs: {
        description: string;
        id: number;
        link: string;
        name: string;
    }[];
    skills: {
        id: number;
        name: string;
    }[];
    study_habits: {
        id: number;
        name: string;
    }[];
    languages: {
        id: number;
        name: string;
    }[];
    electives: {
        id: number;
        title: string;
    }[];
}

interface RelatedContent {
    id: number;
    banner: string;
    banner_url: string;
    banner_video: string;
    description: string;
    enquire_form: number;
    estimated_time: boolean;
    inner_banner: string;
    inner_banner_url: string;
    industry_id: number | null;
    pivot: { subject_id: number; industryunit_id: number };
    publish: string;
    sortorder: number;
    title: string;
    type: string;
}

export interface SubjectsResponse {
    id: number;
    country: string | null;
    state: string | null;
    gallery: SubjectGalleryItemResponse[];
    subject_type: {
        id: number;
        name: string;
        state_id: number;
    };
    course_type: {
        id: number;
        name: string;
        state_id: number;
    } | null;
    title: string;
    area: {
        id: number;
        name: string;
        created_at: Date | null;
        updated_at: Date | null;
    } | null;
    number: string | number;
    standards: {
        id: number;
        title: string;
    }[];
    units: string | null;
    major_work: number;
    description: string | null;
    related_content: RelatedContent[];
    pre_requisites: SubjectsResponse[];
    related_subjects: SubjectsResponse[];
    featured_image_url: string | null;
    description_url: string | null;
    score: number;
    percentage: number;
    match: string;
}

export interface SubjectPreferencesRequestParams {
    subjects: number[];
    status?: SubjectPreferenceStatus;
}

export enum SubjectPreferenceStatus {
    IN_PROGRESS = 'in_progress',
    SAVED = 'saved',
}

export interface SubjectPreferencesResponse {
    status: SubjectPreferenceStatus;
    subjects: SubjectsResponse[];
    rules_progress: number[];
}

export type SubjectSubmissionsResponse = SubjectSelectionQuizState;
