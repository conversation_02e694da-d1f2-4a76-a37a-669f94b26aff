import { useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useUserData } from '@/domains/Auth/consumers/useAuthApiConsumer';
import DashboardLayout from '@/components/layouts/DashboardLayout/DashboardLayout';
import { Roles } from '@/domains/Auth/models/Auth.model';
import { SUBJECTS_SELECTION_STUDENT_LANDING, SUBJECTS_SELECTION_TEACHER_LANDING } from '@/router/Router.constants';
import SubjectSelectionLandingLayout from '@/components/layouts/SubjectSelectionLandingLayout/SubjectSelectionLandingLayout';
import {
    useQuizAnswers,
    useQuizResults,
    useSubjectPreferencesSelection,
    useSubjects,
} from '@/domains/SubjectSelection/consumers/useSubjectSelectionQuizApiConsumer';
import { SubjectManagementServiceProvider } from '@/domains/SubjectManagement/SubjectManagement';
import { useStatistics, useStudents } from '@/domains/SubjectManagement/consumers/useSubjectManagementApiConsumer';
import { useSchoolRules } from '@/domains/SubjectManagement/consumers/useSchoolsApiConsumer';
import { useStudentRulesProgress } from '@/domains/SubjectSelection/consumers/useStudentsApiConsumer';

export default function RootView() {
    const { isLoading, data: user } = useUserData();

    if (isLoading || !user) {
        return (
            <DashboardLayout>
                <SubjectSelectionLandingLayout>
                    <div className='landing-view d-flex align-items-center justify-content-center h-100'>
                        <div className='col-lg-3 '>
                            <div className='text-center'>
                                <div className='spinner-border text-primary' role='status' />
                                <span className='text-dark d-block'>Loading subject selection module</span>
                            </div>
                        </div>
                        <div className='col-lg-8'></div>
                    </div>
                </SubjectSelectionLandingLayout>
            </DashboardLayout>
        );
    }

    if (user.role === Roles.Student) {
        return <RootStudentView />;
    }

    if (user.role === Roles.Teacher.toLocaleLowerCase()) {
        return <RootTeacherView />;
    }

    return (
        <DashboardLayout>
            <SubjectSelectionLandingLayout>
                <div className='landing-view d-flex align-items-center justify-content-center h-100'>
                    <div className='col-lg-3 '>
                        <div className='text-center'>
                            <span>Something went wrong! Please contact our support!</span>
                        </div>
                    </div>
                    <div className='col-lg-8'></div>
                </div>
            </SubjectSelectionLandingLayout>
        </DashboardLayout>
    );
}

function RootStudentView() {
    const navigate = useNavigate();
    const location = useLocation();
    const { isLoading: isUserLoading, data: user } = useUserData();
    const { isLoading: isSubjectLoading } = useSubjects();
    const { isLoading: isQuizAnswersLoading } = useQuizAnswers();
    const { isLoading: isSubjectPreferencesLoading } = useSubjectPreferencesSelection();
    const { isLoading: isQuizResultsLoading, refetch: fetchResults } = useQuizResults();
    useSchoolRules({ schoolId: user?.schoolId });
    user && useStudentRulesProgress(user.id);

    useEffect(() => {
        fetchResults();
    }, []);

    useEffect(() => {
        if (!isUserLoading && location.pathname === '/subjects-selection') {
            navigate(SUBJECTS_SELECTION_STUDENT_LANDING);
        }
    }, [isUserLoading]);

    if (
        isUserLoading &&
        isSubjectLoading &&
        isQuizAnswersLoading &&
        isSubjectPreferencesLoading &&
        isQuizResultsLoading
    ) {
        return (
            <DashboardLayout>
                <SubjectSelectionLandingLayout>
                    <div className='landing-view d-flex align-items-center justify-content-center h-100'>
                        <div className='col-lg-3 '>
                            <div className='text-center'>
                                <div className='spinner-border text-primary' role='status' />
                                <span className='text-dark d-block'>Loading subject selection module</span>
                            </div>
                        </div>
                        <div className='col-lg-8'></div>
                    </div>
                </SubjectSelectionLandingLayout>
            </DashboardLayout>
        );
    }

    return (
        <DashboardLayout>
            <Outlet />
        </DashboardLayout>
    );
}

function RootTeacherView() {
    const navigate = useNavigate();
    const { isLoading, data: user } = useUserData();
    const { isLoading: isLoadingStatistics } = useStatistics();
    const { isLoading: isLoadingUsers } = useStudents({});

    user && useSchoolRules({ schoolId: user.schoolId });

    useEffect(() => {
        if (!isLoading && location.pathname === '/subjects-selection') {
            navigate(SUBJECTS_SELECTION_TEACHER_LANDING);
        }
    }, [isLoading]);

    if (isLoading && isLoadingStatistics && isLoadingUsers) {
        return (
            <SubjectManagementServiceProvider>
                <DashboardLayout>
                    <SubjectSelectionLandingLayout>
                        <div className='landing-view d-flex align-items-center justify-content-center h-100'>
                            <div className='col-lg-3 '>
                                <div className='text-center'>
                                    <div className='spinner-border text-primary' role='status' />
                                    <span className='text-dark d-block'>Loading subject selection module</span>
                                </div>
                            </div>
                            <div className='col-lg-8'></div>
                        </div>
                    </SubjectSelectionLandingLayout>
                </DashboardLayout>
            </SubjectManagementServiceProvider>
        );
    }

    return (
        <DashboardLayout>
            <Outlet />
        </DashboardLayout>
    );
}
