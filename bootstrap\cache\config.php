<?php return array (
  'activitylog' => 
  array (
    'enabled' => true,
    'delete_records_older_than_days' => 365,
    'default_log_name' => 'default',
    'default_auth_driver' => NULL,
    'subject_returns_soft_deleted_models' => false,
    'activity_model' => 'Spatie\\Activitylog\\Models\\Activity',
    'table_name' => 'activity_log',
    'database_connection' => NULL,
  ),
  'admin' => 
  array (
    'email' => '<EMAIL>',
    'name' => 'Sarah',
  ),
  'app' => 
  array (
    'name' => 'The Careers Department Local',
    'hostname' => 'TCDAPP',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://school.test',
    'timezone' => 'Australia/Sydney',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'key' => 'base64:waZKVCwE8Nze9glCEux9plmEYOOJfYaAYid1izHE+xg=',
    'cipher' => 'AES-256-CBC',
    'log' => 'single',
    'log_level' => 'debug',
    'assets_cache_busting' => false,
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Laravel\\Tinker\\TinkerServiceProvider',
      23 => 'App\\Providers\\AppServiceProvider',
      24 => 'App\\Providers\\AuthServiceProvider',
      25 => 'App\\Providers\\BroadcastServiceProvider',
      26 => 'App\\Providers\\EventServiceProvider',
      27 => 'App\\Providers\\HorizonServiceProvider',
      28 => 'App\\Providers\\CanvasServiceProvider',
      29 => 'App\\Providers\\RouteServiceProvider',
      30 => 'App\\Providers\\TelescopeServiceProvider',
      31 => 'Proengsoft\\JsValidation\\JsValidationServiceProvider',
      32 => 'Lavary\\Menu\\ServiceProvider',
      33 => 'Gmopx\\LaravelOWM\\LaravelOWMServiceProvider',
      34 => 'Laravel\\Cashier\\CashierServiceProvider',
      35 => 'Jenssegers\\Agent\\AgentServiceProvider',
      36 => 'Barryvdh\\DomPDF\\ServiceProvider',
      37 => 'Conner\\Tagging\\Providers\\TaggingServiceProvider',
      38 => 'Spatie\\Newsletter\\NewsletterServiceProvider',
      39 => 'App\\Providers\\BigMarkerServiceProvider',
      40 => 'App\\Providers\\IntercomServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Redis' => 'Illuminate\\Support\\Facades\\Redis',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'JsValidator' => 'Proengsoft\\JsValidation\\Facades\\JsValidatorFacade',
      'Menu' => 'Lavary\\Menu\\Facade',
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
      'Agent' => 'Jenssegers\\Agent\\Facades\\Agent',
      'PDF' => 'Barryvdh\\DomPDF\\Facade',
      'Newsletter' => 'Spatie\\Newsletter\\NewsletterFacade',
      'Str' => 'Illuminate\\Support\\Str',
      'Arr' => 'Illuminate\\Support\\Arr',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'token',
        'provider' => 'users',
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 4320,
      ),
    ),
    'inactiveseconds' => 70,
  ),
  'backup' => 
  array (
    'backup' => 
    array (
      'name' => 'The Careers Department Local',
      'source' => 
      array (
        'files' => 
        array (
          'include' => 
          array (
          ),
          'exclude' => 
          array (
            0 => 'C:\\dev\\PhpProjects\\the-careers-department\\vendor',
            1 => 'C:\\dev\\PhpProjects\\the-careers-department\\node_modules',
          ),
          'followLinks' => false,
        ),
        'databases' => 
        array (
          0 => 'mysql',
        ),
      ),
      'database_dump_compressor' => NULL,
      'destination' => 
      array (
        'filename_prefix' => '',
        'disks' => 
        array (
          0 => 's3',
        ),
      ),
      'temporary_directory' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\app/backup-temp',
    ),
    'notifications' => 
    array (
      'notifications' => 
      array (
        'Spatie\\Backup\\Notifications\\Notifications\\BackupHasFailed' => 
        array (
          0 => 'mail',
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\UnhealthyBackupWasFound' => 
        array (
          0 => 'mail',
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\CleanupHasFailed' => 
        array (
          0 => 'mail',
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\BackupWasSuccessful' => 
        array (
          0 => 'mail',
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\HealthyBackupWasFound' => 
        array (
          0 => 'mail',
        ),
        'Spatie\\Backup\\Notifications\\Notifications\\CleanupWasSuccessful' => 
        array (
          0 => 'mail',
        ),
      ),
      'notifiable' => 'Spatie\\Backup\\Notifications\\Notifiable',
      'mail' => 
      array (
        'to' => '<EMAIL>',
      ),
      'slack' => 
      array (
        'webhook_url' => '',
        'channel' => NULL,
        'username' => NULL,
        'icon' => NULL,
      ),
    ),
    'monitorBackups' => 
    array (
      0 => 
      array (
        'name' => 'The Careers Department Local',
        'disks' => 
        array (
          0 => 'local',
        ),
        'newestBackupsShouldNotBeOlderThanDays' => 1,
        'storageUsedMayNotBeHigherThanMegabytes' => 5000,
      ),
    ),
    'cleanup' => 
    array (
      'strategy' => 'Spatie\\Backup\\Tasks\\Cleanup\\Strategies\\DefaultStrategy',
      'defaultStrategy' => 
      array (
        'keepAllBackupsForDays' => 7,
        'keepDailyBackupsForDays' => 7,
        'keepWeeklyBackupsForWeeks' => 5,
        'keepMonthlyBackupsForMonths' => 4,
        'keepYearlyBackupsForYears' => 2,
        'deleteOldestBackupsWhenUsingMoreMegabytesThan' => 5000,
      ),
    ),
  ),
  'bigmarker' => 
  array (
    'api_key' => 'b55bf5778e26edba7efc',
    'baseurl' => 'https://www.bigmarker.com/api/',
    'api_version' => 'v1',
  ),
  'breadcrumbs' => 
  array (
    'view' => 'breadcrumbs::bootstrap4',
    'files' => 'C:\\dev\\PhpProjects\\the-careers-department\\routes/breadcrumbs.php',
    'unnamed-route-exception' => true,
    'missing-route-bound-breadcrumb-exception' => true,
    'invalid-named-breadcrumb-exception' => true,
    'manager-class' => 'Diglactic\\Breadcrumbs\\Manager',
    'generator-class' => 'Diglactic\\Breadcrumbs\\Generator',
  ),
  'breadcrumbs (copy)' => 
  array (
    'view' => 'partials.breadcrumbs',
    'files' => 'C:\\dev\\PhpProjects\\the-careers-department\\routes/breadcrumbs.php',
    'unnamed-route-exception' => true,
    'missing-route-bound-breadcrumb-exception' => true,
    'invalid-named-breadcrumb-exception' => true,
    'manager-class' => 'Diglactic\\Breadcrumbs\\Manager',
    'generator-class' => 'Diglactic\\Breadcrumbs\\Generator',
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '5a24ca9f7b64a419f008',
        'secret' => '40ae2a9c9f029b7e2eb511',
        'app_id' => '1013007',
        'options' => 
        array (
          'host' => '127.0.0.1',
          'port' => 6001,
          'scheme' => 'http',
          'encrypted' => true,
          'useTLS' => false,
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'redis',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'client' => 'phpredis',
      ),
    ),
    'prefix' => 'the_careers_department_local_cache',
  ),
  'canvas' => 
  array (
    'middleware' => 
    array (
      0 => 'web',
      1 => 'auth',
      2 => 'admin',
    ),
    'storage_disk' => 'local',
    'storage_path' => 'public/canvas',
    'unsplash' => 
    array (
      'access_key' => NULL,
    ),
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'database' => 'tcd',
        'prefix' => '',
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'host' => 'localhost',
        'port' => '3306',
        'database' => 'tcd',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
        ),
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'host' => 'localhost',
        'port' => '3306',
        'database' => 'tcd',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'search_path' => 'public',
        'sslmode' => 'prefer',
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'phpredis',
      'options' => 
      array (
        'cluster' => 'redis',
        'prefix' => 'the_careers_department_local_database_',
      ),
      'default' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
      ),
      'cache' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '1',
      ),
      'horizon' => 
      array (
        'url' => NULL,
        'host' => '127.0.0.1',
        'username' => NULL,
        'password' => NULL,
        'port' => '6379',
        'database' => '0',
        'options' => 
        array (
          'prefix' => 'the_careers_department_local_horizon:',
        ),
      ),
    ),
  ),
  'db-snapshots' => 
  array (
    'disk' => 'spaces',
    'default_connection' => NULL,
    'temporary_directory_path' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\app/laravel-db-snapshots/temp',
    'compress' => true,
  ),
  'debugbar' => 
  array (
    'enabled' => NULL,
    'hide_empty_tabs' => true,
    'except' => 
    array (
      0 => 'telescope*',
      1 => 'horizon*',
    ),
    'storage' => 
    array (
      'enabled' => true,
      'driver' => 'file',
      'path' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\debugbar',
      'connection' => NULL,
      'provider' => '',
      'hostname' => '127.0.0.1',
      'port' => 2304,
    ),
    'editor' => 'vscode',
    'remote_sites_path' => '',
    'local_sites_path' => '',
    'include_vendors' => true,
    'capture_ajax' => true,
    'add_ajax_timing' => false,
    'ajax_handler_auto_show' => true,
    'ajax_handler_enable_tab' => true,
    'defer_datasets' => false,
    'error_handler' => false,
    'clockwork' => false,
    'collectors' => 
    array (
      'phpinfo' => true,
      'messages' => true,
      'time' => true,
      'memory' => true,
      'exceptions' => true,
      'log' => true,
      'db' => true,
      'views' => true,
      'route' => true,
      'auth' => false,
      'gate' => true,
      'session' => true,
      'symfony_request' => true,
      'mail' => true,
      'laravel' => false,
      'events' => false,
      'default_request' => false,
      'logs' => false,
      'files' => false,
      'config' => false,
      'cache' => false,
      'models' => true,
      'livewire' => true,
    ),
    'options' => 
    array (
      'auth' => 
      array (
        'show_name' => true,
      ),
      'db' => 
      array (
        'with_params' => true,
        'backtrace' => true,
        'backtrace_exclude_paths' => 
        array (
        ),
        'timeline' => false,
        'duration_background' => true,
        'explain' => 
        array (
          'enabled' => false,
          'types' => 
          array (
            0 => 'SELECT',
          ),
        ),
        'hints' => false,
        'show_copy' => false,
      ),
      'mail' => 
      array (
        'full_log' => false,
      ),
      'views' => 
      array (
        'timeline' => false,
        'data' => false,
      ),
      'route' => 
      array (
        'label' => true,
      ),
      'logs' => 
      array (
        'file' => NULL,
      ),
      'cache' => 
      array (
        'values' => true,
      ),
    ),
    'inject' => true,
    'route_prefix' => '_debugbar',
    'route_middleware' => 
    array (
    ),
    'route_domain' => NULL,
    'theme' => 'auto',
    'debug_backtrace_limit' => 50,
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'orientation' => 'portrait',
    'convert_entities' => true,
    'defines' => 
    array (
      'font_dir' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\fonts/',
      'font_cache' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\fonts/',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'C:\\dev\\PhpProjects\\the-careers-department',
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => false,
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'strict_null_comparison' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
        'output_encoding' => '',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'ignore_empty' => false,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => NULL,
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'UTF-8',
      ),
      'properties' => 
      array (
        'creator' => '',
        'lastModifiedBy' => '',
        'title' => '',
        'description' => '',
        'subject' => '',
        'keywords' => '',
        'category' => '',
        'manager' => '',
        'company' => '',
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
    ),
    'transactions' => 
    array (
      'handler' => 'db',
      'db' => 
      array (
        'connection' => NULL,
      ),
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'remote_disk' => NULL,
      'remote_prefix' => NULL,
      'force_resync_remote' => true,
    ),
  ),
  'favorite' => 
  array (
    'uuids' => false,
    'user_foreign_key' => 'user_id',
    'favorites_table' => 'favorites',
    'favorite_model' => 'Overtrue\\LaravelFavorite\\Favorite',
    'favoriter_model' => 'App\\User',
  ),
  'feeds' => 
  array (
    'cache.location' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage/framework/cache',
    'cache.life' => 3600,
    'cache.disabled' => false,
    'ssl_check.disabled' => false,
    'strip_html_tags.disabled' => false,
    'strip_html_tags.tags' => 
    array (
      0 => 'base',
      1 => 'blink',
      2 => 'body',
      3 => 'doctype',
      4 => 'embed',
      5 => 'font',
      6 => 'form',
      7 => 'frame',
      8 => 'frameset',
      9 => 'html',
      10 => 'iframe',
      11 => 'input',
      12 => 'marquee',
      13 => 'meta',
      14 => 'noscript',
      15 => 'object',
      16 => 'param',
      17 => 'script',
      18 => 'style',
    ),
    'strip_attribute.disabled' => false,
    'strip_attributes.tags' => 
    array (
      0 => 'bgsound',
      1 => 'class',
      2 => 'expr',
      3 => 'id',
      4 => 'style',
      5 => 'onclick',
      6 => 'onerror',
      7 => 'onfinish',
      8 => 'onmouseover',
      9 => 'onmouseout',
      10 => 'onfocus',
      11 => 'onblur',
      12 => 'lowsrc',
      13 => 'dynsrc',
    ),
    'curl.options' => NULL,
  ),
  'filesystems' => 
  array (
    'default' => 's3',
    'cloud' => 's3',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\app/public',
        'url' => 'http://school.test/storage',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '********************',
        'secret' => 'oso1e3eE/2svaw5Cftzdc6UCUtpgilR+oWbGYjVX',
        'region' => 'ap-southeast-2',
        'bucket' => 'tcd-staging',
        'visibility' => 'public',
        'options' => 
        array (
          'CacheControl' => 'max-age=315360000, no-transform, public',
        ),
      ),
      'spaces' => 
      array (
        'driver' => 's3',
        'key' => 'SHWZSTWDBTPD4N37G4VG',
        'secret' => 'ZMeUYkswGUZ98VxK4UJbtbYgHe00xsCh5fTMOsUaWKU',
        'endpoint' => 'https://sgp1.digitaloceanspaces.com',
        'region' => 'sgp1',
        'bucket' => 'thecareersdepartment',
      ),
      'efs' => 
      array (
        'driver' => 'local',
        'root' => '/mnt/efs',
      ),
    ),
  ),
  'flare' => 
  array (
    'key' => NULL,
    'flare_middleware' => 
    array (
      0 => 'Spatie\\FlareClient\\FlareMiddleware\\RemoveRequestIp',
      1 => 'Spatie\\FlareClient\\FlareMiddleware\\AddGitInformation',
      2 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddNotifierName',
      3 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddEnvironmentInformation',
      4 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddExceptionInformation',
      5 => 'Spatie\\LaravelIgnition\\FlareMiddleware\\AddDumps',
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs' => 
      array (
        'maximum_number_of_collected_logs' => 200,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries' => 
      array (
        'maximum_number_of_collected_queries' => 200,
        'report_query_bindings' => true,
      ),
      'Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs' => 
      array (
        'max_chained_job_reporting_depth' => 5,
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields' => 
      array (
        'censor_fields' => 
        array (
          0 => 'password',
        ),
      ),
      'Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders' => 
      array (
        'headers' => 
        array (
          0 => 'API-KEY',
        ),
      ),
    ),
    'send_logs_as_events' => true,
  ),
  'horizon' => 
  array (
    'domain' => NULL,
    'path' => 'horizon',
    'use' => 'default',
    'prefix' => 'the_careers_department_local_horizon:',
    'middleware' => 
    array (
      0 => 'web',
    ),
    'waits' => 
    array (
      'redis:default' => 60,
    ),
    'trim' => 
    array (
      'recent' => 60,
      'pending' => 60,
      'completed' => 60,
      'recent_failed' => 10080,
      'failed' => 10080,
      'monitored' => 10080,
    ),
    'silenced' => 
    array (
    ),
    'metrics' => 
    array (
      'trim_snapshots' => 
      array (
        'job' => 24,
        'queue' => 24,
      ),
    ),
    'fast_termination' => false,
    'memory_limit' => 128,
    'defaults' => 
    array (
      'supervisor-1' => 
      array (
        'connection' => 'redis',
        'queue' => 
        array (
          0 => 'default',
          1 => 'syncmailchimp',
          2 => 'syncintercom',
        ),
        'balance' => 'auto',
        'autoScalingStrategy' => 'time',
        'maxProcesses' => 1,
        'maxTime' => 0,
        'maxJobs' => 0,
        'memory' => 128,
        'tries' => 1,
        'timeout' => 60,
        'nice' => 0,
        'balanceMaxShift' => 1,
        'balanceCooldown' => 3,
      ),
    ),
    'environments' => 
    array (
      'production' => 
      array (
        'supervisor-1' => 
        array (
          'memory' => 256,
          'maxProcesses' => 20,
          'tries' => 1,
          'timeout' => 60,
          'retry_after' => 40,
          'balanceMaxShift' => 2,
          'balanceCooldown' => 3,
          'force' => true,
        ),
      ),
      'local' => 
      array (
        'supervisor-1' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'default',
          ),
          'balance' => 'simple',
          'processes' => 3,
          'tries' => 1,
          'timeout' => 60,
        ),
      ),
    ),
  ),
  'ignition' => 
  array (
    'editor' => 'vscode',
    'theme' => 'auto',
    'enable_share_button' => true,
    'register_commands' => false,
    'solution_providers' => 
    array (
      0 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\BadMethodCallSolutionProvider',
      1 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\MergeConflictSolutionProvider',
      2 => 'Spatie\\Ignition\\Solutions\\SolutionProviders\\UndefinedPropertySolutionProvider',
      3 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\IncorrectValetDbCredentialsSolutionProvider',
      4 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingAppKeySolutionProvider',
      5 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\DefaultDbNameSolutionProvider',
      6 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\TableNotFoundSolutionProvider',
      7 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingImportSolutionProvider',
      8 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\InvalidRouteActionSolutionProvider',
      9 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\ViewNotFoundSolutionProvider',
      10 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\RunningLaravelDuskInProductionProvider',
      11 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingColumnSolutionProvider',
      12 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UnknownValidationSolutionProvider',
      13 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingMixManifestSolutionProvider',
      14 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\MissingLivewireComponentSolutionProvider',
      15 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\UndefinedViewVariableSolutionProvider',
      16 => 'Spatie\\LaravelIgnition\\Solutions\\SolutionProviders\\GenericLaravelExceptionSolutionProvider',
    ),
    'ignored_solution_providers' => 
    array (
    ),
    'enable_runnable_solutions' => true,
    'remote_sites_path' => 'C:\\dev\\PhpProjects\\the-careers-department',
    'local_sites_path' => '',
    'housekeeping_endpoint_prefix' => '_ignition',
    'settings_file_path' => '',
    'recorders' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\Recorders\\DumpRecorder\\DumpRecorder',
      1 => 'Spatie\\LaravelIgnition\\Recorders\\JobRecorder\\JobRecorder',
      2 => 'Spatie\\LaravelIgnition\\Recorders\\LogRecorder\\LogRecorder',
      3 => 'Spatie\\LaravelIgnition\\Recorders\\QueryRecorder\\QueryRecorder',
    ),
  ),
  'imap' => 
  array (
    'default' => 'default',
    'accounts' => 
    array (
      'default' => 
      array (
        'host' => 'localhost',
        'port' => 993,
        'encryption' => 'ssl',
        'validate_cert' => true,
        'username' => '<EMAIL>',
        'password' => '',
      ),
    ),
    'options' => 
    array (
      'delimiter' => '/',
      'fetch_body' => true,
      'fetch_attachment' => true,
      'open' => 
      array (
      ),
    ),
  ),
  'intercom' => 
  array (
    'app_id' => 'dzsnh9bk',
    'access_token' => 'dG9rOmQ2OTlkNTY1XzZhNTZfNDQ3Y19iYmIyXzAxNWQyNjczMzQ5ZjoxOjA6YXAtc291dGhlYXN0LTI=',
    'intercom_version' => '2.11',
    'admin' => 
    array (
      'email' => '<EMAIL>',
      'id' => '*********',
    ),
    'test_user_ids' => 
    array (
    ),
  ),
  'jsvalidation' => 
  array (
    'view' => 'jsvalidation::bootstrap',
    'form_selector' => 'form',
    'focus_on_error' => false,
    'duration_animate' => 1000,
    'disable_remote_validation' => false,
    'remote_validation_field' => '_jsvalidation',
    'escape' => true,
    'ignore' => ':hidden, [contenteditable=\'true\']',
  ),
  'laravel-menu' => 
  array (
    'settings' => 
    array (
      'default' => 
      array (
        'auto_activate' => true,
        'activate_parents' => true,
        'active_class' => 'active',
        'restful' => false,
        'cascade_data' => true,
        'rest_base' => '',
        'active_element' => 'item',
      ),
    ),
    'views' => 
    array (
      'bootstrap-items' => 'laravel-menu::bootstrap-navbar-items',
    ),
  ),
  'laravel-model-caching' => 
  array (
    'cache-prefix' => '',
    'enabled' => true,
    'use-database-keying' => true,
    'store' => NULL,
  ),
  'laravel-owm' => 
  array (
    'api_key' => '4e53bfd40e3aec9fd20e347c7ef902bf',
    'routes_enabled' => true,
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'daily',
        ),
        'ignore_exceptions' => false,
        'permission' => 436,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\logs/laravel.log',
        'level' => 'debug',
        'permission' => 436,
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
        'permission' => 436,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
        'permission' => 436,
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
        'permission' => 436,
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'cloudwatch' => 
      array (
        'driver' => 'custom',
        'via' => 'App\\Logging\\CloudWatchLoggerFactory',
        'sdk' => 
        array (
          'region' => 'us-east-1',
          'version' => 'latest',
          'credentials' => 
          array (
            'key' => NULL,
            'secret' => NULL,
          ),
        ),
        'retention' => 7,
        'batchsize' => 10000,
        'level' => 'error',
      ),
    ),
  ),
  'mail' => 
  array (
    'driver' => 'smtp',
    'host' => '127.0.0.1',
    'port' => '1025',
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'The Careers Department Local',
    ),
    'reply_to' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Sarah Warmoll',
    ),
    'encryption' => NULL,
    'username' => NULL,
    'password' => NULL,
    'sendmail' => '/usr/sbin/sendmail -bs',
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'C:\\dev\\PhpProjects\\the-careers-department\\resources\\views/vendor/mail',
      ),
    ),
    'contact' => 
    array (
      'to' => '<EMAIL>',
      'name' => 'Sarah Warmoll',
    ),
    'mailers' => 
    array (
      'intercom' => 
      array (
        'transport' => 'intercom',
      ),
    ),
  ),
  'mail-viewer' => 
  array (
    'table' => 'mail_logs',
    'uri_prefix' => 'mail123',
    'middleware' => 
    array (
      0 => 'web',
    ),
    'date_format' => 'd.m.Y H:i:s',
    'time_format' => 'H:i:s',
    'timezone' => 'UTC',
    'emails_per_page' => 20,
    'prune_older_than_days' => 31,
    'site_url' => NULL,
  ),
  'newsletter' => 
  array (
    'driver' => 'api',
    'apiKey' => '************************************',
    'defaultListName' => 'students',
    'lists' => 
    array (
      'students' => 
      array (
        'id' => 'de628f2d8a',
        'interests' => 
        array (
          'industries' => 'b09954f0ff',
        ),
      ),
      'parents' => 
      array (
        'id' => 'd3df47f8c2',
        'interests' => 
        array (
          'years' => 'ca4e3ffe3c',
          'industries' => '8ab934acc8',
        ),
      ),
      'teachers' => 
      array (
        'id' => '994646fe03',
      ),
      'parent_subscribers' => 
      array (
        'id' => '7462b7307b',
      ),
    ),
    'ssl' => true,
  ),
  'octane' => 
  array (
    'server' => 'frankenphp',
    'https' => false,
    'listeners' => 
    array (
      'Laravel\\Octane\\Events\\WorkerStarting' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\EnsureUploadedFilesAreValid',
        1 => 'Laravel\\Octane\\Listeners\\EnsureUploadedFilesCanBeMoved',
      ),
      'Laravel\\Octane\\Events\\RequestReceived' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\CreateConfigurationSandbox',
        1 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToAuthorizationGate',
        2 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToBroadcastManager',
        3 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseManager',
        4 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseSessionHandler',
        5 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToFilesystemManager',
        6 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToHttpKernel',
        7 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToMailManager',
        8 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToNotificationChannelManager',
        9 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToPipelineHub',
        10 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToCacheManager',
        11 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToSessionManager',
        12 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToQueueManager',
        13 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToRouter',
        14 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToValidationFactory',
        15 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToViewFactory',
        16 => 'Laravel\\Octane\\Listeners\\FlushDatabaseRecordModificationState',
        17 => 'Laravel\\Octane\\Listeners\\FlushDatabaseQueryLog',
        18 => 'Laravel\\Octane\\Listeners\\RefreshQueryDurationHandling',
        19 => 'Laravel\\Octane\\Listeners\\FlushLogContext',
        20 => 'Laravel\\Octane\\Listeners\\FlushArrayCache',
        21 => 'Laravel\\Octane\\Listeners\\FlushMonologState',
        22 => 'Laravel\\Octane\\Listeners\\FlushStrCache',
        23 => 'Laravel\\Octane\\Listeners\\FlushTranslatorCache',
        24 => 'Laravel\\Octane\\Listeners\\PrepareInertiaForNextOperation',
        25 => 'Laravel\\Octane\\Listeners\\PrepareLivewireForNextOperation',
        26 => 'Laravel\\Octane\\Listeners\\PrepareScoutForNextOperation',
        27 => 'Laravel\\Octane\\Listeners\\PrepareSocialiteForNextOperation',
        28 => 'Laravel\\Octane\\Listeners\\FlushLocaleState',
        29 => 'Laravel\\Octane\\Listeners\\FlushQueuedCookies',
        30 => 'Laravel\\Octane\\Listeners\\FlushSessionState',
        31 => 'Laravel\\Octane\\Listeners\\FlushAuthenticationState',
        32 => 'Laravel\\Octane\\Listeners\\EnforceRequestScheme',
        33 => 'Laravel\\Octane\\Listeners\\EnsureRequestServerPortMatchesScheme',
        34 => 'Laravel\\Octane\\Listeners\\GiveNewRequestInstanceToApplication',
        35 => 'Laravel\\Octane\\Listeners\\GiveNewRequestInstanceToPaginator',
      ),
      'Laravel\\Octane\\Events\\RequestHandled' => 
      array (
      ),
      'Laravel\\Octane\\Events\\RequestTerminated' => 
      array (
      ),
      'Laravel\\Octane\\Events\\TaskReceived' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\CreateConfigurationSandbox',
        1 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToAuthorizationGate',
        2 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToBroadcastManager',
        3 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseManager',
        4 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseSessionHandler',
        5 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToFilesystemManager',
        6 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToHttpKernel',
        7 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToMailManager',
        8 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToNotificationChannelManager',
        9 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToPipelineHub',
        10 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToCacheManager',
        11 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToSessionManager',
        12 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToQueueManager',
        13 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToRouter',
        14 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToValidationFactory',
        15 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToViewFactory',
        16 => 'Laravel\\Octane\\Listeners\\FlushDatabaseRecordModificationState',
        17 => 'Laravel\\Octane\\Listeners\\FlushDatabaseQueryLog',
        18 => 'Laravel\\Octane\\Listeners\\RefreshQueryDurationHandling',
        19 => 'Laravel\\Octane\\Listeners\\FlushLogContext',
        20 => 'Laravel\\Octane\\Listeners\\FlushArrayCache',
        21 => 'Laravel\\Octane\\Listeners\\FlushMonologState',
        22 => 'Laravel\\Octane\\Listeners\\FlushStrCache',
        23 => 'Laravel\\Octane\\Listeners\\FlushTranslatorCache',
        24 => 'Laravel\\Octane\\Listeners\\PrepareInertiaForNextOperation',
        25 => 'Laravel\\Octane\\Listeners\\PrepareLivewireForNextOperation',
        26 => 'Laravel\\Octane\\Listeners\\PrepareScoutForNextOperation',
        27 => 'Laravel\\Octane\\Listeners\\PrepareSocialiteForNextOperation',
      ),
      'Laravel\\Octane\\Events\\TaskTerminated' => 
      array (
      ),
      'Laravel\\Octane\\Events\\TickReceived' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\CreateConfigurationSandbox',
        1 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToAuthorizationGate',
        2 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToBroadcastManager',
        3 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseManager',
        4 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToDatabaseSessionHandler',
        5 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToFilesystemManager',
        6 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToHttpKernel',
        7 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToMailManager',
        8 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToNotificationChannelManager',
        9 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToPipelineHub',
        10 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToCacheManager',
        11 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToSessionManager',
        12 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToQueueManager',
        13 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToRouter',
        14 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToValidationFactory',
        15 => 'Laravel\\Octane\\Listeners\\GiveNewApplicationInstanceToViewFactory',
        16 => 'Laravel\\Octane\\Listeners\\FlushDatabaseRecordModificationState',
        17 => 'Laravel\\Octane\\Listeners\\FlushDatabaseQueryLog',
        18 => 'Laravel\\Octane\\Listeners\\RefreshQueryDurationHandling',
        19 => 'Laravel\\Octane\\Listeners\\FlushLogContext',
        20 => 'Laravel\\Octane\\Listeners\\FlushArrayCache',
        21 => 'Laravel\\Octane\\Listeners\\FlushMonologState',
        22 => 'Laravel\\Octane\\Listeners\\FlushStrCache',
        23 => 'Laravel\\Octane\\Listeners\\FlushTranslatorCache',
        24 => 'Laravel\\Octane\\Listeners\\PrepareInertiaForNextOperation',
        25 => 'Laravel\\Octane\\Listeners\\PrepareLivewireForNextOperation',
        26 => 'Laravel\\Octane\\Listeners\\PrepareScoutForNextOperation',
        27 => 'Laravel\\Octane\\Listeners\\PrepareSocialiteForNextOperation',
      ),
      'Laravel\\Octane\\Events\\TickTerminated' => 
      array (
      ),
      'Laravel\\Octane\\Contracts\\OperationTerminated' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\FlushTemporaryContainerInstances',
      ),
      'Laravel\\Octane\\Events\\WorkerErrorOccurred' => 
      array (
        0 => 'Laravel\\Octane\\Listeners\\ReportException',
        1 => 'Laravel\\Octane\\Listeners\\StopWorkerIfNecessary',
      ),
      'Laravel\\Octane\\Events\\WorkerStopping' => 
      array (
      ),
    ),
    'warm' => 
    array (
      0 => 'auth',
      1 => 'cache',
      2 => 'cache.store',
      3 => 'config',
      4 => 'cookie',
      5 => 'db',
      6 => 'db.factory',
      7 => 'db.transactions',
      8 => 'encrypter',
      9 => 'files',
      10 => 'hash',
      11 => 'log',
      12 => 'router',
      13 => 'routes',
      14 => 'session',
      15 => 'session.store',
      16 => 'translator',
      17 => 'url',
      18 => 'view',
    ),
    'flush' => 
    array (
      0 => 'Barryvdh\\Debugbar\\LaravelDebugbar',
    ),
    'cache' => 
    array (
      'rows' => 1000,
      'bytes' => 10000,
    ),
    'tables' => 
    array (
      'example:1000' => 
      array (
        'name' => 'string:1000',
        'votes' => 'int',
      ),
    ),
    'watch' => 
    array (
      0 => 'app',
      1 => 'bootstrap',
      2 => 'config',
      3 => 'database',
      4 => 'public/**/*.php',
      5 => 'resources/**/*.php',
      6 => 'routes',
      7 => 'composer.lock',
      8 => '.env',
    ),
    'garbage' => 50,
    'max_execution_time' => 30,
  ),
  'permission' => 
  array (
    'models' => 
    array (
      'permission' => 'App\\Permission',
      'role' => 'App\\Role',
    ),
    'table_names' => 
    array (
      'roles' => 'roles',
      'permissions' => 'permissions',
      'model_has_permissions' => 'model_has_permissions',
      'model_has_roles' => 'model_has_roles',
      'role_has_permissions' => 'role_has_permissions',
    ),
    'cache_expiration_time' => 1440,
  ),
  'profilling' => 
  array (
    'view_limit_job_suggestion' => 5,
  ),
  'queue' => 
  array (
    'default' => 'database',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'after_commit' => true,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => 'your-public-key',
        'secret' => 'your-secret-key',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'your-queue-name',
        'region' => 'us-east-1',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'tries' => 3,
        'after_commit' => true,
      ),
    ),
    'failed' => 
    array (
      'database' => 'mysql',
      'table' => 'failed_jobs',
      'driver' => 'database-uuids',
    ),
  ),
  'scout' => 
  array (
    'driver' => 'null',
    'prefix' => '',
    'queue' => false,
    'after_commit' => false,
    'chunk' => 
    array (
      'searchable' => 500,
      'unsearchable' => 500,
    ),
    'soft_delete' => false,
    'identify' => false,
    'algolia' => 
    array (
      'id' => '',
      'key' => '',
      'secret' => '',
    ),
    'meilisearch' => 
    array (
      'host' => 'http://localhost:7700',
      'key' => NULL,
      'index-settings' => 
      array (
      ),
    ),
  ),
  'secure-headers' => 
  array (
    'server' => '',
    'x-content-type-options' => 'nosniff',
    'x-download-options' => 'noopen',
    'x-frame-options' => 'sameorigin',
    'x-permitted-cross-domain-policies' => 'none',
    'x-powered-by' => '',
    'x-xss-protection' => '1; mode=block',
    'referrer-policy' => 'no-referrer',
    'cross-origin-embedder-policy' => 'unsafe-none',
    'cross-origin-opener-policy' => 'unsafe-none',
    'cross-origin-resource-policy' => 'cross-origin',
    'clear-site-data' => 
    array (
      'enable' => false,
      'all' => false,
      'cache' => true,
      'cookies' => true,
      'storage' => true,
      'executionContexts' => true,
    ),
    'hsts' => 
    array (
      'enable' => true,
      'max-age' => 31536000,
      'include-sub-domains' => false,
      'preload' => false,
    ),
    'expect-ct' => 
    array (
      'enable' => false,
      'max-age' => 2147483648,
      'enforce' => false,
      'report-uri' => NULL,
    ),
    'permissions-policy' => 
    array (
      'enable' => true,
      'accelerometer' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'ambient-light-sensor' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'autoplay' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'battery' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'camera' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'cross-origin-isolated' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'display-capture' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'document-domain' => 
      array (
        'none' => false,
        '*' => true,
        'self' => false,
        'origins' => 
        array (
        ),
      ),
      'encrypted-media' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'execution-while-not-rendered' => 
      array (
        'none' => false,
        '*' => true,
        'self' => false,
        'origins' => 
        array (
        ),
      ),
      'execution-while-out-of-viewport' => 
      array (
        'none' => false,
        '*' => true,
        'self' => false,
        'origins' => 
        array (
        ),
      ),
      'fullscreen' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'geolocation' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'gyroscope' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'magnetometer' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'microphone' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'midi' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'navigation-override' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'payment' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'picture-in-picture' => 
      array (
        'none' => false,
        '*' => true,
        'self' => false,
        'origins' => 
        array (
        ),
      ),
      'publickey-credentials-get' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'screen-wake-lock' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'sync-xhr' => 
      array (
        'none' => false,
        '*' => true,
        'self' => false,
        'origins' => 
        array (
        ),
      ),
      'usb' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'web-share' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
      'xr-spatial-tracking' => 
      array (
        'none' => false,
        '*' => false,
        'self' => true,
        'origins' => 
        array (
        ),
      ),
    ),
    'csp' => 
    array (
      'enable' => false,
      'report-only' => true,
      'report-to' => '',
      'report-uri' => 
      array (
      ),
      'block-all-mixed-content' => false,
      'upgrade-insecure-requests' => false,
      'base-uri' => 
      array (
      ),
      'child-src' => 
      array (
      ),
      'connect-src' => 
      array (
        'self' => true,
        'allow' => 
        array (
          0 => 'thecareersdepartment.helpcrunch.com',
        ),
      ),
      'default-src' => 
      array (
        'self' => true,
        'allow' => 
        array (
          0 => '*.google.com',
          1 => '*.youtube.com',
        ),
      ),
      'font-src' => 
      array (
        'self' => true,
        'schemes' => 
        array (
          0 => 'data:',
        ),
        'allow' => 
        array (
          0 => 'fonts.gstatic.com',
          1 => 'stackpath.bootstrapcdn.com',
          2 => 'fonts.googleapis.com',
        ),
      ),
      'form-action' => 
      array (
      ),
      'frame-ancestors' => 
      array (
      ),
      'frame-src' => 
      array (
        'self' => true,
        'allow' => 
        array (
          0 => 'player.vimeo.com',
        ),
      ),
      'img-src' => 
      array (
        'self' => true,
        'allow' => 
        array (
          0 => 'www.google-analytics.com',
          1 => 'www.facebook.com',
          2 => 'cdn.ckeditor.com',
          3 => 'stats.g.doubleclick.net',
          4 => 'd.adroll.com',
          5 => 'ads.yahoo.com',
          6 => 'x.bidswitch.net',
          7 => 'ib.adnxs.com',
          8 => 'idsync.rlcdn.com',
          9 => 'us-u.openx.net',
          10 => 'dpm.demdex.net',
          11 => 'cm.g.doubleclick.net',
          12 => '*.google.com',
        ),
      ),
      'manifest-src' => 
      array (
      ),
      'media-src' => 
      array (
      ),
      'navigate-to' => 
      array (
        'unsafe-allow-redirects' => false,
      ),
      'object-src' => 
      array (
      ),
      'plugin-types' => 
      array (
      ),
      'prefetch-src' => 
      array (
      ),
      'require-trusted-types-for' => 
      array (
        'script' => false,
      ),
      'sandbox' => 
      array (
        'enable' => false,
        'allow-downloads-without-user-activation' => false,
        'allow-forms' => false,
        'allow-modals' => false,
        'allow-orientation-lock' => false,
        'allow-pointer-lock' => false,
        'allow-popups' => false,
        'allow-popups-to-escape-sandbox' => false,
        'allow-presentation' => false,
        'allow-same-origin' => false,
        'allow-scripts' => false,
        'allow-storage-access-by-user-activation' => false,
        'allow-top-navigation' => false,
        'allow-top-navigation-by-user-activation' => false,
      ),
      'script-src' => 
      array (
        'none' => false,
        'self' => true,
        'report-sample' => false,
        'allow' => 
        array (
          0 => 'www.googletagmanager.com',
          1 => 'player.vimeo.com',
          2 => 'connect.facebook.net',
          3 => 'www.google-analytics.com',
          4 => 's.adroll.com',
          5 => 'www.facebook.com',
          6 => 'd.adroll.mgr.consensu.org',
          7 => 'd.adroll.com',
          8 => 'www.gstatic.com',
          9 => 'cdn.ckeditor.com',
          10 => 'widget.helpcrunch.com',
          11 => 'player.flipsnack.com',
        ),
        'schemes' => 
        array (
        ),
        'unsafe-inline' => true,
        'unsafe-eval' => false,
        'unsafe-hashes' => false,
        'strict-dynamic' => false,
        'hashes' => 
        array (
          'sha256' => 
          array (
          ),
          'sha384' => 
          array (
          ),
          'sha512' => 
          array (
          ),
        ),
      ),
      'script-src-attr' => 
      array (
      ),
      'script-src-elem' => 
      array (
        'self' => true,
        'allow' => 
        array (
          0 => 'fonts.googleapis.com',
        ),
      ),
      'style-src' => 
      array (
        'self' => true,
        'unsafe-inline' => true,
      ),
      'style-src-attr' => 
      array (
      ),
      'style-src-elem' => 
      array (
      ),
      'trusted-types' => 
      array (
        'enable' => false,
        'allow-duplicates' => false,
        'default' => false,
        'policies' => 
        array (
        ),
      ),
      'worker-src' => 
      array (
      ),
    ),
    'x-power-by' => '',
    'feature-policy' => 
    array (
      'enable' => true,
      'accelerometer' => 
      array (
        'self' => false,
      ),
      'ambient-light-sensor' => 
      array (
        'self' => true,
      ),
      'autoplay' => 
      array (
        'self' => true,
      ),
      'battery' => 
      array (
        'self' => false,
      ),
      'camera' => 
      array (
        'self' => false,
      ),
      'display-capture' => 
      array (
        'self' => false,
      ),
      'document-domain' => 
      array (
        '*' => true,
      ),
      'encrypted-media' => 
      array (
        'self' => true,
      ),
      'execution-while-not-rendered' => 
      array (
        '*' => true,
      ),
      'execution-while-out-of-viewport' => 
      array (
        '*' => true,
      ),
      'fullscreen' => 
      array (
        'self' => true,
      ),
      'geolocation' => 
      array (
        'self' => true,
      ),
      'gyroscope' => 
      array (
        'self' => false,
      ),
      'layout-animations' => 
      array (
        'self' => true,
      ),
      'legacy-image-formats' => 
      array (
        'self' => true,
      ),
      'magnetometer' => 
      array (
        'self' => true,
      ),
      'microphone' => 
      array (
        'self' => true,
      ),
      'midi' => 
      array (
        'self' => true,
      ),
      'navigation-override' => 
      array (
        'self' => true,
      ),
      'oversized-images' => 
      array (
        '*' => true,
      ),
      'payment' => 
      array (
        'self' => true,
      ),
      'picture-in-picture' => 
      array (
        '*' => true,
      ),
      'publickey-credentials' => 
      array (
        'self' => true,
      ),
      'sync-xhr' => 
      array (
        '*' => true,
      ),
      'unoptimized-images' => 
      array (
      ),
      'unsized-media' => 
      array (
        '*' => true,
      ),
      'usb' => 
      array (
        'self' => true,
      ),
      'wake-lock' => 
      array (
        'self' => true,
      ),
      'xr-spatial-tracking' => 
      array (
        'self' => true,
      ),
    ),
  ),
  'seotools' => 
  array (
    'meta' => 
    array (
      'defaults' => 
      array (
        'title' => 'The Careers Department',
        'description' => 'A digital career advice resource for parents, schools, students and individuals.',
        'separator' => ' - ',
        'keywords' => 
        array (
        ),
        'canonical' => false,
        'robots' => false,
      ),
      'webmaster_tags' => 
      array (
        'google' => NULL,
        'bing' => NULL,
        'alexa' => NULL,
        'pinterest' => NULL,
        'yandex' => NULL,
      ),
    ),
    'opengraph' => 
    array (
      'defaults' => 
      array (
        'title' => 'The Careers Department',
        'description' => 'A digital career advice resource for parents, schools, students and individuals.',
        'url' => NULL,
        'type' => 'website',
        'site_name' => 'The Careers Department',
        'images' => 
        array (
          0 => 'https://thecareersdepartment.com/images/share-image.jpg',
        ),
      ),
    ),
    'twitter' => 
    array (
      'defaults' => 
      array (
        'card' => 'summary',
        'site' => '@TheCareersDept',
      ),
    ),
    'json-ld' => 
    array (
      'defaults' => 
      array (
        'title' => 'The Careers Department',
        'description' => 'A digital career advice resource for parents, schools, students and individuals.',
        'url' => NULL,
        'type' => 'website',
        'site_name' => 'The Careers Department',
        'images' => 
        array (
          0 => 'https://thecareersdepartment.com/images/share-image.jpg',
        ),
      ),
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
    ),
    'ses' => 
    array (
      'key' => '********************',
      'secret' => 'ZYxDUQw1f3ZWepLhGarL9ZS9juatgziSBk4YMbq2',
      'region' => 'ap-southeast-2',
    ),
    'sparkpost' => 
    array (
      'secret' => NULL,
    ),
    'stripe' => 
    array (
      'model' => 'App\\User',
      'key' => 'pk_test_UDtmFO0Hhd0XNBW2WrLobwbP',
      'secret' => 'sk_test_THqCAHtlYpzhRPrIhXDQqeXs',
      'parent_key' => 'plan_Ebg5AWUdAUwmd2',
      'child_key' => 'price_1IYoOaFOu1otgbB0m9xmTnQ8',
      'parent_daily_key' => NULL,
      'child_daily_key' => NULL,
      'individual_key' => 'price_1J1onlFOu1otgbB0p4iq77dN',
    ),
    'indeed' => 
    array (
      'id' => '2955470644339913',
    ),
    'froala' => 
    array (
      'key' => 'hWA2C-7I2A4C3D5D2D2G3wxeklqcwvffrrhxhoqxpkC7bmnxE2F2G2D1B10B2B3E6F1F2==',
    ),
    'maps' => 
    array (
      'key' => 'AIzaSyCy5iyXwwk8nE62H7aJF3NhL45fk_Ok4rI',
    ),
    'forge' => 
    array (
      'token' => '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      'loadbalancer_server_id' => '522786',
      'loadbalancer_site_id' => '1524623',
    ),
    'aws' => 
    array (
      'lambda_token' => '0B2hRgnoCpoi3jByKLDD0it87zselqyZu8klYlDxb8dviyiJPSgOnw5P9BPwJDf92Hs3aAvKwEYl4lb1makdYriBHyu43KhqzCKnPhAv3t91J0sEUEjuKVzTtrxCGRMKUk51QcRG25MiIIRitHEQcIWxvfKcAQV07nDxi3iuQwulhUM7bBoK4QMzvkoDpWSczAAMGGqzVMOObbGVtBuKCBy7PaXL0dYNPdAm5X3g3AZNp67lutXXumsd115gfqy7B',
    ),
    'portfolio' => 
    array (
      'url' => 'http://eportfolio.school.test',
    ),
    'wix' => 
    array (
      'url' => 'https://www.thecareersdepartment.com',
    ),
  ),
  'session' => 
  array (
    'driver' => 'redis',
    'lifetime' => '360',
    'expired-session-redirect' => '/login',
    'expire_on_close' => true,
    'encrypt' => false,
    'files' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'the_careers_department_local_session',
    'path' => '/',
    'domain' => '.school.test',
    'secure' => NULL,
    'http_only' => true,
    'same_site' => 'lax',
  ),
  'sluggable' => 
  array (
    'source' => NULL,
    'maxLength' => NULL,
    'maxLengthKeepWords' => true,
    'method' => NULL,
    'separator' => '-',
    'unique' => true,
    'uniqueSuffix' => NULL,
    'firstUniqueSuffix' => 2,
    'includeTrashed' => false,
    'reserved' => NULL,
    'onUpdate' => false,
    'slugEngineOptions' => 
    array (
    ),
  ),
  'sneaker' => 
  array (
    'silent' => true,
    'capture' => 
    array (
      0 => '*',
    ),
    'to' => 
    array (
      0 => '<EMAIL>',
    ),
    'ignored_bots' => 
    array (
      0 => 'yandexbot',
      1 => 'googlebot',
      2 => 'bingbot',
      3 => 'slurp',
      4 => 'ia_archiver',
    ),
  ),
  'social-share' => 
  array (
    'separator' => '&',
    'services' => 
    array (
      'delicious' => 
      array (
        'uri' => 'https://delicious.com/post',
      ),
      'digg' => 
      array (
        'uri' => 'http://www.digg.com/submit',
      ),
      'email' => 
      array (
        'view' => 'social-share::email',
      ),
      'evernote' => 
      array (
        'uri' => 'http://www.evernote.com/clip.action',
      ),
      'facebook' => 
      array (
        'uri' => 'https://www.facebook.com/sharer/sharer.php',
        'urlName' => 'https://thecareersdepartment.com/',
      ),
      'gmail' => 
      array (
        'uri' => 'https://mail.google.com/mail/',
        'urlName' => 'su',
        'titleName' => 'body',
        'extra' => 
        array (
          'view' => 'cm',
          'fs' => 1,
          'to' => '',
          'ui' => 2,
          'tf' => 1,
        ),
      ),
      'gplus' => 
      array (
        'uri' => 'https://plus.google.com/share',
        'only' => 
        array (
          0 => 'url',
        ),
      ),
      'linkedin' => 
      array (
        'uri' => 'http://www.linkedin.com/shareArticle',
        'extra' => 
        array (
          'mini' => 'true',
        ),
      ),
      'pinterest' => 
      array (
        'uri' => 'http://pinterest.com/pin/create/button/',
        'titleName' => 'description',
        'mediaName' => 'media',
      ),
      'reddit' => 
      array (
        'uri' => 'http://www.reddit.com/submit',
      ),
      'scoopit' => 
      array (
        'uri' => 'http://www.scoop.it/oexchange/share',
      ),
      'tumblr' => 
      array (
        'uri' => 'http://www.tumblr.com/share',
        'urlName' => 'u',
        'titleName' => 't',
        'extra' => 
        array (
          'v' => 3,
        ),
      ),
      'twitter' => 
      array (
        'uri' => 'https://twitter.com/intent/tweet',
        'titleName' => 'text',
      ),
      'viadeo' => 
      array (
        'uri' => 'http://www.viadeo.com/',
      ),
      'vk' => 
      array (
        'uri' => 'http://vk.com/share.php',
        'mediaName' => 'image',
        'extra' => 
        array (
          'noparse' => 'false',
        ),
      ),
      'whatsapp' => 
      array (
        'view' => 'social-share::whatsapp',
      ),
    ),
  ),
  'sociallinks' => 
  array (
    'facebook' => 'https://www.facebook.com/thecareersdepartment/',
    'twitter' => 'https://twitter.com/TheCareersDept/',
    'instagram' => 'https://www.instagram.com/thecareersdepartment/',
    'linkedin' => 'https://www.linkedin.com/company/19070751/',
  ),
  'studio' => 
  array (
    'path' => 'studio',
    'identifier' => 'id',
  ),
  'tagging' => 
  array (
    'primary_keys_type' => 'integer',
    'normalizer' => '\\Conner\\Tagging\\Util::slug',
    'displayer' => '\\Illuminate\\Support\\Str::title',
    'untag_on_delete' => true,
    'delete_unused_tags' => true,
    'tag_model' => '\\Conner\\Tagging\\Model\\Tag',
    'delimiter' => '-',
    'tagged_model' => '\\Conner\\Tagging\\Model\\Tagged',
  ),
  'telescope' => 
  array (
    'domain' => NULL,
    'path' => 'telescope',
    'driver' => 'database',
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
        'chunk' => 1000,
      ),
    ),
    'enabled' => true,
    'middleware' => 
    array (
      0 => 'web',
      1 => 'admin',
    ),
    'only_paths' => 
    array (
    ),
    'ignore_paths' => 
    array (
      0 => 'nova-api*',
    ),
    'ignore_commands' => 
    array (
    ),
    'watchers' => 
    array (
      'Laravel\\Telescope\\Watchers\\CacheWatcher' => false,
      'Laravel\\Telescope\\Watchers\\CommandWatcher' => 
      array (
        'enabled' => false,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\DumpWatcher' => false,
      'Laravel\\Telescope\\Watchers\\EventWatcher' => 
      array (
        'enabled' => false,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ExceptionWatcher' => true,
      'Laravel\\Telescope\\Watchers\\JobWatcher' => true,
      'Laravel\\Telescope\\Watchers\\LogWatcher' => false,
      'Laravel\\Telescope\\Watchers\\MailWatcher' => true,
      'Laravel\\Telescope\\Watchers\\ModelWatcher' => 
      array (
        'enabled' => false,
        'events' => 
        array (
          0 => 'eloquent.*',
        ),
      ),
      'Laravel\\Telescope\\Watchers\\NotificationWatcher' => true,
      'Laravel\\Telescope\\Watchers\\QueryWatcher' => 
      array (
        'enabled' => false,
        'ignore_packages' => true,
        'slow' => 100,
      ),
      'Laravel\\Telescope\\Watchers\\RedisWatcher' => false,
      'Laravel\\Telescope\\Watchers\\RequestWatcher' => 
      array (
        'enabled' => false,
        'size_limit' => 64,
      ),
      'Laravel\\Telescope\\Watchers\\GateWatcher' => 
      array (
        'enabled' => false,
        'ignore_abilities' => 
        array (
        ),
        'ignore_packages' => true,
      ),
      'Laravel\\Telescope\\Watchers\\ScheduleWatcher' => false,
      'Laravel\\Telescope\\Watchers\\ViewWatcher' => false,
    ),
  ),
  'ttwitter' => 
  array (
    'debug' => true,
    'API_URL' => 'api.twitter.com',
    'UPLOAD_URL' => 'upload.twitter.com',
    'API_VERSION' => '1.1',
    'AUTHENTICATE_URL' => 'https://api.twitter.com/oauth/authenticate',
    'AUTHORIZE_URL' => 'https://api.twitter.com/oauth/authorize',
    'ACCESS_TOKEN_URL' => 'https://api.twitter.com/oauth/access_token',
    'REQUEST_TOKEN_URL' => 'https://api.twitter.com/oauth/request_token',
    'USE_SSL' => true,
    'CONSUMER_KEY' => '*************************',
    'CONSUMER_SECRET' => 'd8zcBUgPOAKkhfLr6VDOcVWtL5M9qV30cIH7odHay0EEPV3ka0',
    'ACCESS_TOKEN' => '2862784502-6Jt4V1ojrMWQ6prPxfLLGmV9XsxOXsEs2JCU1Bl',
    'ACCESS_TOKEN_SECRET' => 'MFWpq7qjpJHjKOOhPVlbv92YQZQqr5knsotClYMawNMrZ',
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\dev\\PhpProjects\\the-careers-department\\resources\\views',
    ),
    'compiled' => 'C:\\dev\\PhpProjects\\the-careers-department\\storage\\framework\\views',
  ),
  'aws' => 
  array (
    'region' => 'ap-southeast-2',
    'version' => 'latest',
    'ua_append' => 
    array (
      0 => 'L5MOD/3.7.0',
    ),
  ),
  'cashier' => 
  array (
    'key' => NULL,
    'secret' => 'sk_test_THqCAHtlYpzhRPrIhXDQqeXs',
    'path' => 'stripe',
    'webhook' => 
    array (
      'secret' => NULL,
      'tolerance' => 300,
    ),
    'currency' => 'usd',
    'currency_locale' => 'en',
    'payment_notification' => NULL,
    'invoices' => 
    array (
      'renderer' => 'Laravel\\Cashier\\Invoices\\DompdfInvoiceRenderer',
      'options' => 
      array (
        'paper' => 'letter',
      ),
    ),
    'logger' => 'stack',
  ),
  'datatables' => 
  array (
    'search' => 
    array (
      'smart' => true,
      'multi_term' => true,
      'case_insensitive' => true,
      'use_wildcards' => false,
      'starts_with' => false,
    ),
    'index_column' => 'DT_RowIndex',
    'engines' => 
    array (
      'eloquent' => 'Yajra\\DataTables\\EloquentDataTable',
      'query' => 'Yajra\\DataTables\\QueryDataTable',
      'collection' => 'Yajra\\DataTables\\CollectionDataTable',
      'resource' => 'Yajra\\DataTables\\ApiResourceDataTable',
    ),
    'builders' => 
    array (
    ),
    'nulls_last_sql' => ':column :direction NULLS LAST',
    'error' => NULL,
    'columns' => 
    array (
      'excess' => 
      array (
        0 => 'rn',
        1 => 'row_num',
      ),
      'escape' => '*',
      'raw' => 
      array (
        0 => 'action',
      ),
      'blacklist' => 
      array (
        0 => 'password',
        1 => 'remember_token',
      ),
      'whitelist' => '*',
    ),
    'json' => 
    array (
      'header' => 
      array (
      ),
      'options' => 0,
    ),
  ),
  'ide-helper' => 
  array (
    'filename' => '_ide_helper.php',
    'models_filename' => '_ide_helper_models.php',
    'meta_filename' => '.phpstorm.meta.php',
    'include_fluent' => false,
    'include_factory_builders' => false,
    'write_model_magic_where' => true,
    'write_model_external_builder_methods' => true,
    'write_model_relation_count_properties' => true,
    'write_eloquent_model_mixins' => false,
    'include_helpers' => false,
    'helper_files' => 
    array (
      0 => 'C:\\dev\\PhpProjects\\the-careers-department/vendor/laravel/framework/src/Illuminate/Support/helpers.php',
    ),
    'model_locations' => 
    array (
      0 => 'app',
    ),
    'ignored_models' => 
    array (
    ),
    'model_hooks' => 
    array (
    ),
    'extra' => 
    array (
      'Eloquent' => 
      array (
        0 => 'Illuminate\\Database\\Eloquent\\Builder',
        1 => 'Illuminate\\Database\\Query\\Builder',
      ),
      'Session' => 
      array (
        0 => 'Illuminate\\Session\\Store',
      ),
    ),
    'magic' => 
    array (
    ),
    'interfaces' => 
    array (
    ),
    'custom_db_types' => 
    array (
    ),
    'model_camel_case_properties' => false,
    'type_overrides' => 
    array (
      'integer' => 'int',
      'boolean' => 'bool',
    ),
    'include_class_docblocks' => false,
    'force_fqn' => false,
    'use_generics_annotations' => true,
    'additional_relation_types' => 
    array (
    ),
    'additional_relation_return_types' => 
    array (
    ),
    'post_migrate' => 
    array (
    ),
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
