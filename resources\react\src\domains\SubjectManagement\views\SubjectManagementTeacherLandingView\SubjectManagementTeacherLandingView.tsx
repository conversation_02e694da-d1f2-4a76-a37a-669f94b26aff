import Button from '@/components/base/Button/Button';
import SubjectSelectionLandingLayout from '@/components/layouts/SubjectSelectionLandingLayout/SubjectSelectionLandingLayout';
import { SUBJECTS_SELECTION_MANAGEMENT_STATISTICS } from '@/router/Router.constants';
import { ButtonType } from '@/utils/enums';
import { useNavigate } from 'react-router-dom';

export default function SubjectManagementTeacherLandingView() {
    const navigate = useNavigate();

    return (
        <SubjectSelectionLandingLayout>
            <div className='d-flex align-items-center justify-content-center h-100'>
                <div className='col-lg-3'>
                    <span className='fs-3 fw-normal'>Tools</span>
                    <h1 className='landing-view__title mb-10 fs-2 fw-normal'>Subject Selections</h1>
                    <Button
                        style={{ maxWidth: 360, padding: '20px 0' }}
                        className='mb-6 fs-5 fw-normal'
                        onClick={() => navigate(SUBJECTS_SELECTION_MANAGEMENT_STATISTICS)}
                        title='Manage Tool'
                        type={ButtonType.PRIMARY}
                    />
                </div>
                <div className='col-lg-8'></div>
            </div>
        </SubjectSelectionLandingLayout>
    );
}
