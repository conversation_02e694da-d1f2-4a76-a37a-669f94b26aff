<template>
   <div class="modal fade" id="kt_modal_viewResponse" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content rounded-0 mt-5">
                <div class="modal-header py-3">
                    <h5 class="modal-title"></h5>
                    <div>
                        <span class="mx-4  cursor-pointer" @click="toggleFullscreen">
                            <i class="fa-solid fa-expand text-black text-black"></i>
                        </span>
                        <a v-if="downloadUrl" :href="downloadUrl" download class="text-secondary mx-2">
                            <i class="fa-solid fa-download text-black"></i>
                        </a>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body bg-black p-0 text-white text-center">

                    <iframe class="w-100" id="previewFrame" style="height:80vh" :src="modalSrc" allowfullscreen></iframe>

                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { defineProps } from 'vue'
  
  const props = defineProps<{
    modalSrc: string
    downloadUrl: string
  }>()
  
  const toggleFullscreen = async () => {
    const iframe = document.querySelector('#kt_modal_viewResponse iframe') as HTMLIFrameElement
    if (!iframe) return
  
    try {
      if (!document.fullscreenElement) {
        await iframe.requestFullscreen()
      } else {
        await document.exitFullscreen()
      }
    } catch (err) {
      console.error('Error attempting to toggle fullscreen:', err)
    }
  }
  </script>
  