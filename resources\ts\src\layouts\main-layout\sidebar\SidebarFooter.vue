<template>
  <!--begin::Footer-->
  <div
    class="aside-footer flex-column-auto"
    id="kt_app_sidebar_footer"
  >
    <a
      href="https://help.thecareersdepartment.com/en/"
      class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap pb-20 px-0 h-40px w-100"
      data-bs-toggle="tooltip"
      data-bs-trigger="hover"
      data-bs-dismiss-="click"
      title="200+ in-house components and 3rd-party plugins"
      target="_blank"
    >
      <span class="btn-label footer"></span>
      <span class="svg-icon btn-icon svg-icon-2 m-0 footer-icon">
        <inline-svg src="media/icons/sidebar/teacher/Support.svg" />
      </span>
    </a>
  </div>
  <!--end::Footer-->
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "sidebar-footer",
  components: {},
});
</script>
<style>
.footer svg path{
    fill: #707070;
}

.footer-icon:hover svg path{
    fill: #fff;
}
</style>
