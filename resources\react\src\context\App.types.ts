import { UserResponse } from '@/domains/Auth/models/Auth.model';
import { SubjectPreferenceStatus } from '@/domains/SubjectSelection/models/SubjectSelection.api.model';
import {
    SubjectPreference,
    SubjectSelectionQuizState,
    SubjectsMap,
} from '@/domains/SubjectSelection/models/SubjectSelection.model';

export enum AppActionType {
    SET_USER_DATA = 'SET_USER_DATA',
    SET_QUIZ_SUBMITTED = 'SET_QUIZ_SUBMITTED',
    SET_MAPPED_SUBJECTS = 'SET_MAPPED_SUBJECTS',
    SET_SELECTED_PREFERENCES = 'SET_SELECTED_PREFERENCES',
    SET_PREFERENCES_DATA = 'SET_PREFERENCES_DATA',
    SET_PREFERENCES_SUBMISSION = 'SET_PREFERENCES_SUBMISSION',
    SET_QUIZ_SUBMITTED_SELECTION = 'SET_QUIZ_SUBMITTED_SELECTION',
    SET_RULES_PROGRESS_BY_ID = 'SET_RULES_PROGRESS_BY_ID',
    SET_RULES_PROGRESS = 'SET_RULES_PROGRESS',
}

export interface AppState {
    user?: UserResponse;
    isQuizSubmitted: boolean;
    isPreferencesSubmitted: boolean;
    mappedSubjects?: Map<number | 'other', SubjectsMap>;
    selectedPreferencesStatus?: SubjectPreferenceStatus;
    selectedPreferences: SubjectPreference[];
    ruleProgress: number[];
    submittedQuizSelection?: SubjectSelectionQuizState;
}

export interface SetUserState {
    type: AppActionType.SET_USER_DATA;
    payload: UserResponse;
}

export interface SetQuizSubmissionState {
    type: AppActionType.SET_QUIZ_SUBMITTED;
    payload: boolean;
}

export interface SetMappedSubjectsState {
    type: AppActionType.SET_MAPPED_SUBJECTS;
    payload: Map<number | 'other', SubjectsMap>;
}

export interface SetSelectedPreferencesState {
    type: AppActionType.SET_SELECTED_PREFERENCES;
    payload: SubjectPreference;
}

export interface SetSubjectPreferenceDataState {
    type: AppActionType.SET_PREFERENCES_DATA;
    payload: { subjects: SubjectPreference[]; status: SubjectPreferenceStatus };
}

export interface SetSubjectPreferencesSubmissionState {
    type: AppActionType.SET_PREFERENCES_SUBMISSION;
    payload: boolean;
}

export interface SetSubmittedQuizSelectionState {
    type: AppActionType.SET_QUIZ_SUBMITTED_SELECTION;
    payload: SubjectSelectionQuizState;
}

export interface SetRulesProgressByIdState {
    type: AppActionType.SET_RULES_PROGRESS_BY_ID;
    payload: number;
}

export interface SetRulesProgressState {
    type: AppActionType.SET_RULES_PROGRESS;
    payload: number[];
}

export type AppActions =
    | SetUserState
    | SetQuizSubmissionState
    | SetMappedSubjectsState
    | SetSelectedPreferencesState
    | SetSubjectPreferenceDataState
    | SetSubjectPreferencesSubmissionState
    | SetSubmittedQuizSelectionState
    | SetRulesProgressByIdState
    | SetRulesProgressState;
