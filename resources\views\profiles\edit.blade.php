@extends('layouts.admin')
@section('breadcrumbs', Breadcrumbs::render('edit-profile'))
@section('content') @if (session()->has('message'))
    <div class="alert alert-success text-center">
        <a href="#" class="close" data-dismiss="alert" aria-label="close"> </a> {{ session()->get('message') }}
    </div>
@endif
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card card-transparent">
            <div class="card-header">
                <img src="{{ asset('images/favicon.png') }}" alt="*" class="card-title-X">
                <div class="card-title custom-card-title">Update Profile</div>
            </div>
            <div class="card-block">
                <form id="form-personal" method="POST" role="form" autocomplete="off" enctype="multipart/form-data">
                    @csrf
                    @method("PUT")
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>First Name</label>
                                <input type="text" class="form-control" name="firstname" required="" value="{{ $user->profile->firstname }}" aria-required="true">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Last Name</label>
                                <input type="text" class="form-control" name="lastname" required="" value="{{ $user->profile->lastname }}" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="row clearfix">
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Email</label>
                                <input type="email" class="form-control" name="email" value="{{ $user->email }}" required="" aria-required="true">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default" aria-required="true">
                                <label>Password</label>
                                <input type="password" class="form-control" name="password" placeholder="Enter only if you want to change" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        {{-- <div class="col-md-6">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Country</label>
                                <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                    <option selected disabled></option>
                                    @foreach ($countries as $country)
                                        <option @if ($user->state && $country->id == $user->state->country->id) selected="selected" @endif value="{{ $country->id }}">{{ $country->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>State</label>
                                <select name="state" class="form-control" data-init-plugin="select2" data-placement="Select..">
                                    <option selected disabled></option>
                                    @if ($user->state)
                                        @foreach ($user->state->country->states as $state)
                                            <option @if ($state->id == $user->state_id) selected="selected" @endif value="{{ $state->id }}">{{ $state->name }}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div> --}}
                        <div class="col-md-6">
                            <div class="form-group form-group-default form-group-default-select2">
                                <label>Gender</label>
                                <select class="full-width" name="gender" id="gender" data-init-plugin="select2" data-placeholder="Select..">
                                    <option value=""></option>
                                    <option value="M" {{ @$user->profile->gender == 'M' ? 'selected' : '' }}>Male</option>
                                    <option value="F" {{ @$user->profile->gender == 'F' ? 'selected' : '' }}>Female</option>
                                    <option value="O" {{ @$user->profile->gender && (@$user->profile->gender != 'M' && @$user->profile->gender != 'F') ? 'selected' : '' }}>Other / Prefer not to say</option>
                                </select>
                            </div>
                        </div>
                        {{-- <div class="col-md-6 @if ($user->profile->gender && ($user->profile->gender == 'M' || $user->profile->gender == 'F')) d-none @endif" id="other_gender">
                            <div class="form-group form-group-default required">
                                <label>Other Gender</label>
                                <input type="text" class="form-control other_gender" name="other_gender" @if ($user->profile && $user->profile->gender != 'M' && $user->profile->gender != 'F') value="{{ $user->profile->gender }}" @endif />
                            </div>
                        </div> --}}
                        <div class="col-md-6">
                            <div class="form-group form-group-default required" aria-required="true">
                                <label>Postcode</label>
                                <input type="text" class="form-control" name="postcode" required="" value="{{ $user->postcode }}" aria-required="true">
                            </div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <button class="btn btn-black" type="submit">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
@push('scripts')
    <script>
        jQuery(document).ready(function() {

            // jQuery('#gender').on('change', function() {
            //     if (this.value == 'Other') {
            //         jQuery("#other_gender").removeClass('d-none');
            //     } else {
            //         jQuery("#other_gender").addClass('d-none');
            //         jQuery('#form-personal .other_gender').val('');
            //     }
            // });

            jQuery('#form-personal').validate({
                rules: {
                    firstname: 'required',
                    lastname: 'required',
                    email: {
                        required: true,
                        email: true,
                        remote: {
                            url: "/checkEmailOnUpdate",
                            type: "get",
                            data: {
                                email: function() {
                                    return $("input[name='email']").val();
                                },
                                id: {{ Auth::id() }},
                            },
                        }
                    },
                    // other_gender: {
                    //     required: function() {
                    //         return (jQuery('#gender').val() == 'Other');
                    //     }
                    // },
                },
                messages: {
                    email: {
                        remote: "Email already in use!"
                    },
                }
            })

        });
    </script>
@endpush
@endsection
