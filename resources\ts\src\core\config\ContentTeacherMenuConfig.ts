const DocMenuConfig = [
    {
        pages: [
            {
                heading: "Home",
                route: "/dashboard",
                svgIcon: "media/icons/sidebar/teacher/Home.svg",
                fontIcon: "bi-app-indicator",
            },
            {
                heading: "Profile",
                route: "/profiles/edit",
                isExternal: true,
                svgIcon: "media/icons/sidebar/teacher/Profile.svg",
                fontIcon: "bi-person",
            },
            {
                sectionTitle: "Users",
                route: "/users",
                // isExternal:true,
                svgIcon: "media/icons/sidebar/teacher/Users.svg",
                fontIcon: "bi-person",
                sub: [
                    {
                        heading: "Students",
                        route: "/students",
                        isExternal: true,
                        hasNotAccess: true,

                    },
                    {
                        heading: "Teachers",
                        route: "/teachers",
                        isExternal: true,
                        hasNotAccess: true,
                    }
                ]
            },
            {
                sectionTitle: "Resources",
                route: "/resources",
                svgIcon: "media/icons/sidebar/teacher/Resources.svg",
                fontIcon: "bi-people-fill",
                sub: [
                    {
                        heading: "Teacher Resources",
                        route: "/teacherresources",
                        isExternal: true,
                    },
                ]
            },
            {
                sectionTitle: "Manage",
                route: "/manage",
                svgIcon: "media/icons/sidebar/teacher/Manage.svg",
                fontIcon: "bi-people-fill",
                sub: [
                    {
                        heading: "Lessons Manage",
                        route: "/tasks-manage",
                        isExternal: true,
                        hasNotAccess: true,
                    },
                    //   {
                    //     heading: "Lessons Responses",
                    //     route: "/student-tasks",
                    //     isExternal:true,
                    //     hasNotAccess:true,
                    //   },
                    {
                        heading: "Work Experience Manage",
                        route: "/wew/manage",
                        isExternal: true,
                        hasNotAccess: true,
                    },
                    {
                        heading: "Student Responses",
                        route: "/wew/responses",
                        isExternal: true,
                        hasNotAccess: true,
                    },
                ]
            },
            {
                sectionTitle: "Insights",
                route: "/insights",
                svgIcon: "media/icons/sidebar/teacher/insights.svg",
                fontIcon: "bi-people-fill",
                sub: [
                    {
                        heading: "Student Reports",
                        route: "/reports",
                        isExternal: true,
                        hasNotAccess: true,
                    },
                ]
            },
            {
                sectionTitle: "Support",
                route: "/insights",
                svgIcon: "media/icons/sidebar/student/support.svg",
                fontIcon: "bi-node-minus",
                sub: [
                    // {
                    //     heading: "Noticeboard",
                    //     route: "/noticeboard",
                    //     isExternal:true,
                    // },
                    {
                        heading: "FB Group",
                        route: "https://www.facebook.com/groups/991216071271987/",
                        isExternal: true,
                        inNewTab: true,
                    },
                    {
                        heading: 'Help Centre',
                        route: 'https://help.thecareersdepartment.com/en/',
                        isExternal: true,
                        inNewTab: true,
                    },
                ]
            },
        ],
    },


];

export default DocMenuConfig;
